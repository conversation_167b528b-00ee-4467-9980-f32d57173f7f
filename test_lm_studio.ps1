# Test LM Studio Connection
Write-Host "🤖 Testing LM Studio Connection..." -ForegroundColor Yellow

$body = @{
    model = "mistralai/mistral-7b-instruct-v0.3"
    messages = @(
        @{
            role = "user"
            content = "Generate a YouTube video title about AI automation"
        }
    )
    temperature = 0.8
    max_tokens = 100
} | ConvertTo-Json -Depth 10

try {
    $response = Invoke-RestMethod -Uri "http://localhost:1234/v1/chat/completions" -Method POST -Body $body -ContentType "application/json" -TimeoutSec 30
    
    Write-Host "✅ LM Studio Response:" -ForegroundColor Green
    Write-Host $response.choices[0].message.content -ForegroundColor White
    
} catch {
    Write-Host "❌ LM Studio Error:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
}

{
  "name": "LM Studio YouTube Automation System",
  "nodes": [
    {
      "parameters": {
        "rule": {
          "interval": [
            {
              "field": "hours",
              "hoursInterval": 2
            }
          ]
        }
      },
      "type": "n8n-nodes-base.scheduleTrigger",
      "typeVersion": 1.2,
      "position": [-1200, 0],
      "id": "schedule-trigger",
      "name": "🕐 Intelligent Schedule Trigger"
    },
    {
      "parameters": {
        "httpMethod": "POST",
        "path": "webhook-trigger",
        "responseMode": "responseNode",
        "options": {}
      },
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 2,
      "position": [-1200, 200],
      "id": "webhook-trigger",
      "name": "🔗 Manual Trigger Webhook"
    },
    {
      "parameters": {
        "jsCode": "// 🧠 MARKET ANALYSIS & STRATEGY THINKER NODE\nconst marketAnalyzer = {\n  async analyzeMarket() {\n    const currentTime = new Date();\n    const dayOfWeek = currentTime.getDay();\n    const hour = currentTime.getHours();\n    \n    const optimalTimes = {\n      0: [14, 18, 20], 1: [12, 17, 19], 2: [11, 16, 18],\n      3: [13, 17, 20], 4: [12, 18, 21], 5: [15, 19, 22], 6: [10, 16, 20]\n    };\n    \n    const isOptimalTime = optimalTimes[dayOfWeek].includes(hour);\n    \n    const strategies = {\n      morning: { contentType: 'educational', duration: 60, tone: 'energetic', topics: ['productivity', 'motivation', 'tutorials'] },\n      afternoon: { contentType: 'entertainment', duration: 30, tone: 'casual', topics: ['trending', 'viral', 'quick-tips'] },\n      evening: { contentType: 'storytelling', duration: 90, tone: 'engaging', topics: ['stories', 'reviews', 'deep-dive'] }\n    };\n    \n    let timeSlot = 'morning';\n    if (hour >= 12 && hour < 18) timeSlot = 'afternoon';\n    if (hour >= 18) timeSlot = 'evening';\n    \n    return {\n      isOptimalTime,\n      currentStrategy: strategies[timeSlot],\n      marketConditions: {\n        competitionLevel: Math.random() > 0.5 ? 'high' : 'moderate',\n        trendingTopics: ['AI automation', 'productivity hacks', 'viral content'],\n        recommendedDuration: strategies[timeSlot].duration\n      },\n      timestamp: currentTime.toISOString()\n    };\n  }\n};\n\nconst analysis = await marketAnalyzer.analyzeMarket();\n\nreturn {\n  json: {\n    marketAnalysis: analysis,\n    shouldProceed: analysis.isOptimalTime || $input.first().json.forceRun === true,\n    contentStrategy: analysis.currentStrategy,\n    processingId: `proc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n  }\n};"
      },
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [-900, 100],
      "id": "market-strategy-thinker",
      "name": "🧠 Market Strategy Thinker"
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
          },
          "conditions": [
            {
              "id": "should-proceed",
              "leftValue": "={{ $json.shouldProceed }}",
              "rightValue": true,
              "operator": {
                "type": "boolean",
                "operation": "equals"
              }
            }
          ],
          "combinator": "and"
        },
        "options": {}
      },
      "type": "n8n-nodes-base.if",
      "typeVersion": 2,
      "position": [-600, 100],
      "id": "timing-gate",
      "name": "⏰ Timing Gate"
    },
    {
      "parameters": {
        "jsCode": "// 🤖 LM STUDIO CONTENT GENERATOR\n// Uses LM Studio local server for content generation\n\nconst lmStudioGenerator = {\n  config: {\n    lmStudioUrl: process.env.LM_STUDIO_URL || 'http://localhost:1234',\n    maxTokens: 4000,\n    temperature: 0.8,\n    timeout: 180000\n  },\n\n  async generateContent(systemPrompt, userPrompt, options = {}) {\n    const config = { ...this.config, ...options };\n    \n    try {\n      console.log('🤖 Generating content with LM Studio...');\n      \n      const response = await fetch(`${config.lmStudioUrl}/v1/chat/completions`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          messages: [\n            { role: 'system', content: systemPrompt },\n            { role: 'user', content: userPrompt }\n          ],\n          max_tokens: config.maxTokens,\n          temperature: config.temperature,\n          stream: false\n        }),\n        signal: AbortSignal.timeout(config.timeout)\n      });\n\n      if (!response.ok) {\n        throw new Error(`LM Studio API error: ${response.status} - ${response.statusText}`);\n      }\n\n      const data = await response.json();\n      \n      if (!data.choices || !data.choices[0] || !data.choices[0].message) {\n        throw new Error('Invalid response format from LM Studio');\n      }\n\n      const content = data.choices[0].message.content;\n      \n      console.log('✅ Content generated successfully with LM Studio');\n      \n      return {\n        content: content,\n        model: data.model || 'lm-studio-model',\n        service: 'lm-studio',\n        tokens: data.usage?.total_tokens || 0,\n        processingTime: Date.now(),\n        offline: true\n      };\n    } catch (error) {\n      console.error('LM Studio generation failed:', error);\n      \n      // Fallback response\n      return {\n        content: JSON.stringify({\n          videoTitle: \"Amazing AI Content Creation Tutorial\",\n          videoDescription: \"Learn how to create amazing content with AI automation. This video shows you step-by-step how to set up your own content creation system.\",\n          videoScript: \"Welcome to this amazing tutorial on AI content creation! Today we'll explore how to automate your content workflow. Don't forget to subscribe for more AI tips!\",\n          visualCues: [\"Text overlay with title\", \"Animated graphics\", \"Call-to-action buttons\"],\n          seoTags: [\"AI\", \"automation\", \"content creation\", \"tutorial\", \"productivity\"],\n          thumbnailPrompt: \"Professional YouTube thumbnail with bold text 'AI AUTOMATION' and tech graphics\",\n          hooks: {\n            opening: \"Want to automate your content creation?\",\n            closing: \"Subscribe for more AI automation tips!\"\n          }\n        }),\n        model: 'fallback',\n        service: 'fallback',\n        tokens: 0,\n        processingTime: Date.now(),\n        offline: true,\n        isFallback: true\n      };\n    }\n  }\n};\n\n// Get input data\nconst contentData = $input.first().json;\nconst marketData = contentData.marketAnalysis || {};\n\n// Build prompts for LM Studio\nconst systemPrompt = `You are an advanced YouTube content strategist and viral content creator. Your expertise includes viral content patterns, audience engagement optimization, SEO optimization, and creative intelligence. Generate content that is highly engaging, optimized for YouTube algorithm, trend-aware and professional. Respond ONLY with valid JSON format.`;\n\nconst userPrompt = `Create a comprehensive video content plan based on these parameters:\n\nDuration: ${contentData.contentStrategy?.duration || 60} seconds\nContent Type: ${contentData.contentStrategy?.contentType || 'educational'}\nTone: ${contentData.contentStrategy?.tone || 'engaging'}\nTarget Topics: ${contentData.contentStrategy?.topics?.join(', ') || 'trending topics'}\n\nMarket Context:\n- Competition Level: ${marketData.marketConditions?.competitionLevel || 'moderate'}\n- Trending Topics: ${marketData.marketConditions?.trendingTopics?.join(', ') || 'AI, automation, productivity'}\n\nRequired JSON output format:\n{\n  \"videoTitle\": \"Compelling, SEO-optimized title with hooks (30-60 characters)\",\n  \"videoDescription\": \"Detailed description with keywords and CTAs (200+ words)\",\n  \"videoScript\": \"Complete script with timing markers and engagement hooks\",\n  \"visualCues\": [\"Array of visual elements needed for the video\"],\n  \"seoTags\": [\"Array of 10-15 optimized tags for discoverability\"],\n  \"thumbnailPrompt\": \"Detailed AI prompt for thumbnail generation\",\n  \"hooks\": {\n    \"opening\": \"Compelling first 3-second hook to grab attention\",\n    \"closing\": \"Strong CTA and subscribe prompt\"\n  }\n}\n\nMake this content EXCEPTIONAL and industry-leading!`;\n\n// Generate content using LM Studio\nconst startTime = Date.now();\nconst aiResponse = await lmStudioGenerator.generateContent(systemPrompt, userPrompt);\nconst processingTime = Date.now() - startTime;\n\n// Parse and validate the response\nlet parsedContent;\ntry {\n  // Try to extract JSON from the response\n  const jsonMatch = aiResponse.content.match(/```(?:json)?\\s*([\\s\\S]*?)```/) || [null, aiResponse.content];\n  const jsonString = jsonMatch[1] ? jsonMatch[1].trim() : aiResponse.content.trim();\n  \n  // Remove any leading/trailing text that might not be JSON\n  const cleanJsonString = jsonString.replace(/^[^{]*/, '').replace(/[^}]*$/, '');\n  \n  parsedContent = JSON.parse(cleanJsonString);\n  \n  console.log('✅ Successfully parsed AI response');\n} catch (error) {\n  console.warn('⚠️ Failed to parse AI response, using fallback content');\n  \n  // Fallback content if parsing fails\n  parsedContent = {\n    videoTitle: \"Amazing AI Content Creation with LM Studio\",\n    videoDescription: \"Discover how to create professional YouTube content using LM Studio and local AI models. This comprehensive guide shows you everything you need to know about automated content creation. Perfect for content creators, marketers, and anyone interested in AI automation. Subscribe for more AI tutorials and productivity tips!\",\n    videoScript: \"Hey everyone! Today I'm excited to show you how to create amazing content using LM Studio and local AI models. This is a game-changer for content creators who want to maintain privacy while producing high-quality videos. Let's dive in! [Continue with detailed explanation] Don't forget to like this video and subscribe for more AI automation content!\",\n    visualCues: [\"LM Studio interface screenshots\", \"AI model selection graphics\", \"Content generation process\", \"Results showcase\"],\n    seoTags: [\"LM Studio\", \"AI content creation\", \"local AI\", \"YouTube automation\", \"content strategy\", \"AI tools\", \"productivity\", \"automation\", \"tutorial\", \"tech\"],\n    thumbnailPrompt: \"Professional YouTube thumbnail featuring LM Studio logo, bold text 'LOCAL AI CONTENT', tech-style graphics with blue and purple gradients, excited person pointing at screen\",\n    hooks: {\n      opening: \"Want to create content with AI without paying monthly fees?\",\n      closing: \"Hit subscribe for more local AI tutorials and automation tips!\"\n    }\n  };\n}\n\n// Add LM Studio metadata\nparsedContent.localAI = {\n  model: aiResponse.model,\n  service: aiResponse.service,\n  processingTime,\n  tokens: aiResponse.tokens,\n  generatedAt: new Date().toISOString(),\n  offline: true,\n  isFallback: aiResponse.isFallback || false\n};\n\nreturn {\n  json: {\n    ...contentData,\n    ...parsedContent,\n    metadata: {\n      ...contentData.metadata,\n      aiGeneration: parsedContent.localAI,\n      processingId: contentData.processingId || `lms_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n    }\n  }\n};"
      },
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [-300, 100],
      "id": "lm-studio-content-generator",
      "name": "🤖 LM Studio Content Generator"
    },
    {
      "parameters": {
        "jsCode": "// 🎨 SIMPLE IMAGE PLACEHOLDER GENERATOR\n// Creates placeholder thumbnails when no image generation is available\n\nconst simpleImageGenerator = {\n  generatePlaceholder(prompt, title) {\n    // Create a simple data URL for a colored rectangle with text\n    const canvas = {\n      width: 1280,\n      height: 720,\n      createDataURL() {\n        // Simple base64 encoded 1x1 pixel image (transparent)\n        return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';\n      }\n    };\n    \n    return {\n      imageData: canvas.createDataURL().split(',')[1],\n      imageUrl: canvas.createDataURL(),\n      service: 'placeholder',\n      prompt: prompt,\n      title: title,\n      offline: true,\n      isPlaceholder: true,\n      instructions: 'Use any image editing software to create a 1280x720 thumbnail with the title: ' + title\n    };\n  }\n};\n\n// Get input data\nconst contentData = $input.first().json;\nconst thumbnailPrompt = contentData.thumbnailPrompt || 'Professional YouTube thumbnail, engaging design';\nconst videoTitle = contentData.videoTitle || 'Generated Video';\n\nconsole.log(`🎨 Creating placeholder thumbnail for: ${videoTitle}`);\n\n// Generate placeholder thumbnail\nconst startTime = Date.now();\nconst imageResult = simpleImageGenerator.generatePlaceholder(thumbnailPrompt, videoTitle);\nconst processingTime = Date.now() - startTime;\n\nconsole.log(`✅ Placeholder thumbnail created in ${processingTime}ms`);\n\nreturn {\n  json: {\n    ...contentData,\n    imageGeneration: {\n      imageUrl: imageResult.imageUrl,\n      imageData: imageResult.imageData,\n      service: imageResult.service,\n      prompt: imageResult.prompt,\n      processingTime,\n      generatedAt: new Date().toISOString(),\n      offline: true,\n      isPlaceholder: true,\n      instructions: imageResult.instructions\n    }\n  }\n};"
      },
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [0, 100],\n      "id": "simple-image-generator",
      "name": "🎨 Simple Image Generator"
    },
    {
      "parameters": {
        "jsCode": "// 🔍 QUALITY ASSURANCE SYSTEM\n// Validates content quality before publishing\n\nconst qualityAssurance = {\n  assessContentQuality(content) {\n    const scores = {\n      titleQuality: this.assessTitle(content.videoTitle),\n      scriptQuality: this.assessScript(content.videoScript),\n      seoOptimization: this.assessSEO(content.seoTags),\n      technicalSpecs: this.assessTechnical(content)\n    };\n    \n    const overallScore = Object.values(scores).reduce((sum, score) => sum + score, 0) / Object.keys(scores).length;\n    \n    return {\n      scores,\n      overallScore: Math.round(overallScore),\n      passed: overallScore >= 60, // Lower threshold for LM Studio\n      recommendations: this.generateRecommendations(scores)\n    };\n  },\n  \n  assessTitle(title) {\n    if (!title) return 0;\n    \n    let score = 50;\n    if (title.length >= 30 && title.length <= 60) score += 20;\n    if (/[!?]/.test(title)) score += 10;\n    if (/\\b(amazing|secret|viral|instant|how to)\\b/i.test(title)) score += 10;\n    if (title.split(' ').length >= 5) score += 10;\n    \n    return Math.min(100, score);\n  },\n  \n  assessScript(script) {\n    if (!script) return 0;\n    \n    let score = 50;\n    if (script.length >= 100) score += 20;\n    if (script.toLowerCase().includes('subscribe')) score += 10;\n    if (script.toLowerCase().includes('like')) score += 10;\n    if (/\\b(you|your)\\b/gi.test(script)) score += 10;\n    \n    return Math.min(100, score);\n  },\n  \n  assessSEO(tags) {\n    if (!tags || !Array.isArray(tags)) return 0;\n    \n    let score = 30;\n    if (tags.length >= 5) score += 30;\n    if (tags.length >= 10) score += 20;\n    if (tags.some(tag => tag.length > 10)) score += 20;\n    \n    return Math.min(100, score);\n  },\n  \n  assessTechnical(content) {\n    let score = 50;\n    \n    if (content.localAI && !content.localAI.isFallback) score += 20;\n    if (content.videoTitle && content.videoScript) score += 15;\n    if (content.seoTags && content.seoTags.length > 0) score += 15;\n    \n    return Math.min(100, score);\n  },\n  \n  generateRecommendations(scores) {\n    const recommendations = [];\n    \n    if (scores.titleQuality < 70) {\n      recommendations.push('Improve title with emotional hooks and optimal length (30-60 chars)');\n    }\n    if (scores.scriptQuality < 70) {\n      recommendations.push('Add more engagement elements (CTAs, direct address)');\n    }\n    if (scores.seoOptimization < 70) {\n      recommendations.push('Add more relevant tags (aim for 10+ tags)');\n    }\n    if (scores.technicalSpecs < 70) {\n      recommendations.push('Ensure all content fields are properly filled');\n    }\n    \n    return recommendations;\n  }\n};\n\nconst contentData = $input.first().json;\nconst qualityReport = qualityAssurance.assessContentQuality(contentData);\n\nconsole.log(`🔍 Quality Assessment Complete:`);\nconsole.log(`   Overall Score: ${qualityReport.overallScore}/100`);\nconsole.log(`   Status: ${qualityReport.passed ? 'PASSED' : 'NEEDS IMPROVEMENT'}`);\n\nif (qualityReport.recommendations.length > 0) {\n  console.log(`   Recommendations:`);\n  qualityReport.recommendations.forEach(rec => console.log(`   - ${rec}`));\n}\n\nreturn {\n  json: {\n    ...contentData,\n    qualityAssurance: qualityReport,\n    readyForUpload: qualityReport.passed\n  }\n};"
      },
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [300, 100],
      "id": "quality-assurance",
      "name": "🔍 Quality Assurance"
    },
    {
      "parameters": {
        "jsCode": "// 📊 FINAL ANALYTICS & SUCCESS NOTIFICATION\n// Generates final analytics and success message\n\nconst finalProcessor = {\n  generateFinalReport(content) {\n    const timestamp = new Date().toISOString();\n    \n    return {\n      completedAt: timestamp,\n      contentSummary: {\n        title: content.videoTitle || 'Generated Content',\n        duration: content.contentStrategy?.duration || 60,\n        qualityScore: content.qualityAssurance?.overallScore || 0,\n        aiModel: content.localAI?.model || 'unknown',\n        processingTime: content.localAI?.processingTime || 0,\n        service: content.localAI?.service || 'unknown'\n      },\n      predictions: {\n        expectedViews: this.predictViews(content),\n        expectedEngagement: this.predictEngagement(content),\n        viralPotential: this.assessViralPotential(content)\n      },\n      technicalDetails: {\n        generatedOffline: content.localAI?.offline || false,\n        imageService: content.imageGeneration?.service || 'none',\n        processingId: content.processingId || 'unknown',\n        isFallback: content.localAI?.isFallback || false\n      },\n      status: content.readyForUpload ? 'READY_FOR_UPLOAD' : 'NEEDS_IMPROVEMENT'\n    };\n  },\n  \n  predictViews(content) {\n    let baseViews = 1000;\n    \n    if (content.qualityAssurance?.overallScore > 80) baseViews *= 2;\n    if (content.contentStrategy?.contentType === 'viral') baseViews *= 1.5;\n    if (content.contentStrategy?.duration <= 30) baseViews *= 1.3;\n    if (content.localAI?.service === 'lm-studio') baseViews *= 1.2; // Bonus for local AI\n    \n    return Math.round(baseViews * (0.8 + Math.random() * 0.4));\n  },\n  \n  predictEngagement(content) {\n    let baseEngagement = 50;\n    \n    if (content.qualityAssurance?.overallScore > 80) baseEngagement += 20;\n    if (content.videoScript?.toLowerCase().includes('subscribe')) baseEngagement += 10;\n    if (content.videoScript?.toLowerCase().includes('comment')) baseEngagement += 10;\n    if (content.seoTags?.length >= 10) baseEngagement += 10;\n    \n    return Math.min(100, baseEngagement);\n  },\n  \n  assessViralPotential(content) {\n    let viralScore = 5;\n    \n    if (content.contentStrategy?.contentType === 'viral') viralScore += 2;\n    if (content.qualityAssurance?.overallScore > 85) viralScore += 2;\n    if (content.contentStrategy?.duration <= 30) viralScore += 1;\n    \n    return Math.min(10, viralScore);\n  },\n  \n  generateSuccessMessage(report) {\n    const emoji = report.status === 'READY_FOR_UPLOAD' ? '🎉' : '⚠️';\n    const statusText = report.status === 'READY_FOR_UPLOAD' ? 'SUCCESS' : 'NEEDS WORK';\n    \n    return `${emoji} LM Studio YouTube Content Generated - ${statusText}!\n\n📊 **Content Summary:**\n• Title: ${report.contentSummary.title}\n• Duration: ${report.contentSummary.duration}s\n• Quality Score: ${report.contentSummary.qualityScore}/100\n• AI Model: ${report.contentSummary.aiModel}\n• Processing Time: ${report.contentSummary.processingTime}ms\n\n🎯 **Predictions:**\n• Expected Views: ${report.predictions.expectedViews.toLocaleString()}\n• Engagement Score: ${report.predictions.expectedEngagement}%\n• Viral Potential: ${report.predictions.viralPotential}/10\n\n🔧 **Technical Details:**\n• Generated Completely Offline: ${report.technicalDetails.generatedOffline ? '✅' : '❌'}\n• Image Service: ${report.technicalDetails.imageService}\n• Processing ID: ${report.technicalDetails.processingId}\n• Using Fallback: ${report.technicalDetails.isFallback ? '⚠️ Yes' : '✅ No'}\n\n${report.status === 'READY_FOR_UPLOAD' ? '🚀 Ready for YouTube upload!' : '🔧 Please review and improve content before uploading.'}`;\n  }\n};\n\nconst contentData = $input.first().json;\nconst finalReport = finalProcessor.generateFinalReport(contentData);\nconst successMessage = finalProcessor.generateSuccessMessage(finalReport);\n\nconsole.log('📊 Final Report Generated:');\nconsole.log(successMessage);\n\nreturn {\n  json: {\n    ...contentData,\n    finalReport,\n    successMessage,\n    finalStatus: finalReport.status,\n    completedAt: finalReport.completedAt\n  }\n};"
      },
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [600, 100],
      "id": "final-processor",
      "name": "📊 Final Processor"
    }
  ],
  "connections": {
    "🕐 Intelligent Schedule Trigger": {
      "main": [
        [
          {
            "node": "🧠 Market Strategy Thinker",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "🔗 Manual Trigger Webhook": {
      "main": [
        [
          {
            "node": "🧠 Market Strategy Thinker",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "🧠 Market Strategy Thinker": {
      "main": [
        [
          {
            "node": "⏰ Timing Gate",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "⏰ Timing Gate": {
      "main": [
        [
          {
            "node": "🤖 LM Studio Content Generator",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "🤖 LM Studio Content Generator": {
      "main": [
        [
          {
            "node": "🎨 Simple Image Generator",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "🎨 Simple Image Generator": {
      "main": [
        [
          {
            "node": "🔍 Quality Assurance",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "🔍 Quality Assurance": {
      "main": [
        [
          {
            "node": "📊 Final Processor",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "pinData": {},
  "settings": {
    "executionOrder": "v1"
  },
  "staticData": null,
  "tags": [
    {
      "createdAt": "2024-07-14T00:00:00.000Z",
      "updatedAt": "2024-07-14T00:00:00.000Z",
      "id": "lm-studio-automation",
      "name": "LM Studio Automation"
    }
  ],
  "triggerCount": 2,
  "updatedAt": "2024-07-14T00:00:00.000Z",
  "versionId": "2.0.0-lm-studio"
}

# 🏠 Local AI Setup Guide - Complete Offline YouTube Automation

## 🌟 Overview

This guide will help you set up a **completely self-hosted, offline-first YouTube automation system** that uses local AI models instead of external APIs. This system provides:

- **🔒 Complete Privacy**: All AI processing happens locally
- **💰 Zero Ongoing Costs**: No API fees or subscriptions
- **⚡ High Performance**: Optimized for local hardware
- **🌐 Offline Operation**: Works without internet (except for YouTube uploads)
- **🎯 Professional Quality**: Industry-standard results

## 🖥️ Hardware Requirements

### 🏆 **Recommended (High-End)**
- **CPU**: 8+ cores (Intel i7/i9, AMD Ryzen 7/9)
- **RAM**: 32GB+ DDR4/DDR5
- **GPU**: NVIDIA RTX 4080/4090 (16GB+ VRAM) or RTX 3080/3090
- **Storage**: 500GB+ NVMe SSD
- **Network**: Gigabit Ethernet (for initial model downloads)

### ✅ **Minimum (Budget)**
- **CPU**: 4+ cores (Intel i5, AMD Ryzen 5)
- **RAM**: 16GB DDR4
- **GPU**: NVIDIA RTX 3060 (8GB+ VRAM) or GTX 1660 Ti
- **Storage**: 200GB+ SSD
- **Network**: 100Mbps+ (for model downloads)

### ⚠️ **Absolute Minimum**
- **CPU**: 4 cores
- **RAM**: 8GB
- **GPU**: Any NVIDIA GPU with 4GB+ VRAM (or CPU-only)
- **Storage**: 100GB+ available space

## 🚀 Quick Start Installation

### Step 1: Clone and Setup
```bash
# Clone the repository
git clone https://github.com/your-repo/local-youtube-automation
cd local-youtube-automation

# Make setup script executable
chmod +x setup-local-ai.sh

# Run automated setup
./setup-local-ai.sh
```

### Step 2: Start the System
```bash
# Start all services with Docker Compose
docker-compose -f docker-compose.local-ai.yml up -d

# Check service status
docker-compose -f docker-compose.local-ai.yml ps
```

### Step 3: Install AI Models
```bash
# Install recommended models based on your hardware
./install-models.sh --profile auto

# Or manually specify your hardware profile
./install-models.sh --profile high-end    # 32GB+ RAM
./install-models.sh --profile mid-range   # 16GB RAM
./install-models.sh --profile budget      # 8GB RAM
```

## 📦 Detailed Installation Steps

### 1. Install Docker and Dependencies

#### Ubuntu/Debian:
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Install NVIDIA Container Toolkit (for GPU support)
distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | sudo tee /etc/apt/sources.list.d/nvidia-docker.list

sudo apt-get update && sudo apt-get install -y nvidia-docker2
sudo systemctl restart docker
```

#### Windows (with WSL2):
```powershell
# Install WSL2 and Ubuntu
wsl --install -d Ubuntu

# Install Docker Desktop with WSL2 backend
# Download from: https://www.docker.com/products/docker-desktop

# Install NVIDIA CUDA on WSL2
# Follow: https://docs.nvidia.com/cuda/wsl-user-guide/index.html
```

#### macOS:
```bash
# Install Docker Desktop
# Download from: https://www.docker.com/products/docker-desktop

# Install Homebrew (if not installed)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install required tools
brew install wget curl git
```

### 2. Configure Environment Variables

Create `.env` file:
```bash
# Copy template
cp .env.template .env

# Edit configuration
nano .env
```

Example `.env` configuration:
```bash
# System Configuration
N8N_PASSWORD=your-secure-password-123
POSTGRES_PASSWORD=postgres-secure-password
GRAFANA_PASSWORD=grafana-admin-password

# Hardware Profile (auto, high-end, mid-range, budget)
HARDWARE_PROFILE=auto

# Model Preferences
PREFERRED_LLM=qwen2.5:32b
FALLBACK_LLM=mistral:7b-instruct
PREFERRED_SD_MODEL=sd_xl_base_1.0.safetensors

# Service URLs (usually don't need to change)
OLLAMA_URL=http://ollama:11434
LOCALAI_URL=http://localai:8080
STABLE_DIFFUSION_URL=http://automatic1111:7860
COMFYUI_URL=http://comfyui:8188

# YouTube API (still needed for uploads)
YOUTUBE_CLIENT_ID=your-youtube-client-id
YOUTUBE_CLIENT_SECRET=your-youtube-client-secret

# Google Sheets API (for analytics)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
ANALYTICS_SPREADSHEET_ID=your-spreadsheet-id

# Optional: Slack notifications
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
```

### 3. Start Local AI Services

```bash
# Start all services
docker-compose -f docker-compose.local-ai.yml up -d

# Monitor startup logs
docker-compose -f docker-compose.local-ai.yml logs -f

# Check service health
curl http://localhost:11434/api/tags        # Ollama
curl http://localhost:7860/sdapi/v1/sd-models  # Stable Diffusion
curl http://localhost:5678/healthz         # N8N
curl http://localhost:9000/health          # Model Manager
```

### 4. Install AI Models

#### Automatic Installation (Recommended):
```bash
# Auto-detect hardware and install optimal models
./scripts/install-models.sh --auto

# This will:
# 1. Detect your hardware capabilities
# 2. Download appropriate models
# 3. Configure optimal settings
# 4. Test model functionality
```

#### Manual Model Installation:

**For Ollama (Language Models):**
```bash
# High-end systems (32GB+ RAM)
docker exec youtube-automation-ollama ollama pull qwen2.5:32b
docker exec youtube-automation-ollama ollama pull llama3.2:70b

# Mid-range systems (16GB RAM)
docker exec youtube-automation-ollama ollama pull qwen2.5:32b
docker exec youtube-automation-ollama ollama pull mistral:7b-instruct

# Budget systems (8GB RAM)
docker exec youtube-automation-ollama ollama pull phi3:4b
docker exec youtube-automation-ollama ollama pull gemma2:9b
```

**For Stable Diffusion (Image Models):**
```bash
# Download models to the correct directory
mkdir -p ./sd-models/Stable-diffusion

# SDXL Base (recommended for high quality)
wget -O ./sd-models/Stable-diffusion/sd_xl_base_1.0.safetensors \
  "https://huggingface.co/stabilityai/stable-diffusion-xl-base-1.0/resolve/main/sd_xl_base_1.0.safetensors"

# Deliberate v2 (good balance of quality and speed)
wget -O ./sd-models/Stable-diffusion/deliberate_v2.safetensors \
  "https://huggingface.co/XpucT/Deliberate/resolve/main/Deliberate_v2.safetensors"

# Restart Stable Diffusion to load new models
docker-compose -f docker-compose.local-ai.yml restart automatic1111
```

### 5. Import N8N Workflow

```bash
# Access N8N interface
open http://localhost:5678

# Login with credentials from .env file
# Username: admin
# Password: [N8N_PASSWORD from .env]

# Import the local AI workflow
# 1. Go to Workflows
# 2. Click "Import from File"
# 3. Select "advanced-youtube-automation-workflow.json"
# 4. Click "Import"
```

### 6. Configure YouTube API (Still Required)

Even though we're using local AI, we still need YouTube API for uploads:

```bash
# 1. Go to Google Cloud Console
# 2. Create new project or select existing
# 3. Enable YouTube Data API v3
# 4. Create OAuth 2.0 credentials
# 5. Add authorized redirect URI: http://localhost:5678/rest/oauth2-credential/callback
# 6. Add credentials to N8N
```

## 🎛️ System Configuration

### Hardware Optimization

#### GPU Configuration:
```bash
# Check GPU availability
nvidia-smi

# Configure GPU memory growth (add to docker-compose.yml)
environment:
  - NVIDIA_VISIBLE_DEVICES=all
  - NVIDIA_DRIVER_CAPABILITIES=compute,utility
```

#### Memory Optimization:
```bash
# For systems with limited RAM, enable swap
sudo fallocate -l 8G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile

# Make permanent
echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab
```

#### Storage Optimization:
```bash
# Use SSD for model storage
# Mount SSD to ./models directory
sudo mount /dev/nvme0n1p1 ./models

# Configure automatic cleanup
echo "0 2 * * * docker system prune -f" | crontab -
```

### Model Configuration

#### Ollama Settings:
```bash
# Configure Ollama for your hardware
# Edit: ./ollama-config/config.json
{
  "num_ctx": 4096,        # Context size (adjust based on RAM)
  "num_gpu": -1,          # Use all GPU layers (-1) or specify number
  "num_thread": 8,        # CPU threads (match your CPU cores)
  "repeat_penalty": 1.1,
  "temperature": 0.8,
  "top_p": 0.9
}
```

#### Stable Diffusion Settings:
```bash
# Configure Automatic1111
# Edit: ./sd-config/config.json
{
  "samples_save": false,
  "grid_save": false,
  "return_grid": false,
  "enable_pnginfo": false,
  "save_images_before_face_restoration": false,
  "save_images_before_highres_fix": false,
  "save_images_before_color_correction": false,
  "jpeg_quality": 95,
  "export_for_4chan": false,
  "use_original_name_batch": false,
  "save_selected_only": true,
  "do_not_add_watermark": true
}
```

## 🔧 Troubleshooting

### Common Issues

#### 1. Models Not Loading
```bash
# Check available disk space
df -h

# Check model files
ls -la ./models/

# Restart Ollama
docker-compose -f docker-compose.local-ai.yml restart ollama

# Check Ollama logs
docker-compose -f docker-compose.local-ai.yml logs ollama
```

#### 2. GPU Not Detected
```bash
# Check NVIDIA drivers
nvidia-smi

# Check Docker GPU support
docker run --rm --gpus all nvidia/cuda:11.0-base nvidia-smi

# Restart Docker with GPU support
sudo systemctl restart docker
```

#### 3. Out of Memory Errors
```bash
# Check memory usage
free -h

# Reduce model size or context length
# Edit model configuration to use smaller models

# Enable CPU offloading for Stable Diffusion
# Add to docker-compose.yml:
environment:
  - COMMANDLINE_ARGS=--lowvram --precision full --no-half
```

#### 4. Slow Generation Times
```bash
# Check system resources
htop

# Optimize model settings
# Reduce steps, context size, or use smaller models

# Enable optimizations
# For Stable Diffusion: --xformers --opt-split-attention
# For Ollama: increase num_gpu layers
```

### Performance Monitoring

#### System Monitoring:
```bash
# Access Grafana dashboard
open http://localhost:3000

# Login: admin / [GRAFANA_PASSWORD from .env]

# Monitor:
# - GPU utilization
# - Memory usage
# - Generation times
# - Success rates
```

#### Model Manager API:
```bash
# Check system status
curl http://localhost:9000/api/status

# Get recommendations
curl http://localhost:9000/api/recommendations

# View installed models
curl http://localhost:9000/api/models
```

## 📊 Performance Optimization

### Model Selection by Hardware

#### High-End Systems (32GB+ RAM, RTX 4080+):
```json
{
  "llm": "qwen2.5:32b",
  "fallback": ["llama3.2:70b", "mistral:7b-instruct"],
  "sd_model": "sd_xl_base_1.0.safetensors",
  "settings": {
    "context_size": 8192,
    "gpu_layers": -1,
    "sd_steps": 30
  }
}
```

#### Mid-Range Systems (16GB RAM, RTX 3070+):
```json
{
  "llm": "qwen2.5:32b",
  "fallback": ["mistral:7b-instruct", "phi3:4b"],
  "sd_model": "deliberate_v2.safetensors",
  "settings": {
    "context_size": 4096,
    "gpu_layers": 25,
    "sd_steps": 25
  }
}
```

#### Budget Systems (8GB RAM, GTX 1660+):
```json
{
  "llm": "phi3:4b",
  "fallback": ["gemma2:9b"],
  "sd_model": "dreamshaper_8.safetensors",
  "settings": {
    "context_size": 2048,
    "gpu_layers": 15,
    "sd_steps": 20
  }
}
```

### Optimization Tips

1. **Use SSD Storage**: Significantly faster model loading
2. **Enable GPU Acceleration**: 10-50x faster than CPU-only
3. **Optimize Context Size**: Balance quality vs. memory usage
4. **Use Model Quantization**: Reduce memory usage with minimal quality loss
5. **Enable Caching**: Cache successful generations for reuse
6. **Monitor Resources**: Use Grafana dashboard for optimization insights

## 🎯 Next Steps

1. **Test the System**: Run the test suite to validate everything works
2. **Customize Models**: Fine-tune models for your specific content niche
3. **Optimize Performance**: Use monitoring data to optimize settings
4. **Scale Up**: Add more GPU power or distribute across multiple machines
5. **Backup Models**: Create backups of your trained/fine-tuned models

## 🆘 Support

### Getting Help
- **Documentation**: Refer to this comprehensive guide
- **Logs**: Check Docker logs for detailed error information
- **Monitoring**: Use Grafana dashboard for system insights
- **Community**: Join our Discord/Slack for community support

### Reporting Issues
1. **Check Logs**: Gather relevant log information
2. **System Info**: Include hardware specifications
3. **Configuration**: Share relevant configuration files
4. **Steps to Reproduce**: Provide detailed reproduction steps

---

## 🎉 Congratulations!

You now have a **completely self-hosted, offline-first YouTube automation system** that:

- ✅ **Generates high-quality content** using local AI models
- ✅ **Creates professional thumbnails** with local Stable Diffusion
- ✅ **Works completely offline** (except for YouTube uploads)
- ✅ **Costs nothing to operate** after initial setup
- ✅ **Provides complete privacy** with local processing
- ✅ **Scales with your hardware** for optimal performance

**Welcome to the future of private, cost-free AI automation!** 🚀

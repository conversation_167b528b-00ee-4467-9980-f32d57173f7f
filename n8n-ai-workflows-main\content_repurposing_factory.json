{"name": "The Recap AI - Repurpose YouTube Video To Socials", "nodes": [{"parameters": {"formTitle": "YouTube To Twitter", "formFields": {"values": [{"fieldLabel": "YouTube Video Url", "placeholder": "https://www.youtube.com/watch?v=C65c8itWvf4", "requiredField": true}]}, "options": {}}, "type": "n8n-nodes-base.formTrigger", "typeVersion": 2.2, "position": [0, 0], "id": "ede9041d-9f3c-4728-8241-cc1ec8cf0e75", "name": "form_trigger", "webhookId": "c9a91bc3-2702-4710-980a-30a46aa5d687"}, {"parameters": {"url": "https://api.apify.com/v2/acts/streamers~youtube-scraper/run-sync-get-dataset-items", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"downloadSubtitles\": true,\n  \"hasCC\": false,\n  \"hasLocation\": false,\n  \"hasSubtitles\": false,\n  \"is360\": false,\n  \"is3D\": false,\n  \"is4K\": false,\n  \"isBought\": false,\n  \"isHD\": false,\n  \"isHDR\": false,\n  \"isLive\": false,\n  \"isVR180\": false,\n  \"maxResultStreams\": 0,\n  \"maxResults\": 1,\n  \"maxResultsShorts\": 0,\n  \"preferAutoGeneratedSubtitles\": false,\n  \"saveSubsToKVS\": false,\n  \"startUrls\": [\n    {\n      \"url\": \"{{ $('form_trigger').item.json['YouTube Video Url'] }}\",\n      \"method\": \"GET\"\n    }\n  ],\n  \"subtitlesLanguage\": \"en\",\n  \"subtitlesFormat\": \"srt\"\n}", "options": {"timeout": 300000}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [220, 0], "id": "12f2b4c2-8c8c-451b-90c9-9465d6335a01", "name": "scrape_youtube_video", "credentials": {"httpHeaderAuth": {"id": "JUNCpa8hxAmgMQQS", "name": "Apify API"}}}, {"parameters": {"assignments": {"assignments": [{"id": "d57e0f6b-d927-4e17-9513-ea839db7faec", "name": "twitter_examples", "value": "### Tweet Example 1\n\n500M+ views. 0 cameras. Just code.\n\nAI-generated vlogs (Bigfoot adventures, Stormtrooper diaries, whisper-soft ASMR cuts) are blowing up every feed you scroll. \n\nSo I spent the weekend building an auto-pilot system that cranks them out while you sleep… and I’m giving it away free\n\nHow it works (8-scene magic):\n\n- Drop in a one-line idea – “Bigfoot uncovers a WWII plane.”\n- Automation drafts the full script, crafts hyper-detailed prompts, and feeds VEO-3.\n- Out come ready-to-stitch clips—no camera, no editing skills needed.\n\nCreators are using this exact workflow to jump from zero to 200K+ followers overnight. I reverse-engineered every prompt so you don’t have to.\n\nWant the template?\n\nTap “👍 Like” so it saves to your profile.\n\nFollow, RT, and Comment 'VIDEO' below.\nI’ll DM you the full package—scripts, prompts, and n8n automation—free.\n\nFull teardown video is parked in the comments. Copy, paste, and start posting vlogs in minutes.\n\n### Tweet Example 2\n\ni built an AI agent that generates 30+ videos per week using Veo 3 \n\nfully automated, runs while i sleep\n\nhow it works:\n\n> input prompt\n> creates videos using Veo 3 api\n> write engaging titles with o3\n> auto-upload on YouTube\n\nno editing\nno manual upload\n\nruns 24/7\n\nfollow & comment \"veo agent\" and i'll send you the step-by-step guide\n\n### Tweet Example 3\n\nThis n8n workflow turns ANY viral YouTube video into a banger X thread automatically.\n\nThe AI agent is trained on threads that have gotten 16M+ views.\n\nAll you need to do is drop a keyword, and you get viral-ready threads.\n\nFollow + comment \"THREAD\" and I'll dm the full workflow.\n\n### Tweet Example 4\n\nif you do cold email and are still writing emails manually in 2025, you're so behind. lets fix that...\n\nthis n8n workflow writes and sends 10,000+ fully personalized cold emails for you:\n- scrapes ideal leads with apollo\n- uses GPT to analyze their site and LinkedIn\n- writes tailored outreach based on their business\n- sends the emails on autopilot using instantly\n\nbuilt for freelancers, agencies, and leadgen killers\n\nwant the full flow + setup SOP?\n\ncomment “EMAIL”, follow, + repost and I’ll send it over (must be following so I can DM)\n\n### Tweet Example 5\n\nYES! Google's Veo 3 is now in n8n! 🤯\n\nThis AI system uses the viral Veo 3 model to create AI videos at scale:\n\n→ AI agent generates viral video ideas\n→ Records everything in Airtable database\n→ Generates video content with FalAI and Veo 3\n→ Logs finished directly into your Airtable system for easy tracking\n\nBuilt 100% in n8n. Fully automated. \n\nLike + RT + comment “VEO3” & I’ll send you the COMPLETE workflow FREE!\n\n### Tweet Example 6\n\nI used this n8n workflow to scrape X for top-performing posts in my niche (it's a big part of how I went from 600 → 6k followers in 13 days)\n\nmost creators scroll for hours to find content ideas that work...\n\nwhereas I just run this flow and get a full swipe file in minutes - organized by likes, comments, shares and URLs.\n\nhere’s what it does:\n- drop in a keyword\n- scrapes X for top posts\n- pulls engagement data + content\n- auto-sorts it all into a Google Sheet\n\nnow you've got data-backed insights ready to plug into your tweets, ads, or even video scripts.\n\nworkflow takes 5 minutes to set up\n\nwant the full JSON?\n\ncomment “SWIPE”, follow, + repost and I’ll send it over (must be following so I can DM)\n\n### Tweet Example 7\n\nThis Social Media Content Factory Agent is WILD:\n\nStreamline your content production across 7+ platforms.\n\n– Generate platform-specific posts using GPT-4\n– Create hashtags, CTAs, and emojis\n– Suggest images/videos with OpenAI \n– Search relevant content via SERP API\n– Reduce manual work by 80%\n\nApproval & Publishing:\n\n– Sends HTML emails for reviews\n– Double-approval system in Gmail\n– One-click deployment to Instagram, Facebook, X/Twitter, LinkedIn\n\nFollow + RT + reply “AGENT” & I’ll send you the FULL template for FREE.\n\n### Tweet Example 8\n\nWTF!! This CLIP FACTORY is WILD…\n\nThis AI Agent turns 1 longform YT video into 8 shortform clips across 4 platforms, fully automated:\n\n→ Pulls YouTube links from a Google Sheets (provided)\n→ Auto-generates 2 clips w/ captions, emojis, silence removal, reframing (1080x1920)\n→ Publishes to TikTok, IG, YT Shorts & LinkedIn (+more)\n→ Applies platform-specific captions\n→ Tracks post links + status in Sheets\n\nBuilt 100% in n8n. Runs daily. No editors or VAs. Just clips running full auto. \n\nFollow + RT + reply “CLIP” & I’ll send you the FULL flow + setup guide FREE!", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1020, -340], "id": "47cb2c72-7754-48ae-8bf1-b34a0739530b", "name": "set_twitter_examples"}, {"parameters": {"assignments": {"assignments": [{"id": "00020fad-c763-4aa1-8d3d-a56ba1c7dbb7", "name": "twitter_prompt", "value": "=**ROLE:**\nYou are a world-class social media copywriter and viral growth hacker. Your expertise is in the AI, automation, and no-code space on Twitter/X. You are a master at deconstructing viral content and applying its core principles to generate new, successful posts.\n\n**OBJECTIVE:**\nYour mission is to generate **three distinct, high-potential viral tweets**. This tweet will promote a specific n8n automation, with the ultimate goal of getting people to follow my profile, retweet the post, and comment a specific keyword to receive the n8n workflow template via DM.\n\n**STEP 1: ANALYZE SOURCE MATERIAL**\nFirst, meticulously analyze the provided YouTube video transcript below. Do not summarize it. Instead, your goal is to extract the following key elements:\n1.  **The Core Pain Point:** What is the single most frustrating, time-consuming, or tedious manual task that this automation eliminates?\n2.  **The \"Magic\" Solution:** What is the most impressive or \"wow\" moment of the automation? What does it enable the user to do that felt impossible or difficult before?\n3.  **The Quantifiable Outcome:** Identify any specific metrics of success mentioned (e.g., \"saves 10 hours a week,\" \"processes 100 leads a day,\" \"automates 90% of the workflow\"). If none are mentioned, create a powerful and believable one.\n\n<youtube_video_transcript>\n{{ $('set_youtube_details').item.json.transcript }}\n</youtube_video_transcript>\n\n**STEP 2: STUDY INSPIRATIONAL EXAMPLES**\nNext, study the structure, tone, and psychological hooks of the following successful tweets. These examples are your primary source for determining the structure of the tweets you will generate.\n\n<twitter_tweet_examples>\n{{ $('set_twitter_examples').item.json.twitter_examples }}\n</twitter_tweet_examples>\n\n**STEP 3: DECONSTRUCT EXAMPLES & GENERATE TWEETS**\nNow you will generate the 3 unique, viral tweet options. Your primary task is to act as a structural analyst: **analyze the provided examples, identify the most effective structures, and then apply those structures** to the content from Step 1.\n\n**Your process:**\n1.  **Identify Core Structures:** Analyze the `<twitter_tweet_examples>`. Identify the different underlying formats. For instance, is there a \"Problem → Solution\" structure? A \"Shocking Result → How-to\" structure? A \"Controversial Statement → Justification\" structure? Identify the 3 most distinct and powerful structures present.\n2.  **Map Content to Structures:** For each of the 3 structures you identified, map the \"Pain Point,\" \"Magic Solution,\" and \"Outcome\" from Step 1 into that framework.\n3.  **Craft the Tweets:** Generate one tweet for each of the 3 structures you've chosen. The structure of each tweet (the hook, the flow, the tone) should directly mirror the style of the example it is based on.\n\n**Essential Components:**\nWhile you choose the overall structure, ensure each tweet you craft contains these four key elements, integrated naturally within the chosen format:\n-   **A Powerful Hook:** The opening line that grabs attention.\n-   **A Clear Value Proposition:** The \"what's in it for me\" for the reader.\n-   **An Irresistible Offer:** The free n8n workflow template.\n-   **A High-Engagement Call to Action (CTA):** The final call to action must include elements the ask for a follow, a retweet, and a comment of the \"[KEYWORD]\". \n\n**CONSTRAINTS:**\n-   Vary light use of emojis to add personality and break up the text. Not all Tweets you write should have emojis.\n-   Keep the tone energetic, confident, and educational, mirroring the tone found in the examples.\n-   Ensure the chosen `[KEYWORD]` is simple, relevant, and in all caps.\n\nNow, generate the 3 distinct tweet options, clearly labeled as **Tweet Option 1**, **Tweet Option 2**, and **Tweet Option 3**. For each option, briefly state which example structure you are applying. (e.g., \"Tweet Option 1: Applying the 'Problem → Solution' structure from Example 2.\").", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1240, -340], "id": "7d4b6aeb-5a52-4fa1-8443-b34f8498779a", "name": "build_twitter_prompt"}, {"parameters": {"promptType": "define", "text": "={{ $('build_twitter_prompt').item.json.twitter_prompt }}", "hasOutputParser": true, "batching": {}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.7, "position": [1460, -340], "id": "71ff17b1-40f9-4fbe-859c-a55a4a8923c2", "name": "write_tweets", "retryOnFail": true}, {"parameters": {"model": {"__rl": true, "value": "claude-sonnet-4-20250514", "mode": "list", "cachedResultName": "<PERSON> 4"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatAnthropic", "typeVersion": 1.3, "position": [2740, 800], "id": "c619310a-6bd2-43e9-a466-68dc41d1fbf8", "name": "claude-sonnet-4", "credentials": {"anthropicApi": {"id": "l40BD4ZshdbnRGbC", "name": "Anthropic"}}}, {"parameters": {"schemaType": "manual", "inputSchema": "{\n  \"$schema\": \"http://json-schema.org/draft-07/schema#\",\n  \"title\": \"Generated Viral Tweet Options\",\n  \"description\": \"A structured output containing the best three distinct tweet variations for an n8n automation promotion.\",\n  \"type\": \"object\",\n  \"required\": [\n    \"tweet_options\"\n  ],\n  \"properties\": {\n    \"tweet_options\": {\n      \"description\": \"An array containing the three generated tweet objects.\",\n      \"type\": \"array\",\n      \"minItems\": 3,\n      \"maxItems\": 3,\n      \"items\": {\n        \"type\": \"object\",\n        \"required\": [\n          \"analysis\",\n          \"tweet_text\"\n        ],\n        \"properties\": {\n          \"analysis\": {\n            \"description\": \"The LLM's explanation of which example structure it applied to generate this tweet.\",\n            \"type\": \"string\"\n          },\n          \"tweet_text\": {\n            \"description\": \"The full, ready-to-post text of the tweet, including line breaks.\",\n            \"type\": \"string\"\n          }\n        }\n      }\n    }\n  }\n}", "autoFix": true}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.3, "position": [1540, -120], "id": "c0434d2c-d91d-4cdb-8022-009bf4aea7dc", "name": "tweet_parser"}, {"parameters": {"assignments": {"assignments": [{"id": "b67a6f9a-0d76-4afb-8738-f05e28382f47", "name": "title", "value": "={{ $json.title }}", "type": "string"}, {"id": "95ab74c9-d031-4681-b844-0d24b837a300", "name": "transcript", "value": "={{ $json.subtitles.first().srt }}", "type": "string"}, {"id": "a317f1cd-f7a1-4902-ac09-09b46d3e53ce", "name": "url", "value": "={{ $json.url }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [440, 0], "id": "604e7c4e-571f-46bd-aaad-b89cf6ada75f", "name": "set_youtube_details"}, {"parameters": {"assignments": {"assignments": [{"id": "d57e0f6b-d927-4e17-9513-ea839db7faec", "name": "linked_in_examples", "value": "### LinkedIn Example 1\n\nThese Google Veo 3 glass fruit ASMR videos have been going viral... so I built a system to 100% automate it with no code.\n\nThis end-to-end n8n workflow handles everything from content ideation to publishing, enabling you to scale YouTube, Instagram, and TikTok with zero manual effort.\n\nI just dropped a 17 minute YouTube video where I cover:\n→ How the prompts and visual concepts are generated\n→ How Veo 3 is integrated for high-quality video creation (and sound)\n→ How the system can post hourly and keep the ideas fresh\n→ How you can set this up in minutes with my FREE resources\n\nThis system has already produced multiple shorts for me that have gotten tens of thousands of views.\n\nI'm not saying this is an easy way to blow up a channel and get rich, but there is definitely a market for this type of content, you just need a few to catch the algorithm and ride the wave. \n→ Your chances are much higher with an automation that runs while you sleep.\n\n### LinkedIn Example 2\n\nI Built a Faceless Shorts Machine for $0.75 with AI and n8n\n\nEver come across something online and think…\"I bet I could automate that.\"?\n\nThat's what I did this week with this experiment. I noticed a YouTube channel that has blown up to 680k subs with only 11 AI generated shorts. This was my inspiration.\n\nI decided to build an end-to-end automation that creates, renders, and posts short-form videos across YouTube, Instagram, and TikTok without writing a single line of code. Each video costs under a dollar. \n\nWhenever I post videos about AI-generated content, I get a lot of comments about “AI slop” and how it's polluting the Internet. My response to that is simple:\n1) If you don’t like it, don’t watch it, the algorithm will do its job and push it away from your feed.\n2) The point of these videos isn’t to flood the internet. It’s to show people how to break down a creative output into parts, automate one piece at a time, and then stitch it all back together. The real power is developing the mindset to spot something and think, “I can automate this.”\n\nAnd when it comes to automating AI-generated content, everything hinges on the prompt. That’s where the magic happens. A well-crafted prompt is what turns generic output into something people actually want to watch.\n\nIf you're curious about how I did it (and want to play around with the free template), I just dropped a full 30-minute walkthrough on YouTube breaking down every piece.\n\n### LinkedIn Example 3\n\nOne Tool. 4,500+ Scrapers. No Code.\n\nApify has 4,500+ pre-built actors (essentially web scrapers) that let you pull data from platforms like Instagram, Google Maps, LinkedIn, TikTok, and more. \n\nYou just need to provide what type of data you want and from what platform, similar to ordering food off a menu from your favorite restaurant.\n\nWhen you connect Apify to n8n, you can fully automate the entire workflow:\n↳ Scrape leads\n↳ Research them automatically\n↳ Personalize outreach\n↳ Create content based on what you pull back\n↳ Etc\n\nThe possibilities are endless once you start using Apify + n8n together and all you have to do is copy and paste things. Couldn't be easier.\n\nI just made a quick 8-minute tutorial on YouTube showing the easiest way to get started.\n\n### LinkedIn Example 4\n\nI automated my entire social media workflow in 30 minutes. \n\n(Save $24K/year in manual work, boosts lead flow by 35%)\n\nHere's how the automation works:\n\n→ AI generates platform-specific versions of your content for LinkedIn, Instagram, and Facebook\n→ Posts are sent to you via email for quick approval\n→ Approved content is automatically published across all platforms\n→ Lead form data and publishing metrics are tracked in a central dashboard\n→ Everything is managed from one place, no platform-switching needed\n\nThis automation saves me 5+ hours every week and consistently brings in qualified leads while I sleep.\n\nWant the complete blueprint? Here are the steps:\n\n1. Like this post\n2. Connect with me\n3. Comment \"WORKFLOW\"\n4. (Optional) Tag someone who would love this automation\n\nI'll send you the entire system for free.\n\nNo more copying and pasting between platforms or spending hours scheduling posts.", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1020, 320], "id": "4baaa708-a782-401c-975a-f5247f107791", "name": "set_linked_in_examples"}, {"parameters": {"assignments": {"assignments": [{"id": "00020fad-c763-4aa1-8d3d-a56ba1c7dbb7", "name": "linked_in_prompt", "value": "=**ROLE:**\nYou are a world-class B2B content strategist and LinkedIn growth expert. Your expertise lies in creating compelling professional content around AI, automation, and no-code solutions. You are a master of professional storytelling, turning technical case studies into insightful, engaging posts that drive meaningful connections and establish thought leadership.\n\n**OBJECTIVE:**\nYour mission is to generate **three distinct, high-potential LinkedIn posts**. Each post will promote a specific n8n automation, framing it as a professional case study. The ultimate goals are to:\n1.  Grow my LinkedIn professional network (followers).\n2.  Establish my profile as a go-to resource for AI and automation.\n3.  Drive awareness and interest in my YouTube channel and Skool community.\n4.  Get users to comment for a lead magnet (the n8n workflow).\n\n**STEP 1: ANALYZE SOURCE MATERIAL (THE BUSINESS CASE)**\nFirst, meticulously analyze the provided YouTube video transcript. Do not summarize it. Instead, extract the following key business-oriented elements:\n1.  **The Business Pain Point:** What common, frustrating, or inefficient business process does this automation solve? Frame it in terms of lost time, potential for human error, or missed opportunities.\n2.  **The Strategic Solution:** How does the n8n automation provide a smart, strategic solution? What is the core \"insight\" or \"lever\" it uses to create value?\n3.  **The Quantifiable Business Impact:** What is the measurable outcome? Frame it in business terms (e.g., \"reclaimed 10+ hours for strategic work,\" \"achieved 99% accuracy in data processing,\" \"reduced new client onboarding time by 50%\"). If not explicitly mentioned, create a powerful and believable metric.\n\n<youtube_video_transcript>\n{{ $('set_youtube_details').item.json.transcript }}\n</youtube_video_transcript>\n\n**STEP 2: STUDY INSPIRATIONAL EXAMPLES (LINKEDIN POSTS)**\nNext, study the structure, tone, and especially the Call to Action (CTA) of the following successful LinkedIn posts. These examples are your primary source for determining the structure of the posts you will generate. Pay close attention to the length of the examples as they \"feel\" right in length.\n\n<linkedin_post_examples>\n{{ $('set_linked_in_examples').item.json.linked_in_examples }}\n</linkedin_post_examples>\n\n**STEP 3: DECONSTRUCT EXAMPLES & GENERATE POSTS**\nNow you will generate 3 unique LinkedIn post options. Your primary task is to act as a content strategist: **analyze the provided LinkedIn examples, identify the most effective post structures, and then apply those structures** to the business case from Step 1.\n\n**Your process:**\n1.  **Identify Core Structures:** Analyze the `<linkedin_post_examples>`. Identify 3 distinct formats (e.g., \"Problem/Agitate/Solve,\" \"Personal Story → Business Lesson,\" \"Contrarian Take → Justification\").\n2.  **Map Content to Structures:** For each structure, weave the \"Business Pain Point,\" \"Strategic Solution,\" and \"Business Impact\" into a compelling narrative.\n3.  **Craft the Posts:** Generate one post for each chosen structure. The post should be highly readable, using short paragraphs and ample white space.\n\n**Essential Components for each LinkedIn Post:**\n-   **An Intriguing Hook:** A first line that stops the scroll and speaks to a professional ambition or frustration.\n-   **A Relatable Story/Problem:** Briefly set the scene using the \"Business Pain Point.\"\n-   **The Insightful Solution:** Explain the \"Strategic Solution\" as the turning point.\n-   **A Dynamic, High-Engagement Call to Action (CTA):** This is critical. Instead of a fixed format, you will **craft the most effective CTA by analyzing the examples provided.** Your CTA must accomplish two things:\n    1.  Clearly state how to get the free n8n workflow template by commenting with a specific `[KEYWORD]`.\n    2.  Naturally encourage following my profile and sharing the post. Draw inspiration for the wording and style directly from the successful CTAs in the examples. If it fits the narrative, you can subtly mention that more deep dives are on my YouTube or in my Skool community.\n\n**CONSTRAINTS:**\n-   Use emojis sparingly and professionally (e.g., ✅, 💡, 🚀) to enhance readability.\n-   The tone must be professional, insightful, and helpful.\n-   The `[KEYWORD]` should be a professional, single word in all caps (e.g., BLUEPRINT, WORKFLOW, SYSTEM).\n\n**FINAL OUTPUT FORMAT:**\nYou MUST format your entire response as a single, valid JSON object. The root of the object should be a key named \"post_options\", which contains an array of three post objects. Adhere strictly to the following structure for each object:\n{\n  \"analysis\": \"<string: Explain which LinkedIn example structure was applied>\",\n  \"post_text\": \"<string: The full text of the LinkedIn post, with line breaks>\"\n}\nDo not include any text or explanations outside of the JSON object.", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1240, 320], "id": "85b63e29-31f5-47a9-b2a1-184fda5a2308", "name": "build_linked_in_prompt"}, {"parameters": {"promptType": "define", "text": "={{ $('build_linked_in_prompt').item.json.linked_in_prompt }}", "hasOutputParser": true, "batching": {}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.7, "position": [1460, 320], "id": "45298db0-4193-4eee-a9ca-ce71eb968be3", "name": "write_linked_in_posts", "retryOnFail": true}, {"parameters": {"schemaType": "manual", "inputSchema": "{\n  \"$schema\": \"http://json-schema.org/draft-07/schema#\",\n  \"title\": \"Generated LinkedIn Post Options\",\n  \"description\": \"A structured output containing three distinct LinkedIn post variations designed to drive multi-platform growth (LinkedIn, YouTube, Skool).\",\n  \"type\": \"object\",\n  \"required\": [\n    \"post_options\"\n  ],\n  \"properties\": {\n    \"post_options\": {\n      \"description\": \"An array containing the three generated LinkedIn post objects.\",\n      \"type\": \"array\",\n      \"minItems\": 3,\n      \"maxItems\": 3,\n      \"items\": {\n        \"type\": \"object\",\n        \"required\": [\n          \"analysis\",\n          \"post_text\"\n        ],\n        \"properties\": {\n          \"analysis\": {\n            \"description\": \"A brief explanation of which LinkedIn example structure was applied to generate this post.\",\n            \"type\": \"string\"\n          },\n          \"post_text\": {\n            \"description\": \"The full, ready-to-post text of the LinkedIn post, including necessary line breaks for readability.\",\n            \"type\": \"string\"\n          }\n        }\n      }\n    }\n  }\n}", "autoFix": true}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.3, "position": [1540, 540], "id": "634d8553-f2fa-4982-ac3e-bec5c30a27f6", "name": "linked_in_parser"}, {"parameters": {"authentication": "oAuth2", "select": "channel", "channelId": {"__rl": true, "value": "C08KC39K8DR", "mode": "list", "cachedResultName": "ai-tools-content"}, "text": "=*LinkedIn Repurposing | <{{ $('set_youtube_details').item.json.url }}|{{ $('set_youtube_details').item.json.title }}>*", "otherOptions": {"includeLinkToWorkflow": false, "unfurl_links": false, "unfurl_media": false}}, "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [1860, 320], "id": "4d17eea6-931a-4fa8-9d20-1413d45bc373", "name": "send_initial_linked_in_msg", "webhookId": "da680568-43ab-4b52-a9de-b92f2c64191b", "credentials": {"slackOAuth2Api": {"id": "iKdTzmZ6YuYaaRG4", "name": "<PERSON><PERSON><PERSON> (OAuth)"}}}, {"parameters": {"assignments": {"assignments": [{"id": "e22eb46f-ddaa-4ef6-836e-84b975aec40f", "name": "options", "value": "={{ $('write_linked_in_posts').item.json.output.post_options }}", "type": "array"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2140, 320], "id": "e4661c4f-c76e-41f3-946e-25261513315e", "name": "set_linked_in_options"}, {"parameters": {"fieldToSplitOut": "options", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [2420, 320], "id": "12dc2986-e075-439e-af98-2fe8368565b8", "name": "split_linked_in_options"}, {"parameters": {"authentication": "oAuth2", "select": "channel", "channelId": {"__rl": true, "value": "C08KC39K8DR", "mode": "list", "cachedResultName": "ai-tools-content"}, "text": "={{ `*Post Option #${$itemIndex + 1}*` }}\n\n```\n{{ $json.post_text }}\n```", "otherOptions": {"includeLinkToWorkflow": false, "thread_ts": {"replyValues": {"thread_ts": "={{ $('send_initial_linked_in_msg').item.json.message.ts }}"}}, "unfurl_links": false, "unfurl_media": false}}, "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [2700, 320], "id": "11207038-051f-46ff-8f0a-3ceb0a47f6cc", "name": "send_linked_in_post_msg", "webhookId": "94d359c4-d735-4a53-9690-575279090f58", "credentials": {"slackOAuth2Api": {"id": "iKdTzmZ6YuYaaRG4", "name": "<PERSON><PERSON><PERSON> (OAuth)"}}}, {"parameters": {"authentication": "oAuth2", "select": "channel", "channelId": {"__rl": true, "value": "C08KC39K8DR", "mode": "list", "cachedResultName": "ai-tools-content"}, "text": "=*Twitter Repurposing | <{{ $('set_youtube_details').item.json.url }}|{{ $('set_youtube_details').item.json.title }}>*  ", "otherOptions": {"includeLinkToWorkflow": false, "unfurl_links": false, "unfurl_media": false}}, "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [1860, -340], "id": "8b1020e2-c233-4163-bb75-44f9d882f7c5", "name": "send_initial_tweets_msg", "webhookId": "93918daa-914d-4b13-a70b-85e648d31fce", "credentials": {"slackOAuth2Api": {"id": "iKdTzmZ6YuYaaRG4", "name": "<PERSON><PERSON><PERSON> (OAuth)"}}}, {"parameters": {"assignments": {"assignments": [{"id": "e22eb46f-ddaa-4ef6-836e-84b975aec40f", "name": "options", "value": "={{ $('write_tweets').item.json.output.tweet_options }}", "type": "array"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2140, -340], "id": "68ae7570-598d-4fa5-8e59-1e9bd0ed410e", "name": "set_twitter_options"}, {"parameters": {"fieldToSplitOut": "options", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [2420, -340], "id": "976e2ff8-8f4d-4673-a39c-44c5d2b15d64", "name": "split_tweet_options"}, {"parameters": {"authentication": "oAuth2", "select": "channel", "channelId": {"__rl": true, "value": "C08KC39K8DR", "mode": "list", "cachedResultName": "ai-tools-content"}, "text": "={{ `*Tweet Option #${$itemIndex + 1}*` }}\n\n```\n{{ $json.tweet_text }}\n```", "otherOptions": {"includeLinkToWorkflow": false, "thread_ts": {"replyValues": {"thread_ts": "={{ $('send_initial_tweets_msg').item.json.message.ts }}"}}, "unfurl_links": false, "unfurl_media": false}}, "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [2700, -340], "id": "aa814c17-9ed8-40fc-8c36-b6ef9ce9c138", "name": "send_tweet_msg", "webhookId": "af12de4c-83d3-4684-9134-61dabb27a23c", "credentials": {"slackOAuth2Api": {"id": "iKdTzmZ6YuYaaRG4", "name": "<PERSON><PERSON><PERSON> (OAuth)"}}}, {"parameters": {"content": "## 1. Scrape YouTube Video\n\n- Uses `streamers~youtube-scraper` Apify actor to scrape the full transcript and metadata from the given YouTube video url\n- Add an Apify header auth credential to use this node", "height": 540, "width": 800, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [-80, -220], "typeVersion": 1, "id": "ff3fe8d7-75cc-46dd-ab9d-3481b5b896fa", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "## 2. Repurpose YouTube Video Into Twitter Post\n\n- Takes the YouTube script as input\n- Takes the specified \"example\" Twitter posts as input to model what we want our output to look like\n- Makes an LLM call to generate two post examples\n- Shares the results in a Slack message", "height": 620, "width": 2080, "color": 5}, "type": "n8n-nodes-base.stickyNote", "position": [860, -560], "typeVersion": 1, "id": "954212e2-04fb-4943-9438-9aa4f0e0fadc", "name": "Sticky Note1"}, {"parameters": {"content": "## 3. Repurpose YouTube Video Into LinkedIn Post\n\n- Takes the YouTube script as input\n- Takes the specified \"example\" LinkedIn posts as input to model what we want our output to look like\n- Makes an LLM call to generate two post examples\n- Shares the results in a Slack message", "height": 620, "width": 2080, "color": 6}, "type": "n8n-nodes-base.stickyNote", "position": [860, 80], "typeVersion": 1, "id": "ed0db85b-d3df-4932-bc32-868aa6a76a11", "name": "Sticky Note2"}], "pinData": {}, "connections": {"form_trigger": {"main": [[{"node": "scrape_youtube_video", "type": "main", "index": 0}]]}, "scrape_youtube_video": {"main": [[{"node": "set_youtube_details", "type": "main", "index": 0}]]}, "set_twitter_examples": {"main": [[{"node": "build_twitter_prompt", "type": "main", "index": 0}]]}, "build_twitter_prompt": {"main": [[{"node": "write_tweets", "type": "main", "index": 0}]]}, "claude-sonnet-4": {"ai_languageModel": [[{"node": "write_tweets", "type": "ai_languageModel", "index": 0}, {"node": "tweet_parser", "type": "ai_languageModel", "index": 0}, {"node": "write_linked_in_posts", "type": "ai_languageModel", "index": 0}, {"node": "linked_in_parser", "type": "ai_languageModel", "index": 0}]]}, "tweet_parser": {"ai_outputParser": [[{"node": "write_tweets", "type": "ai_outputParser", "index": 0}]]}, "write_tweets": {"main": [[{"node": "send_initial_tweets_msg", "type": "main", "index": 0}]]}, "set_youtube_details": {"main": [[{"node": "set_linked_in_examples", "type": "main", "index": 0}, {"node": "set_twitter_examples", "type": "main", "index": 0}]]}, "set_linked_in_examples": {"main": [[{"node": "build_linked_in_prompt", "type": "main", "index": 0}]]}, "build_linked_in_prompt": {"main": [[{"node": "write_linked_in_posts", "type": "main", "index": 0}]]}, "linked_in_parser": {"ai_outputParser": [[{"node": "write_linked_in_posts", "type": "ai_outputParser", "index": 0}]]}, "write_linked_in_posts": {"main": [[{"node": "send_initial_linked_in_msg", "type": "main", "index": 0}]]}, "send_initial_linked_in_msg": {"main": [[{"node": "set_linked_in_options", "type": "main", "index": 0}]]}, "set_linked_in_options": {"main": [[{"node": "split_linked_in_options", "type": "main", "index": 0}]]}, "split_linked_in_options": {"main": [[{"node": "send_linked_in_post_msg", "type": "main", "index": 0}]]}, "send_initial_tweets_msg": {"main": [[{"node": "set_twitter_options", "type": "main", "index": 0}]]}, "set_twitter_options": {"main": [[{"node": "split_tweet_options", "type": "main", "index": 0}]]}, "split_tweet_options": {"main": [[{"node": "send_tweet_msg", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "67b86ea5-d197-41c0-b7fa-3d5f07d0d5a0", "meta": {"templateCredsSetupCompleted": true, "instanceId": "06e5009344f682419c20ccd4ecdcb5223bbb91761882af93ac6d468dbc2cbf8d"}, "id": "d0uf5NZfGfJADX26", "tags": []}
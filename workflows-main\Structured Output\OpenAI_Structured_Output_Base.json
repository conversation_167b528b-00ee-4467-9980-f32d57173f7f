{"name": "OpenAI Structured Output Base", "nodes": [{"parameters": {"method": "POST", "url": "https://api.openai.com/v1/chat/completions", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ $json }}", "options": {}}, "id": "bac7146f-18d3-40c9-85df-8c1018510bb6", "name": "OpenAI", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1480, 800], "credentials": {"httpHeaderAuth": {"id": "9Yk60HcVSVgLxdSJ", "name": "OpenAI Header Key"}}}, {"parameters": {"assignments": {"assignments": [{"id": "cf9374ef-7269-4455-b32d-43e34d993f4f", "name": "response_format.type", "value": "json_schema", "type": "string"}, {"id": "28a95f27-27e0-4bc0-abae-60a05dc8bb4d", "name": "response_format.json_schema.name", "value": "instructions_final_answer", "type": "string"}, {"id": "cbf998ef-fd0d-425a-85bd-ada90c198433", "name": "response_format.json_schema.schema", "value": "{\n  \"title\": \"Instructions for the final answer\",\n  \"description\": \"Follow these steps, first analyze the user's message, think of a strategy for the response and finally generate the final response.\",\n  \"type\": \"object\",\n  \"properties\": {\n    \"reflection\": {\n      \"type\": \"string\",\n  \"description\": \"Reflect on the user's message and its context\"\n    },\n    \"strategy\": {\n      \"type\": \"string\",\n  \"description\": \"Think of a strategy to respond to it following the system's instructions.\"\n    },\n    \"final_answer\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\",\n  \"description\": \"sentence part.\"\n      },\n  \"description\": \"final answer partitioned into not very large sentences\"\n    }\n  },\n  \"required\": [\n    \"reflection\",\n    \"strategy\",\n    \"final_answer\"\n  ],\n  \"additionalProperties\": false\n}", "type": "object"}, {"id": "c5d1082a-42df-4cd7-8662-af2bd1f1930d", "name": "response_format.json_schema.strict", "value": true, "type": "boolean"}]}, "options": {}}, "id": "914441be-ce2f-44b6-b065-74720209bcb0", "name": "responseFormat", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [980, 980]}, {"parameters": {"assignments": {"assignments": [{"id": "4c7b4a94-c342-4143-8950-c845e0fd2d48", "name": "model", "value": "gpt-4o-mini", "type": "string"}]}, "options": {}}, "id": "8c6de17d-68b7-4cbd-ab2e-7b769e446662", "name": "model", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [980, 640]}, {"parameters": {"options": {}}, "id": "3fb8ebde-bc14-4430-8b41-c6da2e558f70", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [760, 800], "webhookId": "5159374a-dbdd-4583-8456-78a154b82dc0"}, {"parameters": {"assignments": {"assignments": [{"id": "a10706be-8775-4c6e-b228-2e8fd48e819d", "name": "messages", "value": "=[\n      {\n        \"role\": \"system\",\n        \"content\": {{ JSON.stringify(`<PERSON> are <PERSON><PERSON>, <PERSON>'s virtual assistant, and you are interacting via WhatsApp. Your native language is Brazilian Portuguese.\nWhenever you receive information in XML <audio> tags, it means that the content was originally an audio that was converted to text.\nWhenever you receive information in XML <text> tags, it means that the content was originally a text.`) }}\n      },\n      {\n        \"role\": \"user\",\n        \"content\": \"{{ $json.chatInput }}\"\n      }\n    ]", "type": "array"}]}, "options": {}}, "id": "c347da39-3750-4366-b19c-7ac7d29807fa", "name": "messages", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [980, 800]}, {"parameters": {"mode": "raw", "jsonOutput": "={{ JSON.parse($json.choices[0].message.content) }}", "options": {}}, "id": "b7bd0f9b-8d0d-4e27-8f21-56e3c65448d8", "name": "Parse", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1700, 800]}, {"parameters": {"mode": "combineBySql", "numberInputs": 3, "query": "SELECT * FROM input1, input2, input3"}, "id": "12de2b54-8d3e-41f1-be75-7c033cc9d770", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "typeVersion": 3, "position": [1240, 800]}], "pinData": {}, "connections": {"When chat message received": {"main": [[{"node": "messages", "type": "main", "index": 0}, {"node": "model", "type": "main", "index": 0}, {"node": "responseFormat", "type": "main", "index": 0}]]}, "messages": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "model": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "responseFormat": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 2}]]}, "OpenAI": {"main": [[{"node": "Parse", "type": "main", "index": 0}]]}, "Merge": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "a59d3dc4-e945-476c-9f47-375347e947b7", "meta": {"templateCredsSetupCompleted": true, "instanceId": "2ad20c637f548d7b1e09cbae0383c6644aca87b4f0fe8deda2de937f77c2be79"}, "id": "mq1G6Cr2071bt1vV", "tags": []}
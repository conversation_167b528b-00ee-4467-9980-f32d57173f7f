#!/usr/bin/env node

/**
 * Advanced YouTube Automation System - Test Suite
 * Comprehensive testing script for validating all system components
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

class AdvancedYouTubeAutomationTester {
  constructor(config) {
    this.config = config;
    this.testResults = {
      passed: 0,
      failed: 0,
      total: 0,
      details: []
    };
  }

  async runAllTests() {
    console.log('🚀 Starting Advanced YouTube Automation System Tests\n');
    
    try {
      await this.testSystemRequirements();
      await this.testApiConnections();
      await this.testWorkflowComponents();
      await this.testContentGeneration();
      await this.testVideoProduction();
      await this.testQualityAssurance();
      await this.testYouTubeIntegration();
      await this.testAnalyticsTracking();
      
      this.generateTestReport();
    } catch (error) {
      console.error('❌ Test suite failed:', error.message);
      process.exit(1);
    }
  }

  async testSystemRequirements() {
    console.log('📋 Testing System Requirements...');
    
    // Test Node.js version
    await this.runTest('Node.js Version', () => {
      const version = process.version;
      const majorVersion = parseInt(version.slice(1).split('.')[0]);
      return majorVersion >= 18;
    });

    // Test N8N availability
    await this.runTest('N8N Availability', async () => {
      try {
        const response = await axios.get(`${this.config.n8nUrl}/healthz`, {
          timeout: 5000
        });
        return response.status === 200;
      } catch (error) {
        return false;
      }
    });

    // Test FFmpeg installation
    await this.runTest('FFmpeg Installation', () => {
      const { execSync } = require('child_process');
      try {
        execSync('ffmpeg -version', { stdio: 'ignore' });
        return true;
      } catch (error) {
        return false;
      }
    });

    console.log('✅ System Requirements Tests Completed\n');
  }

  async testApiConnections() {
    console.log('🔌 Testing API Connections...');

    // Test OpenRouter API
    await this.runTest('OpenRouter API Connection', async () => {
      try {
        const response = await axios.get('https://openrouter.ai/api/v1/models', {
          headers: {
            'Authorization': `Bearer ${this.config.openRouterApiKey}`
          },
          timeout: 10000
        });
        return response.status === 200 && response.data.data.length > 0;
      } catch (error) {
        console.log('OpenRouter API Error:', error.message);
        return false;
      }
    });

    // Test YouTube API
    await this.runTest('YouTube API Connection', async () => {
      try {
        const response = await axios.get('https://www.googleapis.com/youtube/v3/channels', {
          params: {
            part: 'snippet',
            mine: true,
            access_token: this.config.youtubeAccessToken
          },
          timeout: 10000
        });
        return response.status === 200;
      } catch (error) {
        console.log('YouTube API Error:', error.message);
        return false;
      }
    });

    // Test Google Sheets API
    await this.runTest('Google Sheets API Connection', async () => {
      try {
        const response = await axios.get(`https://sheets.googleapis.com/v4/spreadsheets/${this.config.analyticsSpreadsheetId}`, {
          params: {
            access_token: this.config.googleAccessToken
          },
          timeout: 10000
        });
        return response.status === 200;
      } catch (error) {
        console.log('Google Sheets API Error:', error.message);
        return false;
      }
    });

    // Test Replicate API
    await this.runTest('Replicate API Connection', async () => {
      try {
        const response = await axios.get('https://api.replicate.com/v1/models', {
          headers: {
            'Authorization': `Token ${this.config.replicateApiToken}`
          },
          timeout: 10000
        });
        return response.status === 200;
      } catch (error) {
        console.log('Replicate API Error:', error.message);
        return false;
      }
    });

    console.log('✅ API Connection Tests Completed\n');
  }

  async testWorkflowComponents() {
    console.log('⚙️ Testing Workflow Components...');

    // Test workflow file existence
    await this.runTest('Main Workflow File Exists', () => {
      return fs.existsSync('advanced-youtube-automation-workflow.json');
    });

    // Test workflow JSON validity
    await this.runTest('Workflow JSON Validity', () => {
      try {
        const workflowContent = fs.readFileSync('advanced-youtube-automation-workflow.json', 'utf8');
        const workflow = JSON.parse(workflowContent);
        return workflow.nodes && workflow.connections && workflow.nodes.length > 0;
      } catch (error) {
        return false;
      }
    });

    // Test required nodes presence
    await this.runTest('Required Nodes Present', () => {
      const workflowContent = fs.readFileSync('advanced-youtube-automation-workflow.json', 'utf8');
      const workflow = JSON.parse(workflowContent);
      
      const requiredNodes = [
        'Market Strategy Thinker',
        'AI Content Generator',
        'Content Processor',
        'Video Production System',
        'Quality Assurance',
        'YouTube Upload',
        'Analytics Tracker'
      ];
      
      return requiredNodes.every(nodeName => 
        workflow.nodes.some(node => node.name.includes(nodeName.split(' ')[0]))
      );
    });

    console.log('✅ Workflow Component Tests Completed\n');
  }

  async testContentGeneration() {
    console.log('🤖 Testing Content Generation...');

    // Test AI content generation
    await this.runTest('AI Content Generation', async () => {
      try {
        const response = await axios.post('https://openrouter.ai/api/v1/chat/completions', {
          model: 'anthropic/claude-3.5-sonnet',
          messages: [
            {
              role: 'system',
              content: 'You are a YouTube content creator. Generate a brief video script about AI automation.'
            },
            {
              role: 'user',
              content: 'Create a 30-second video script about AI automation benefits.'
            }
          ],
          max_tokens: 500,
          temperature: 0.7
        }, {
          headers: {
            'Authorization': `Bearer ${this.config.openRouterApiKey}`,
            'Content-Type': 'application/json'
          },
          timeout: 30000
        });

        const content = response.data.choices[0].message.content;
        return content && content.length > 50 && content.includes('AI');
      } catch (error) {
        console.log('Content Generation Error:', error.message);
        return false;
      }
    });

    // Test content quality assessment
    await this.runTest('Content Quality Assessment', () => {
      const sampleContent = {
        videoTitle: 'Amazing AI Automation Secrets Revealed!',
        videoScript: 'Discover the incredible power of AI automation that will transform your productivity. Learn the secret techniques that top professionals use to automate their workflows and achieve amazing results in record time.',
        seoTags: ['AI', 'automation', 'productivity', 'secrets', 'amazing']
      };

      // Simulate quality assessment
      const hasTitle = sampleContent.videoTitle && sampleContent.videoTitle.length > 10;
      const hasScript = sampleContent.videoScript && sampleContent.videoScript.length > 50;
      const hasTags = sampleContent.seoTags && sampleContent.seoTags.length >= 5;

      return hasTitle && hasScript && hasTags;
    });

    console.log('✅ Content Generation Tests Completed\n');
  }

  async testVideoProduction() {
    console.log('🎬 Testing Video Production...');

    // Test FFmpeg command generation
    await this.runTest('FFmpeg Command Generation', () => {
      const sampleData = {
        imageUrl: 'https://example.com/image.jpg',
        duration: 30,
        resolution: '1080x1920',
        title: 'Test Video'
      };

      const command = this.generateTestFFmpegCommand(sampleData);
      return command.includes('ffmpeg') && 
             command.includes(sampleData.duration.toString()) &&
             command.includes(sampleData.resolution);
    });

    // Test video specifications
    await this.runTest('Video Specifications Validation', () => {
      const specs = {
        resolution: '1080x1920',
        fps: 30,
        duration: 30,
        format: 'mp4',
        codec: 'h264'
      };

      return specs.resolution === '1080x1920' &&
             specs.fps === 30 &&
             specs.duration > 0 &&
             specs.format === 'mp4' &&
             specs.codec === 'h264';
    });

    console.log('✅ Video Production Tests Completed\n');
  }

  async testQualityAssurance() {
    console.log('🔍 Testing Quality Assurance...');

    // Test quality scoring
    await this.runTest('Quality Scoring System', () => {
      const sampleContent = {
        contentQuality: { score: 85 },
        viralPotential: 7,
        engagementPotential: { score: 8 },
        technicalQuality: { score: 90 }
      };

      const overallScore = (
        sampleContent.contentQuality.score +
        (sampleContent.viralPotential * 10) +
        sampleContent.engagementPotential.score * 10 +
        sampleContent.technicalQuality.score
      ) / 4;

      return overallScore >= 70; // Minimum quality threshold
    });

    // Test validation gates
    await this.runTest('Quality Gates Validation', () => {
      const validationResults = {
        titleLength: true,
        scriptCompleteness: true,
        tagCount: true,
        videoSpecs: true,
        contentAppropriate: true
      };

      return Object.values(validationResults).every(result => result === true);
    });

    console.log('✅ Quality Assurance Tests Completed\n');
  }

  async testYouTubeIntegration() {
    console.log('📺 Testing YouTube Integration...');

    // Test metadata optimization
    await this.runTest('Metadata Optimization', () => {
      const originalMetadata = {
        title: 'This is a very long title that exceeds the recommended character limit for YouTube videos',
        description: 'Short desc',
        tags: ['tag1', 'tag2']
      };

      const optimized = this.optimizeMetadata(originalMetadata);
      
      return optimized.title.length <= 60 &&
             optimized.description.length > 100 &&
             optimized.tags.length >= 5;
    });

    // Test upload payload structure
    await this.runTest('Upload Payload Structure', () => {
      const payload = {
        snippet: {
          title: 'Test Video',
          description: 'Test description with proper formatting and keywords',
          tags: ['test', 'automation', 'youtube'],
          categoryId: '22',
          defaultLanguage: 'en'
        },
        status: {
          privacyStatus: 'private',
          selfDeclaredMadeForKids: false
        }
      };

      return payload.snippet && 
             payload.snippet.title &&
             payload.snippet.description &&
             payload.snippet.tags &&
             payload.status &&
             payload.status.privacyStatus;
    });

    console.log('✅ YouTube Integration Tests Completed\n');
  }

  async testAnalyticsTracking() {
    console.log('📊 Testing Analytics Tracking...');

    // Test analytics data structure
    await this.runTest('Analytics Data Structure', () => {
      const analyticsData = {
        videoId: 'test123',
        uploadTime: new Date().toISOString(),
        contentMetrics: {
          title: 'Test Video',
          duration: 30,
          qualityScore: 85
        },
        predictions: {
          expectedViews: 1000,
          expectedEngagement: 50
        }
      };

      return analyticsData.videoId &&
             analyticsData.uploadTime &&
             analyticsData.contentMetrics &&
             analyticsData.predictions;
    });

    // Test performance calculations
    await this.runTest('Performance Calculations', () => {
      const metrics = {
        views: 1000,
        likes: 50,
        comments: 10,
        shares: 5,
        duration: 30
      };

      const engagementRate = ((metrics.likes + metrics.comments + metrics.shares) / metrics.views) * 100;
      const retentionRate = 0.75; // 75% average retention

      return engagementRate > 0 && retentionRate > 0;
    });

    console.log('✅ Analytics Tracking Tests Completed\n');
  }

  async runTest(testName, testFunction) {
    this.testResults.total++;
    
    try {
      const result = await testFunction();
      if (result) {
        console.log(`  ✅ ${testName}`);
        this.testResults.passed++;
        this.testResults.details.push({ name: testName, status: 'PASSED' });
      } else {
        console.log(`  ❌ ${testName}`);
        this.testResults.failed++;
        this.testResults.details.push({ name: testName, status: 'FAILED' });
      }
    } catch (error) {
      console.log(`  ❌ ${testName} - Error: ${error.message}`);
      this.testResults.failed++;
      this.testResults.details.push({ 
        name: testName, 
        status: 'ERROR', 
        error: error.message 
      });
    }
  }

  generateTestFFmpegCommand(data) {
    return `ffmpeg -loop 1 -i "${data.imageUrl}" -f lavfi -i "color=c=black@0.8:s=${data.resolution}:d=${data.duration}" -filter_complex "[0:v]scale=${data.resolution}:force_original_aspect_ratio=increase,crop=${data.resolution}[bg];[1:v][bg]overlay[video];[video]drawtext=fontfile=/path/to/font.ttf:text='${data.title}':fontsize=48:fontcolor=white:x=(w-text_w)/2:y=100[final]" -map "[final]" -t ${data.duration} -c:v libx264 -preset fast -crf 23 -pix_fmt yuv420p "output_${Date.now()}.mp4"`;
  }

  optimizeMetadata(metadata) {
    return {
      title: metadata.title.length > 60 ? metadata.title.substring(0, 57) + '...' : metadata.title,
      description: metadata.description.length < 100 ? metadata.description + '\n\n🔔 Subscribe for more content!\n\n#YouTube #Automation #AI' : metadata.description,
      tags: metadata.tags.length < 5 ? [...metadata.tags, 'youtube', 'automation', 'ai'] : metadata.tags
    };
  }

  generateTestReport() {
    console.log('\n📊 Test Results Summary');
    console.log('========================');
    console.log(`Total Tests: ${this.testResults.total}`);
    console.log(`Passed: ${this.testResults.passed} ✅`);
    console.log(`Failed: ${this.testResults.failed} ❌`);
    console.log(`Success Rate: ${((this.testResults.passed / this.testResults.total) * 100).toFixed(1)}%`);

    if (this.testResults.failed > 0) {
      console.log('\n❌ Failed Tests:');
      this.testResults.details
        .filter(test => test.status !== 'PASSED')
        .forEach(test => {
          console.log(`  - ${test.name} (${test.status})`);
          if (test.error) {
            console.log(`    Error: ${test.error}`);
          }
        });
    }

    // Save detailed report
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        total: this.testResults.total,
        passed: this.testResults.passed,
        failed: this.testResults.failed,
        successRate: (this.testResults.passed / this.testResults.total) * 100
      },
      details: this.testResults.details
    };

    fs.writeFileSync('test-report.json', JSON.stringify(report, null, 2));
    console.log('\n📄 Detailed report saved to test-report.json');

    if (this.testResults.failed === 0) {
      console.log('\n🎉 All tests passed! Your Advanced YouTube Automation System is ready for production.');
    } else {
      console.log('\n⚠️  Some tests failed. Please review the issues above before deploying to production.');
      process.exit(1);
    }
  }
}

// Configuration
const config = {
  n8nUrl: process.env.N8N_URL || 'http://localhost:5678',
  openRouterApiKey: process.env.OPENROUTER_API_KEY,
  youtubeAccessToken: process.env.YOUTUBE_ACCESS_TOKEN,
  googleAccessToken: process.env.GOOGLE_ACCESS_TOKEN,
  replicateApiToken: process.env.REPLICATE_API_TOKEN,
  analyticsSpreadsheetId: process.env.ANALYTICS_SPREADSHEET_ID
};

// Run tests
if (require.main === module) {
  const tester = new AdvancedYouTubeAutomationTester(config);
  tester.runAllTests().catch(error => {
    console.error('Test suite failed:', error);
    process.exit(1);
  });
}

module.exports = AdvancedYouTubeAutomationTester;

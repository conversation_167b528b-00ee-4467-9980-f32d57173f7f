{"name": "Second Brain Agent", "nodes": [{"parameters": {"promptType": "define", "text": "={{ $json.prompt }}", "options": {"systemMessage": "=You are a helpful assistant. I will either ask you a question or provide you with new information.\n\nIf it's new information:\n\nYou will use the add to vector store tool workflow to add this new information to my database.\n\nIf I'm asking you about something:\n\nYou use RAG and vector stores to look up information based on the questions I ask and provide me with the answers."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [-380, 0], "id": "3f89bbad-46e3-4af5-84af-65a1f3c68e4e", "name": "Vector Store Agent"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-520, 220], "id": "2ebcfcce-c6e6-439d-9460-b85ca70bbdbf", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "ulLOziYE3uIZc6he", "name": "OpenAi account"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $json.prompt }}"}, "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1.3, "position": [-360, 220], "id": "1f490248-2c11-4958-9314-bf3504eacbc0", "name": "Postgres Chat Memory", "credentials": {"postgres": {"id": "i2E9zvRW9rqnSolq", "name": "Postgres account"}}}, {"parameters": {"mode": "retrieve-as-tool", "toolName": "second_brain", "toolDescription": "Second brain retrieval", "tableName": {"__rl": true, "value": "documents", "mode": "list", "cachedResultName": "documents"}, "options": {"queryName": "match_documents"}}, "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [-160, 200], "id": "80ae42ff-cc71-462e-b067-0753085e79e2", "name": "Supabase Vector Store", "credentials": {"supabaseApi": {"id": "m1dt29MDbxHjGj8A", "name": "Supabase account"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [60, 320], "id": "d3e46652-cb10-4f98-8dec-db3d0649a982", "name": "Embeddings OpenAI", "credentials": {"openAiApi": {"id": "ulLOziYE3uIZc6he", "name": "OpenAi account"}}}, {"parameters": {"content": "## <PERSON> Chatbot", "height": 520, "width": 1200}, "type": "n8n-nodes-base.stickyNote", "position": [-960, -80], "typeVersion": 1, "id": "7adb6be5-05b9-4c66-a3dd-31d426517bb3", "name": "<PERSON><PERSON>"}, {"parameters": {"method": "POST", "url": "https://api.apify.com/v2/acts/dB9f4B02ocpTICIEY/runs", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "Accept", "value": "application/json"}]}, "sendBody": true, "contentType": "raw", "rawContentType": "application/json", "body": "={\n    \"includeTimestamps\": \"No\",\n    \"startUrls\": [\n        \"{{ $json.message.text }}\"\n    ]\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-680, 540], "id": "d07f7398-2d80-4fb8-b288-7e671de7b360", "name": "Apify Get YouTube Transcript", "credentials": {"httpHeaderAuth": {"id": "akEmJryjqWC3gI7B", "name": "Apify"}}}, {"parameters": {"url": "=https://api.apify.com/v2/actor-runs/{{ $json.data.id }}/dataset/items", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "content-type", "value": "application/json"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-300, 540], "id": "1bdc416f-efef-442a-b63f-b16ed2f581a4", "name": "Apify Get Last Run", "credentials": {"httpHeaderAuth": {"id": "akEmJryjqWC3gI7B", "name": "Apify"}}}, {"parameters": {"amount": 45}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [-480, 540], "id": "028310db-d912-4207-a987-997ea2412f17", "name": "Wait", "webhookId": "ad2c2075-36df-446d-a448-ca4abe8320ed"}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4o-mini", "mode": "list", "cachedResultName": "GPT-4O-MINI"}, "messages": {"values": [{"content": "=You are a helpful assistant. Please analyze the transcript below and convert into an in-depth article I can utilize to add to my RAG vector store. Output the title and article directly, with nothing else. Summarize as an independent article, without mentioning the speaker or presenter.\n {{ $json.transcript }}\n\nExample output\n\nThe role of bird flu in egg prices\nBird flu has affected egg prices...etc"}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [-80, 540], "id": "23242b9b-1610-4954-832d-7b43fd62e7a5", "name": "OpenAI", "credentials": {"openAiApi": {"id": "ulLOziYE3uIZc6he", "name": "OpenAi account"}}}, {"parameters": {"mode": "insert", "tableName": {"__rl": true, "value": "documents", "mode": "list", "cachedResultName": "documents"}, "options": {"queryName": "match_documents"}}, "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [1040, 540], "id": "c15159a2-a53f-4688-ab35-dc0e9fba1fa2", "name": "Supabase Vector Store1", "credentials": {"supabaseApi": {"id": "m1dt29MDbxHjGj8A", "name": "Supabase account"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [940, 740], "id": "f8863296-20af-4355-a831-61b5d94d446a", "name": "Embeddings OpenAI1", "credentials": {"openAiApi": {"id": "ulLOziYE3uIZc6he", "name": "OpenAi account"}}}, {"parameters": {"jsonMode": "expressionData", "jsonData": "={{ $json['Text Body'] }}", "options": {}}, "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "typeVersion": 1, "position": [1180, 740], "id": "a6db2275-bcba-4cb6-a005-584a3b7d0b19", "name": "Default Data Loader"}, {"parameters": {"assignments": {"assignments": [{"id": "09cb9df5-6fff-49c9-a873-817c907d2335", "name": "Text Body", "value": "={{ $json.message.content }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [800, 540], "id": "a7fdd22a-9c19-43e0-bb69-200288f7b59c", "name": "<PERSON>"}, {"parameters": {"content": "## YouTube Transcript", "height": 300, "width": 1200, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [-960, 480], "typeVersion": 1, "id": "69fd1d6e-14d4-4435-b584-2cc8bfdfb24c", "name": "Sticky Note1"}, {"parameters": {"operation": "pdf", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [860, 80], "id": "bf3ab040-0b2c-4cf1-bc08-5763351d8b93", "name": "Extract from File"}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4o", "mode": "list", "cachedResultName": "GPT-4O"}, "messages": {"values": [{"content": "=You are a helpful assistant. I will provide you with the text of a document. I want you to turn the document into an article that covers all the main learnings and points.\n\nSample Output format:\nExamining the Influence of Likes and Dislikes on User Behaviors on Social\nIn recent years, social media platforms have undergone several changes...etc\n\nThis is the text:\n{{ $json.info.Title }}\n{{ $json.text }}"}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [1280, 80], "id": "ceb0f330-1ab4-49a3-9ea3-29df81300534", "name": "OpenAI1", "credentials": {"openAiApi": {"id": "ulLOziYE3uIZc6he", "name": "OpenAi account"}}}, {"parameters": {"content": "## PDF Upload", "height": 520, "width": 1380, "color": 2}, "type": "n8n-nodes-base.stickyNote", "position": [280, -80], "typeVersion": 1, "id": "3cdafdef-fb27-4489-b7b2-a60ffd1da894", "name": "Sticky Note2"}, {"parameters": {"content": "## Add to vector store", "height": 600, "width": 1380, "color": 7}, "type": "n8n-nodes-base.stickyNote", "position": [280, 500], "typeVersion": 1, "id": "c3111950-f340-489b-b23d-49dbce020b19", "name": "Sticky Note3"}, {"parameters": {"updates": ["*"], "additionalFields": {}}, "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.1, "position": [-1480, -360], "id": "4ddfb681-80e4-4a30-82b4-6ae45d4182e8", "name": "<PERSON>eg<PERSON>", "webhookId": "8ff3cfb6-db4d-4f2b-b395-a783b9075d4c", "credentials": {"telegramApi": {"id": "C6GHWYnJpYcpD9cU", "name": "Second <PERSON>"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "7ffa28b9-3352-47b5-b2bc-be2ef255755d", "leftValue": "={{ $json.message.document }}", "rightValue": "", "operator": {"type": "object", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Document"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "53c39416-f150-4abb-bc3d-da961d1fff81", "leftValue": "={{ $json.message.voice }}", "rightValue": "", "operator": {"type": "object", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Voice Note"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "99e6b061-f397-4237-9598-1441e56804e9", "leftValue": "={{ $json.message.text }}", "rightValue": "=https://www.youtu", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "YouTube Video"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "dc4e6b11-bb34-4825-a2b7-509e8a0cd8fa", "leftValue": "={{ $json.message.text }}", "rightValue": "https://youtu", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "YouTube Video Short URL"}]}, "options": {"fallbackOutput": "extra"}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-1100, -400], "id": "0db69f00-520a-41ee-9a2d-d4dfb95cae93", "name": "Switch"}, {"parameters": {"resource": "file", "fileId": "={{ $('Telegram Trigger').item.json.message.document.file_id }}"}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [0, -420], "id": "ea40695b-5e62-4b38-aafa-a47f817e3b91", "name": "Telegram", "webhookId": "9fbccbec-b49d-4cb0-bbb7-40a5a703c8b0", "credentials": {"telegramApi": {"id": "C6GHWYnJpYcpD9cU", "name": "Second <PERSON>"}}}, {"parameters": {"resource": "file", "fileId": "={{ $('Telegram Trigger').item.json.message.voice.file_id }}"}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-860, -300], "id": "8ef67f2e-99b4-4590-aa5f-01d5ccf39e3a", "name": "Telegram1", "webhookId": "d8d7db45-7e22-448e-818f-0877d21fd5b3", "credentials": {"telegramApi": {"id": "C6GHWYnJpYcpD9cU", "name": "Second <PERSON>"}}}, {"parameters": {"resource": "audio", "operation": "transcribe", "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [-700, -300], "id": "09c56f6a-384b-4925-a26a-01705fb66b11", "name": "OpenAI2", "credentials": {"openAiApi": {"id": "ulLOziYE3uIZc6he", "name": "OpenAi account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "26161bc8-1310-4f27-af34-74c1c95b1ac4", "name": "prompt", "value": "={{ $json.message.text }} {{ $json.text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-620, 0], "id": "5e2c8459-9ddf-4291-a3e4-35c7dfdadce0", "name": "Edit Fields1"}, {"parameters": {"chatId": "={{ $('Telegram Trigger').item.json.message.chat.id }}", "text": "={{ $json.output }}", "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [20, -40], "id": "a9a708e0-359e-4a49-a5d8-52cd7409d537", "name": "Telegram2", "webhookId": "cbc50479-67c7-433b-8d09-029ed0d43457", "credentials": {"telegramApi": {"id": "C6GHWYnJpYcpD9cU", "name": "Second <PERSON>"}}}, {"parameters": {"chatId": "={{ $('Telegram Trigger').item.json.message.chat.id }}", "text": "Successfully added to Second Brain!", "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1420, 540], "id": "2d71c3f6-bb62-497d-8424-27fe9ced7350", "name": "Telegram3", "webhookId": "e5f1b65d-fd80-44dd-8ce2-2b34868680c5", "credentials": {"telegramApi": {"id": "C6GHWYnJpYcpD9cU", "name": "Second <PERSON>"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "typeVersion": 1, "position": [1140, 900], "id": "9c43cf02-a080-473c-a41d-d34310b9954f", "name": "Recursive Character Text Splitter"}], "pinData": {"Telegram Trigger": [{"json": {"update_id": 584566860, "message": {"message_id": 31, "from": {"id": 5675741296, "is_bot": false, "first_name": "Sh", "last_name": "ab", "username": "shab354", "language_code": "en"}, "chat": {"id": 5675741296, "first_name": "Sh", "last_name": "ab", "username": "shab354", "type": "private"}, "date": 1748328484, "text": "what can you tell me about the hong kong protests?"}}}]}, "connections": {"OpenAI Chat Model": {"ai_languageModel": [[{"node": "Vector Store Agent", "type": "ai_languageModel", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "Vector Store Agent", "type": "ai_memory", "index": 0}]]}, "Supabase Vector Store": {"ai_tool": [[{"node": "Vector Store Agent", "type": "ai_tool", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Supabase Vector Store", "type": "ai_embedding", "index": 0}]]}, "Apify Get YouTube Transcript": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "Apify Get Last Run", "type": "main", "index": 0}]]}, "Apify Get Last Run": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}]]}, "OpenAI": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Embeddings OpenAI1": {"ai_embedding": [[{"node": "Supabase Vector Store1", "type": "ai_embedding", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Supabase Vector Store1", "type": "ai_document", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Supabase Vector Store1", "type": "main", "index": 0}]]}, "Extract from File": {"main": [[{"node": "OpenAI1", "type": "main", "index": 0}]]}, "OpenAI1": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Telegram Trigger": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Telegram", "type": "main", "index": 0}], [{"node": "Telegram1", "type": "main", "index": 0}], [{"node": "Apify Get YouTube Transcript", "type": "main", "index": 0}], [{"node": "Apify Get YouTube Transcript", "type": "main", "index": 0}], [{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "Telegram": {"main": [[{"node": "Extract from File", "type": "main", "index": 0}]]}, "Telegram1": {"main": [[{"node": "OpenAI2", "type": "main", "index": 0}]]}, "OpenAI2": {"main": [[{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "Edit Fields1": {"main": [[{"node": "Vector Store Agent", "type": "main", "index": 0}]]}, "Vector Store Agent": {"main": [[{"node": "Telegram2", "type": "main", "index": 0}]]}, "Supabase Vector Store1": {"main": [[{"node": "Telegram3", "type": "main", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "45ea922b-3900-4f94-8892-c8e1126ee010", "meta": {"templateCredsSetupCompleted": true, "instanceId": "fb357db16d4f03c7d7597f4abaa6a5e06176011d3bee518d28bed06754cb1f31"}, "id": "0uyymCPeVQwmpJkl", "tags": []}
#!/bin/bash

# Advanced YouTube Automation - Local AI Setup Script
# Automated installation and configuration for local AI models

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        error "This script should not be run as root. Please run as a regular user."
    fi
}

# Detect operating system
detect_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        OS="linux"
        if command -v apt-get &> /dev/null; then
            DISTRO="debian"
        elif command -v yum &> /dev/null; then
            DISTRO="rhel"
        elif command -v pacman &> /dev/null; then
            DISTRO="arch"
        else
            error "Unsupported Linux distribution"
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
    elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
        OS="windows"
    else
        error "Unsupported operating system: $OSTYPE"
    fi
    
    log "Detected OS: $OS ($DISTRO)"
}

# Check system requirements
check_requirements() {
    log "Checking system requirements..."
    
    # Check available memory
    if [[ "$OS" == "linux" ]]; then
        TOTAL_MEM=$(free -g | awk '/^Mem:/{print $2}')
    elif [[ "$OS" == "macos" ]]; then
        TOTAL_MEM=$(sysctl -n hw.memsize | awk '{print int($1/1024/1024/1024)}')
    fi
    
    info "Total RAM: ${TOTAL_MEM}GB"
    
    if [[ $TOTAL_MEM -lt 8 ]]; then
        error "Minimum 8GB RAM required. Found: ${TOTAL_MEM}GB"
    elif [[ $TOTAL_MEM -lt 16 ]]; then
        warn "16GB+ RAM recommended for optimal performance. Found: ${TOTAL_MEM}GB"
    fi
    
    # Check available disk space
    AVAILABLE_SPACE=$(df -BG . | awk 'NR==2 {print $4}' | sed 's/G//')
    info "Available disk space: ${AVAILABLE_SPACE}GB"
    
    if [[ $AVAILABLE_SPACE -lt 100 ]]; then
        error "Minimum 100GB free space required. Found: ${AVAILABLE_SPACE}GB"
    elif [[ $AVAILABLE_SPACE -lt 200 ]]; then
        warn "200GB+ free space recommended. Found: ${AVAILABLE_SPACE}GB"
    fi
    
    # Check for NVIDIA GPU
    if command -v nvidia-smi &> /dev/null; then
        GPU_INFO=$(nvidia-smi --query-gpu=name,memory.total --format=csv,noheader,nounits)
        info "NVIDIA GPU detected: $GPU_INFO"
        HAS_GPU=true
    else
        warn "No NVIDIA GPU detected. CPU-only inference will be slower."
        HAS_GPU=false
    fi
}

# Install Docker
install_docker() {
    if command -v docker &> /dev/null; then
        log "Docker already installed: $(docker --version)"
        return
    fi
    
    log "Installing Docker..."
    
    if [[ "$OS" == "linux" ]]; then
        if [[ "$DISTRO" == "debian" ]]; then
            curl -fsSL https://get.docker.com -o get-docker.sh
            sudo sh get-docker.sh
            sudo usermod -aG docker $USER
            rm get-docker.sh
        elif [[ "$DISTRO" == "rhel" ]]; then
            sudo yum install -y docker
            sudo systemctl start docker
            sudo systemctl enable docker
            sudo usermod -aG docker $USER
        elif [[ "$DISTRO" == "arch" ]]; then
            sudo pacman -S docker docker-compose
            sudo systemctl start docker
            sudo systemctl enable docker
            sudo usermod -aG docker $USER
        fi
    elif [[ "$OS" == "macos" ]]; then
        warn "Please install Docker Desktop for Mac from: https://www.docker.com/products/docker-desktop"
        warn "Then run this script again."
        exit 1
    fi
    
    log "Docker installed successfully"
}

# Install Docker Compose
install_docker_compose() {
    if command -v docker-compose &> /dev/null; then
        log "Docker Compose already installed: $(docker-compose --version)"
        return
    fi
    
    log "Installing Docker Compose..."
    
    if [[ "$OS" == "linux" ]]; then
        sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
        sudo chmod +x /usr/local/bin/docker-compose
    fi
    
    log "Docker Compose installed successfully"
}

# Install NVIDIA Container Toolkit
install_nvidia_docker() {
    if [[ "$HAS_GPU" != true ]]; then
        info "Skipping NVIDIA Docker installation (no GPU detected)"
        return
    fi
    
    if docker run --rm --gpus all nvidia/cuda:11.0-base nvidia-smi &> /dev/null; then
        log "NVIDIA Container Toolkit already installed"
        return
    fi
    
    log "Installing NVIDIA Container Toolkit..."
    
    if [[ "$OS" == "linux" && "$DISTRO" == "debian" ]]; then
        distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
        curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
        curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | sudo tee /etc/apt/sources.list.d/nvidia-docker.list
        
        sudo apt-get update && sudo apt-get install -y nvidia-docker2
        sudo systemctl restart docker
    fi
    
    log "NVIDIA Container Toolkit installed successfully"
}

# Create directory structure
create_directories() {
    log "Creating directory structure..."
    
    mkdir -p {models,ollama-models,sd-models,localai-models,localai-config,sd-config,comfyui-models,comfyui-custom-nodes,workflows,local-ai-workflows,monitoring,logs,backups}
    
    # Create subdirectories for Stable Diffusion
    mkdir -p sd-models/{Stable-diffusion,VAE,Lora,ControlNet,embeddings}
    
    # Create monitoring configuration directories
    mkdir -p monitoring/{prometheus,grafana/{dashboards,datasources}}
    
    log "Directory structure created"
}

# Generate configuration files
generate_configs() {
    log "Generating configuration files..."
    
    # Generate .env file if it doesn't exist
    if [[ ! -f .env ]]; then
        cat > .env << EOF
# System Configuration
N8N_PASSWORD=secure-password-$(openssl rand -hex 8)
POSTGRES_PASSWORD=postgres-$(openssl rand -hex 8)
GRAFANA_PASSWORD=grafana-$(openssl rand -hex 8)

# Hardware Profile (auto, high-end, mid-range, budget)
HARDWARE_PROFILE=auto

# Model Preferences (will be auto-configured based on hardware)
PREFERRED_LLM=qwen2.5:32b
FALLBACK_LLM=mistral:7b-instruct
PREFERRED_SD_MODEL=sd_xl_base_1.0.safetensors

# Service URLs
OLLAMA_URL=http://ollama:11434
LOCALAI_URL=http://localai:8080
STABLE_DIFFUSION_URL=http://automatic1111:7860
COMFYUI_URL=http://comfyui:8188

# YouTube API (configure these after setup)
YOUTUBE_CLIENT_ID=your-youtube-client-id
YOUTUBE_CLIENT_SECRET=your-youtube-client-secret

# Google Sheets API (configure these after setup)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
ANALYTICS_SPREADSHEET_ID=your-spreadsheet-id

# Optional: Slack notifications
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
EOF
        log "Generated .env file with secure passwords"
    else
        log ".env file already exists, skipping generation"
    fi
    
    # Generate Prometheus configuration
    cat > monitoring/prometheus.yml << EOF
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'n8n'
    static_configs:
      - targets: ['n8n:5678']
  
  - job_name: 'model-manager'
    static_configs:
      - targets: ['model-manager:9000']
  
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['localhost:9100']
EOF
    
    # Generate Grafana datasource configuration
    cat > monitoring/grafana/datasources/prometheus.yml << EOF
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://monitor:9090
    isDefault: true
EOF
    
    log "Configuration files generated"
}

# Determine hardware profile
determine_hardware_profile() {
    log "Determining optimal hardware profile..."
    
    if [[ $TOTAL_MEM -ge 32 && "$HAS_GPU" == true ]]; then
        HARDWARE_PROFILE="high-end"
        PREFERRED_LLM="qwen2.5:32b"
        FALLBACK_LLM="llama3.2:70b"
        PREFERRED_SD_MODEL="sd_xl_base_1.0.safetensors"
    elif [[ $TOTAL_MEM -ge 16 && "$HAS_GPU" == true ]]; then
        HARDWARE_PROFILE="mid-range"
        PREFERRED_LLM="qwen2.5:32b"
        FALLBACK_LLM="mistral:7b-instruct"
        PREFERRED_SD_MODEL="deliberate_v2.safetensors"
    else
        HARDWARE_PROFILE="budget"
        PREFERRED_LLM="phi3:4b"
        FALLBACK_LLM="gemma2:9b"
        PREFERRED_SD_MODEL="dreamshaper_8.safetensors"
    fi
    
    # Update .env file with determined profile
    sed -i "s/HARDWARE_PROFILE=auto/HARDWARE_PROFILE=$HARDWARE_PROFILE/" .env
    sed -i "s/PREFERRED_LLM=qwen2.5:32b/PREFERRED_LLM=$PREFERRED_LLM/" .env
    sed -i "s/FALLBACK_LLM=mistral:7b-instruct/FALLBACK_LLM=$FALLBACK_LLM/" .env
    sed -i "s/PREFERRED_SD_MODEL=sd_xl_base_1.0.safetensors/PREFERRED_SD_MODEL=$PREFERRED_SD_MODEL/" .env
    
    log "Hardware profile: $HARDWARE_PROFILE"
    log "Preferred LLM: $PREFERRED_LLM"
    log "Preferred SD Model: $PREFERRED_SD_MODEL"
}

# Start services
start_services() {
    log "Starting local AI services..."
    
    # Pull latest images
    docker-compose -f docker-compose.local-ai.yml pull
    
    # Start services
    docker-compose -f docker-compose.local-ai.yml up -d
    
    log "Services started. Waiting for initialization..."
    
    # Wait for services to be ready
    sleep 30
    
    # Check service health
    check_service_health
}

# Check service health
check_service_health() {
    log "Checking service health..."
    
    local services=("n8n:5678/healthz" "model-manager:9000/health" "ollama:11434/api/tags")
    
    for service in "${services[@]}"; do
        local url="http://localhost:${service#*:}"
        local name="${service%%:*}"
        
        if curl -f -s "$url" > /dev/null; then
            log "✅ $name is healthy"
        else
            warn "⚠️  $name is not responding"
        fi
    done
}

# Create installation summary
create_summary() {
    log "Creating installation summary..."
    
    cat > INSTALLATION_SUMMARY.md << EOF
# 🎉 Local AI YouTube Automation - Installation Complete!

## 📊 System Configuration
- **Hardware Profile**: $HARDWARE_PROFILE
- **Total RAM**: ${TOTAL_MEM}GB
- **GPU Available**: $HAS_GPU
- **Preferred LLM**: $PREFERRED_LLM
- **Preferred SD Model**: $PREFERRED_SD_MODEL

## 🌐 Service URLs
- **N8N Workflow Engine**: http://localhost:5678
- **Model Manager**: http://localhost:9000
- **Grafana Dashboard**: http://localhost:3000
- **Ollama API**: http://localhost:11434
- **Stable Diffusion**: http://localhost:7860

## 🔑 Login Credentials
Check your .env file for generated passwords:
- **N8N**: admin / [N8N_PASSWORD]
- **Grafana**: admin / [GRAFANA_PASSWORD]

## 📋 Next Steps
1. **Install AI Models**: Run \`./install-models.sh --profile $HARDWARE_PROFILE\`
2. **Configure YouTube API**: Add your YouTube credentials to .env
3. **Import N8N Workflow**: Import advanced-youtube-automation-workflow.json
4. **Test the System**: Run the test suite to validate everything works

## 🆘 Troubleshooting
- **Check Logs**: \`docker-compose -f docker-compose.local-ai.yml logs [service]\`
- **Restart Services**: \`docker-compose -f docker-compose.local-ai.yml restart\`
- **View Documentation**: See LOCAL_AI_SETUP_GUIDE.md for detailed instructions

## 🎯 Ready to Generate Content!
Your local AI YouTube automation system is ready to create amazing content completely offline!
EOF
    
    log "Installation summary created: INSTALLATION_SUMMARY.md"
}

# Main installation function
main() {
    echo "🚀 Advanced YouTube Automation - Local AI Setup"
    echo "=============================================="
    echo
    
    check_root
    detect_os
    check_requirements
    install_docker
    install_docker_compose
    install_nvidia_docker
    create_directories
    generate_configs
    determine_hardware_profile
    start_services
    create_summary
    
    echo
    echo "🎉 Installation Complete!"
    echo "========================"
    echo
    echo "📋 Next steps:"
    echo "1. Install AI models: ./install-models.sh --profile $HARDWARE_PROFILE"
    echo "2. Configure YouTube API credentials in .env file"
    echo "3. Access N8N at: http://localhost:5678"
    echo "4. Import the workflow: advanced-youtube-automation-workflow.json"
    echo
    echo "📖 For detailed instructions, see: LOCAL_AI_SETUP_GUIDE.md"
    echo "📊 For system monitoring, see: INSTALLATION_SUMMARY.md"
    echo
    echo "🚀 Happy automating with local AI!"
}

# Run main function
main "$@"

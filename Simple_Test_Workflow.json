{"name": "🧪 Simple AI Test - raghavrsg8125", "nodes": [{"parameters": {}, "id": "manual-trigger-123", "name": "▶️ Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"mode": "runOnceForAllItems", "jsCode": "// Simple topic generator\nconst topics = [\n  'AI Revolution 2025',\n  'LM Studio Tutorial', \n  'Free AI Tools',\n  'YouTube Automation'\n];\n\nconst randomTopic = topics[Math.floor(Math.random() * topics.length)];\n\nreturn [{\n  json: {\n    topic: randomTopic,\n    timestamp: new Date().toISOString(),\n    channel: 'raghavrsg8125'\n  }\n}];"}, "id": "topic-generator-456", "name": "🎯 Topic Generator", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"url": "http://localhost:1234/v1/chat/completions", "method": "POST", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "model", "value": "mistralai/mistral-7b-instruct-v0.3"}, {"name": "messages", "value": "=[{\"role\":\"user\",\"content\":\"Create a YouTube video title and description for: {{ $json.topic }}. Make it engaging for raghavrsg8125 channel.\"}]"}, {"name": "temperature", "value": 0.7}, {"name": "max_tokens", "value": 200}]}}, "id": "ai-generator-789", "name": "🤖 AI Generator", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [680, 300]}, {"parameters": {"fromEmail": "<EMAIL>", "toEmail": "<EMAIL>", "subject": "🧪 AI Test Result", "message": "Topic: {{ $('🎯 Topic Generator').item.json.topic }}\\n\\nAI Response:\\n{{ $json.choices[0].message.content }}\\n\\nTest completed successfully! 🚀", "options": {}}, "id": "email-result-012", "name": "📧 <PERSON><PERSON>t", "type": "n8n-nodes-base.emailSend", "typeVersion": 2.1, "position": [900, 300]}], "connections": {"▶️ Manual Trigger": {"main": [[{"node": "🎯 Topic Generator", "type": "main", "index": 0}]]}, "🎯 Topic Generator": {"main": [[{"node": "🤖 AI Generator", "type": "main", "index": 0}]]}, "🤖 AI Generator": {"main": [[{"node": "📧 <PERSON><PERSON>t", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 0, "updatedAt": "2025-01-15T12:00:00.000Z", "versionId": "1"}
{"name": "🚀 AI Video Empire - raghavrsg8125 (FIXED)", "nodes": [{"parameters": {"rule": {"interval": [{"field": "cronExpression", "expression": "0 */2 * * *"}]}}, "id": "f8b5c8e1-1234-4567-8901-************", "name": "🎯 Master Control Center", "type": "n8n-nodes-base.cron", "typeVersion": 1.1, "position": [240, 300]}, {"parameters": {"mode": "runOnceForAllItems", "jsCode": "// VIRAL PREDICTION ENGINE\nconst viralTopics = [\n  {\n    topic: 'AI Tools Revolution 2025',\n    viralScore: 85,\n    targetAudience: 'tech-enthusiasts',\n    keywords: ['AI', 'tools', '2025', 'revolution']\n  },\n  {\n    topic: 'LM Studio Complete Guide',\n    viralScore: 78,\n    targetAudience: 'developers',\n    keywords: ['LM Studio', 'local AI', 'guide']\n  },\n  {\n    topic: 'Free AI Automation Setup',\n    viralScore: 92,\n    targetAudience: 'entrepreneurs',\n    keywords: ['free', 'AI', 'automation', 'setup']\n  }\n];\n\n// Select highest scoring topic\nconst bestTopic = viralTopics.reduce((prev, current) => \n  (prev.viralScore > current.viralScore) ? prev : current\n);\n\nreturn [{ json: bestTopic }];"}, "id": "a1b2c3d4-**************-************", "name": "🔮 Viral Prediction Engine", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"url": "http://localhost:1234/v1/chat/completions", "method": "POST", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "Authorization", "value": "Bearer lm-studio"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "model", "value": "mistralai/mistral-7b-instruct-v0.3"}, {"name": "messages", "value": "=[{\"role\":\"system\",\"content\":\"You are an AI Content Director for raghavrsg8125 YouTube channel. Create viral video concepts with professional production planning.\"},{\"role\":\"user\",\"content\":\"Create a complete video production plan for: {{ $json.topic }}\\n\\nViral Score: {{ $json.viralScore }}\\nTarget: {{ $json.targetAudience }}\\n\\nGenerate a JSON response with:\\n1. title (60 chars max)\\n2. description (200 chars)\\n3. script (500 words)\\n4. thumbnailConcept\\n5. seoKeywords (array)\\n6. estimatedDuration (minutes)\"}]"}, {"name": "temperature", "value": 0.8}, {"name": "max_tokens", "value": 2000}]}}, "id": "b2c3d4e5-6789-0123-4567-890123456789", "name": "🎬 AI Content Director", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [680, 300]}, {"parameters": {"assignments": {"assignments": [{"id": "video-content", "name": "videoContent", "type": "object", "value": "={{ JSON.parse($json.choices[0].message.content) }}"}, {"id": "final-title", "name": "finalTitle", "type": "string", "value": "={{ JSON.parse($json.choices[0].message.content).title || $('🔮 Viral Prediction Engine').item.json.topic }}"}, {"id": "final-script", "name": "finalScript", "type": "string", "value": "={{ JSON.parse($json.choices[0].message.content).script || 'Welcome to raghavrsg8125! Today we explore ' + $('🔮 Viral Prediction Engine').item.json.topic + '. This revolutionary topic will change how you think about AI and automation. Subscribe for more amazing content!' }}"}]}}, "id": "c3d4e5f6-7890-1234-5678-901234567890", "name": "📝 Script Processor", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [900, 300]}, {"parameters": {"url": "https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM", "method": "POST", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "audio/mpeg"}, {"name": "Content-Type", "value": "application/json"}, {"name": "xi-api-key", "value": "demo-key"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "text", "value": "={{ $json.finalScript }}"}, {"name": "model_id", "value": "eleven_multilingual_v2"}, {"name": "voice_settings", "value": "{\"stability\": 0.7, \"similarity_boost\": 0.8}"}]}, "options": {"response": {"response": {"responseFormat": "file"}}}}, "id": "d4e5f6g7-8901-2345-6789-012345678901", "name": "🎙️ AI Voice Director", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1120, 200]}, {"parameters": {"url": "http://localhost:7860/sdapi/v1/txt2img", "method": "POST", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "prompt", "value": "={{ $json.videoContent?.thumbnailConcept || $json.finalTitle + ', professional YouTube thumbnail, high contrast, eye-catching, 1920x1080, vibrant colors, tech aesthetic' }}"}, {"name": "negative_prompt", "value": "blurry, low quality, text, watermark"}, {"name": "width", "value": 1920}, {"name": "height", "value": 1080}, {"name": "steps", "value": 20}]}}, "id": "e5f6g7h8-9012-3456-7890-123456789012", "name": "🎨 Visual AI Engine", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1120, 400]}, {"parameters": {"mode": "runOnceForAllItems", "jsCode": "// SIMPLE VIDEO CREATION SIMULATION\n// In production, this would call the Python script\n\nconst videoData = {\n  title: $('📝 Script Processor').item.json.finalTitle,\n  script: $('📝 Script Processor').item.json.finalScript,\n  duration: '5:30',\n  status: 'created',\n  outputPath: '/tmp/ai_video_' + Date.now() + '.mp4',\n  thumbnail: 'generated',\n  audio: 'synthesized'\n};\n\n// Simulate video creation success\nreturn [{ json: videoData }];"}, "id": "f6g7h8i9-0123-4567-8901-************", "name": "🎬 Video Production Engine", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 300]}, {"parameters": {"fromEmail": "<EMAIL>", "toEmail": "<EMAIL>", "subject": "🎬 AI Video Created Successfully!", "message": "New video created for raghavrsg8125!\\n\\nTitle: {{ $json.title }}\\nDuration: {{ $json.duration }}\\nStatus: {{ $json.status }}\\n\\nYour AI Video Empire is working! 🚀", "options": {}}, "id": "g7h8i9j0-1234-**************78901234", "name": "🎉 Success Notification", "type": "n8n-nodes-base.emailSend", "typeVersion": 2.1, "position": [1560, 300]}], "connections": {"🎯 Master Control Center": {"main": [[{"node": "🔮 Viral Prediction Engine", "type": "main", "index": 0}]]}, "🔮 Viral Prediction Engine": {"main": [[{"node": "🎬 AI Content Director", "type": "main", "index": 0}]]}, "🎬 AI Content Director": {"main": [[{"node": "📝 Script Processor", "type": "main", "index": 0}]]}, "📝 Script Processor": {"main": [[{"node": "🎙️ AI Voice Director", "type": "main", "index": 0}, {"node": "🎨 Visual AI Engine", "type": "main", "index": 0}]]}, "🎙️ AI Voice Director": {"main": [[{"node": "🎬 Video Production Engine", "type": "main", "index": 0}]]}, "🎨 Visual AI Engine": {"main": [[{"node": "🎬 Video Production Engine", "type": "main", "index": 0}]]}, "🎬 Video Production Engine": {"main": [[{"node": "🎉 Success Notification", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 0, "updatedAt": "2025-01-15T12:00:00.000Z", "versionId": "1"}
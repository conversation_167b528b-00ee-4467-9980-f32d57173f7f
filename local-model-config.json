{"name": "Local AI Model Configuration System", "version": "2.0.0-local", "description": "Configuration management for local AI models in YouTube automation", "configurations": {"hardware_profiles": {"high_end": {"name": "High-End Workstation", "requirements": {"ram_gb": 32, "gpu_vram_gb": 12, "storage_gb": 200, "cpu_cores": 8}, "recommended_models": {"llm": [{"name": "qwen2.5:32b", "priority": 1, "use_case": "Primary content generation", "context_size": 8192, "gpu_layers": -1}, {"name": "llama3.2:70b", "priority": 2, "use_case": "Advanced reasoning tasks", "context_size": 4096, "gpu_layers": -1}], "image": [{"name": "sd_xl_base_1.0.safetensors", "priority": 1, "use_case": "High-quality thumbnails", "settings": {"steps": 30, "cfg_scale": 7, "width": 1280, "height": 720}}]}, "optimization": {"ollama_settings": {"num_ctx": 8192, "num_gpu": -1, "num_thread": 8, "repeat_penalty": 1.1, "temperature": 0.8, "top_p": 0.9}, "sd_settings": {"enable_xformers": true, "enable_attention_slicing": true, "enable_cpu_offload": false, "precision": "fp16"}}}, "mid_range": {"name": "Mid-Range System", "requirements": {"ram_gb": 16, "gpu_vram_gb": 8, "storage_gb": 100, "cpu_cores": 6}, "recommended_models": {"llm": [{"name": "qwen2.5:32b", "priority": 1, "use_case": "Primary content generation", "context_size": 4096, "gpu_layers": 20}, {"name": "mistral:7b-instruct", "priority": 2, "use_case": "Fast content generation", "context_size": 4096, "gpu_layers": -1}], "image": [{"name": "deliberate_v2.safetensors", "priority": 1, "use_case": "Balanced quality thumbnails", "settings": {"steps": 25, "cfg_scale": 7, "width": 1280, "height": 720}}]}, "optimization": {"ollama_settings": {"num_ctx": 4096, "num_gpu": 20, "num_thread": 6, "repeat_penalty": 1.1, "temperature": 0.8, "top_p": 0.9}, "sd_settings": {"enable_xformers": true, "enable_attention_slicing": true, "enable_cpu_offload": true, "precision": "fp16"}}}, "budget": {"name": "Budget/Low-End System", "requirements": {"ram_gb": 8, "gpu_vram_gb": 4, "storage_gb": 50, "cpu_cores": 4}, "recommended_models": {"llm": [{"name": "phi3:4b", "priority": 1, "use_case": "Lightweight content generation", "context_size": 2048, "gpu_layers": 10}, {"name": "gemma2:9b", "priority": 2, "use_case": "Alternative lightweight model", "context_size": 2048, "gpu_layers": 15}], "image": [{"name": "dreamshaper_8.safetensors", "priority": 1, "use_case": "Efficient thumbnails", "settings": {"steps": 20, "cfg_scale": 6, "width": 1024, "height": 576}}]}, "optimization": {"ollama_settings": {"num_ctx": 2048, "num_gpu": 10, "num_thread": 4, "repeat_penalty": 1.1, "temperature": 0.8, "top_p": 0.9}, "sd_settings": {"enable_xformers": false, "enable_attention_slicing": true, "enable_cpu_offload": true, "precision": "fp32"}}}}, "model_endpoints": {"ollama": {"base_url": "http://localhost:11434", "api_version": "v1", "endpoints": {"generate": "/api/generate", "chat": "/api/chat", "models": "/api/tags", "pull": "/api/pull", "delete": "/api/delete", "show": "/api/show"}, "default_params": {"stream": false, "format": "json", "keep_alive": "5m"}}, "localai": {"base_url": "http://localhost:8080", "api_version": "v1", "endpoints": {"chat": "/v1/chat/completions", "completions": "/v1/completions", "models": "/v1/models", "embeddings": "/v1/embeddings"}, "default_params": {"stream": false, "max_tokens": 4000, "temperature": 0.8}}, "stable_diffusion": {"automatic1111": {"base_url": "http://localhost:7860", "endpoints": {"txt2img": "/sdapi/v1/txt2img", "img2img": "/sdapi/v1/img2img", "models": "/sdapi/v1/sd-models", "samplers": "/sdapi/v1/samplers", "progress": "/sdapi/v1/progress"}}, "comfyui": {"base_url": "http://localhost:8188", "endpoints": {"prompt": "/prompt", "queue": "/queue", "history": "/history", "view": "/view", "system_stats": "/system_stats"}}}}, "content_strategies": {"viral_30s": {"model_selection": {"primary": "qwen2.5:32b", "fallback": ["mistral:7b-instruct", "phi3:4b"], "reasoning": "High-quality model for viral content creation"}, "generation_params": {"temperature": 0.9, "top_p": 0.95, "max_tokens": 2000, "context_size": 4096}, "prompt_template": {"system": "You are a viral content creator specializing in 30-second YouTube videos. Create highly engaging, shareable content optimized for maximum viral potential.", "user_template": "Create a viral 30-second video about {topic} with these requirements: {requirements}"}}, "educational_60s": {"model_selection": {"primary": "qwen2.5:32b", "fallback": ["llama3.2:70b", "mistral:7b-instruct"], "reasoning": "Detailed model for educational content"}, "generation_params": {"temperature": 0.7, "top_p": 0.9, "max_tokens": 3000, "context_size": 6144}, "prompt_template": {"system": "You are an educational content creator. Create clear, informative, and engaging educational videos that teach valuable skills and knowledge.", "user_template": "Create an educational 60-second video about {topic} that teaches {learning_objectives}"}}, "storytelling_90s": {"model_selection": {"primary": "llama3.2:70b", "fallback": ["qwen2.5:32b", "mistral:7b-instruct"], "reasoning": "Large context model for narrative development"}, "generation_params": {"temperature": 0.8, "top_p": 0.92, "max_tokens": 4000, "context_size": 8192}, "prompt_template": {"system": "You are a master storyteller. Create compelling narratives with strong emotional arcs, engaging characters, and satisfying resolutions.", "user_template": "Create a 90-second story about {theme} with {emotional_tone} that includes {story_elements}"}}}, "quality_gates": {"content_validation": {"min_content_score": 75, "required_elements": ["hook", "main_content", "call_to_action"], "max_generation_time": 120, "fallback_strategy": "retry_with_simpler_model"}, "image_validation": {"min_resolution": [1024, 576], "max_file_size_mb": 10, "required_formats": ["png", "jpg", "jpeg"], "max_generation_time": 300, "fallback_strategy": "use_cached_template"}, "system_health": {"max_memory_usage": 0.9, "max_gpu_usage": 0.95, "min_disk_space_gb": 10, "max_response_time": 180}}, "monitoring": {"metrics": {"generation_time": {"target": 60, "warning": 120, "critical": 300}, "memory_usage": {"target": 0.7, "warning": 0.85, "critical": 0.95}, "gpu_utilization": {"target": 0.8, "warning": 0.9, "critical": 0.98}, "success_rate": {"target": 0.95, "warning": 0.9, "critical": 0.8}}, "alerts": {"high_memory": "Memory usage above 85%", "slow_generation": "Generation time above 2 minutes", "model_failure": "Model generation failed", "disk_space_low": "Available disk space below 10GB"}}, "auto_optimization": {"enabled": true, "strategies": {"model_switching": {"enabled": true, "criteria": {"high_load": "switch_to_smaller_model", "low_quality": "switch_to_larger_model", "timeout": "switch_to_faster_model"}}, "parameter_tuning": {"enabled": true, "adaptive_temperature": true, "adaptive_context_size": true, "adaptive_gpu_layers": true}, "caching": {"enabled": true, "cache_successful_prompts": true, "cache_generated_images": true, "cache_duration_hours": 24}}}, "backup_and_recovery": {"model_backup": {"enabled": true, "backup_path": "/backups/models", "schedule": "daily", "retention_days": 7}, "config_backup": {"enabled": true, "backup_path": "/backups/configs", "schedule": "hourly", "retention_hours": 168}, "recovery_strategies": {"model_corruption": "restore_from_backup", "service_failure": "restart_with_fallback", "disk_full": "cleanup_old_files"}}}, "installation_scripts": {"ollama_models": [{"name": "qwen2.5:32b", "command": "ollama pull qwen2.5:32b", "size": "19GB", "estimated_time": "30-60 minutes"}, {"name": "mistral:7b-instruct", "command": "ollama pull mistral:7b-instruct", "size": "4.1GB", "estimated_time": "10-20 minutes"}, {"name": "phi3:4b", "command": "ollama pull phi3:4b", "size": "2.2GB", "estimated_time": "5-10 minutes"}], "sd_models": [{"name": "SDXL Base", "url": "https://huggingface.co/stabilityai/stable-diffusion-xl-base-1.0/resolve/main/sd_xl_base_1.0.safetensors", "path": "/sd-models/Stable-diffusion/sd_xl_base_1.0.safetensors", "size": "6.9GB"}, {"name": "Deliberate v2", "url": "https://huggingface.co/XpucT/Deliberate/resolve/main/Deliberate_v2.safetensors", "path": "/sd-models/Stable-diffusion/deliberate_v2.safetensors", "size": "4.3GB"}]}}
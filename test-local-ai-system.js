#!/usr/bin/env node

/**
 * Local AI YouTube Automation System - Comprehensive Test Suite
 * Tests all local AI components and integration points
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

class LocalAISystemTester {
  constructor() {
    this.config = {
      ollamaUrl: process.env.OLLAMA_URL || 'http://localhost:11434',
      sdUrl: process.env.STABLE_DIFFUSION_URL || 'http://localhost:7860',
      comfyUIUrl: process.env.COMFYUI_URL || 'http://localhost:8188',
      n8nUrl: process.env.N8N_URL || 'http://localhost:5678',
      modelManagerUrl: process.env.MODEL_MANAGER_URL || 'http://localhost:9000'
    };
    
    this.testResults = {
      passed: 0,
      failed: 0,
      total: 0,
      details: []
    };
  }

  async runAllTests() {
    console.log('🧪 Starting Local AI System Tests\n');
    
    try {
      await this.testSystemHealth();
      await this.testLocalLLMGeneration();
      await this.testLocalImageGeneration();
      await this.testN8NWorkflow();
      await this.testModelManager();
      await this.testPerformance();
      await this.testOfflineCapability();
      
      this.generateTestReport();
    } catch (error) {
      console.error('❌ Test suite failed:', error.message);
      process.exit(1);
    }
  }

  async testSystemHealth() {
    console.log('🏥 Testing System Health...');
    
    // Test Docker services
    await this.runTest('Docker Services Running', async () => {
      const { exec } = require('child_process');
      return new Promise((resolve) => {
        exec('docker-compose -f docker-compose.local-ai.yml ps', (error, stdout) => {
          if (error) resolve(false);
          resolve(stdout.includes('Up') && !stdout.includes('Exit'));
        });
      });
    });

    // Test Ollama service
    await this.runTest('Ollama Service Health', async () => {
      try {
        const response = await axios.get(`${this.config.ollamaUrl}/api/tags`, {
          timeout: 10000
        });
        return response.status === 200 && response.data.models;
      } catch (error) {
        console.log('Ollama Error:', error.message);
        return false;
      }
    });

    // Test Stable Diffusion service
    await this.runTest('Stable Diffusion Service Health', async () => {
      try {
        const response = await axios.get(`${this.config.sdUrl}/sdapi/v1/sd-models`, {
          timeout: 10000
        });
        return response.status === 200 && Array.isArray(response.data);
      } catch (error) {
        console.log('Stable Diffusion Error:', error.message);
        return false;
      }
    });

    // Test N8N service
    await this.runTest('N8N Service Health', async () => {
      try {
        const response = await axios.get(`${this.config.n8nUrl}/healthz`, {
          timeout: 5000
        });
        return response.status === 200;
      } catch (error) {
        console.log('N8N Error:', error.message);
        return false;
      }
    });

    // Test Model Manager service
    await this.runTest('Model Manager Service Health', async () => {
      try {
        const response = await axios.get(`${this.config.modelManagerUrl}/health`, {
          timeout: 5000
        });
        return response.status === 200;
      } catch (error) {
        console.log('Model Manager Error:', error.message);
        return false;
      }
    });

    console.log('✅ System Health Tests Completed\n');
  }

  async testLocalLLMGeneration() {
    console.log('🧠 Testing Local LLM Generation...');

    // Test Ollama model availability
    await this.runTest('Ollama Models Available', async () => {
      try {
        const response = await axios.get(`${this.config.ollamaUrl}/api/tags`);
        const models = response.data.models || [];
        return models.length > 0;
      } catch (error) {
        return false;
      }
    });

    // Test content generation
    await this.runTest('Local Content Generation', async () => {
      try {
        const testPrompt = 'Generate a brief YouTube video title about AI automation. Respond with just the title.';
        
        const response = await axios.post(`${this.config.ollamaUrl}/api/generate`, {
          model: 'phi3:4b', // Use smallest model for testing
          prompt: testPrompt,
          stream: false,
          options: {
            temperature: 0.7,
            num_predict: 100
          }
        }, {
          timeout: 60000 // 1 minute timeout
        });

        const content = response.data.response;
        return content && content.length > 10 && content.toLowerCase().includes('ai');
      } catch (error) {
        console.log('Content Generation Error:', error.message);
        return false;
      }
    });

    // Test model performance
    await this.runTest('LLM Response Time', async () => {
      try {
        const startTime = Date.now();
        
        await axios.post(`${this.config.ollamaUrl}/api/generate`, {
          model: 'phi3:4b',
          prompt: 'Hello, respond with "OK"',
          stream: false,
          options: { num_predict: 10 }
        }, {
          timeout: 30000
        });

        const responseTime = Date.now() - startTime;
        console.log(`    Response time: ${responseTime}ms`);
        
        return responseTime < 30000; // Should respond within 30 seconds
      } catch (error) {
        return false;
      }
    });

    console.log('✅ Local LLM Generation Tests Completed\n');
  }

  async testLocalImageGeneration() {
    console.log('🎨 Testing Local Image Generation...');

    // Test Stable Diffusion models
    await this.runTest('SD Models Available', async () => {
      try {
        const response = await axios.get(`${this.config.sdUrl}/sdapi/v1/sd-models`);
        return Array.isArray(response.data) && response.data.length > 0;
      } catch (error) {
        return false;
      }
    });

    // Test image generation
    await this.runTest('Local Image Generation', async () => {
      try {
        const testPayload = {
          prompt: 'simple red circle on white background, minimalist',
          negative_prompt: 'complex, detailed, text',
          width: 512,
          height: 512,
          steps: 10,
          cfg_scale: 7,
          sampler_name: 'Euler a',
          batch_size: 1,
          n_iter: 1
        };

        const response = await axios.post(`${this.config.sdUrl}/sdapi/v1/txt2img`, testPayload, {
          timeout: 120000 // 2 minutes timeout
        });

        return response.data.images && response.data.images.length > 0;
      } catch (error) {
        console.log('Image Generation Error:', error.message);
        return false;
      }
    });

    // Test image generation performance
    await this.runTest('Image Generation Performance', async () => {
      try {
        const startTime = Date.now();
        
        const quickPayload = {
          prompt: 'test',
          width: 256,
          height: 256,
          steps: 5,
          cfg_scale: 5
        };

        await axios.post(`${this.config.sdUrl}/sdapi/v1/txt2img`, quickPayload, {
          timeout: 60000
        });

        const responseTime = Date.now() - startTime;
        console.log(`    Image generation time: ${responseTime}ms`);
        
        return responseTime < 60000; // Should generate within 1 minute for simple image
      } catch (error) {
        return false;
      }
    });

    console.log('✅ Local Image Generation Tests Completed\n');
  }

  async testN8NWorkflow() {
    console.log('⚙️ Testing N8N Workflow Integration...');

    // Test workflow import
    await this.runTest('Workflow File Exists', () => {
      return fs.existsSync('advanced-youtube-automation-workflow.json');
    });

    // Test workflow structure
    await this.runTest('Workflow Structure Valid', () => {
      try {
        const workflowContent = fs.readFileSync('advanced-youtube-automation-workflow.json', 'utf8');
        const workflow = JSON.parse(workflowContent);
        
        const requiredNodes = [
          'Local AI Content Generator',
          'Local Image Generation',
          'Market Strategy Thinker',
          'Video Production System'
        ];
        
        return requiredNodes.every(nodeName => 
          workflow.nodes.some(node => node.name.includes(nodeName.split(' ')[0]))
        );
      } catch (error) {
        return false;
      }
    });

    // Test local AI integration
    await this.runTest('Local AI Nodes Configuration', () => {
      try {
        const workflowContent = fs.readFileSync('advanced-youtube-automation-workflow.json', 'utf8');
        const workflow = JSON.parse(workflowContent);
        
        // Check for local AI references
        const workflowString = JSON.stringify(workflow);
        return workflowString.includes('localhost:11434') && 
               workflowString.includes('localhost:7860') &&
               !workflowString.includes('openrouter.ai') &&
               !workflowString.includes('replicate.com');
      } catch (error) {
        return false;
      }
    });

    console.log('✅ N8N Workflow Tests Completed\n');
  }

  async testModelManager() {
    console.log('🎛️ Testing Model Manager...');

    // Test model manager API
    await this.runTest('Model Manager API', async () => {
      try {
        const response = await axios.get(`${this.config.modelManagerUrl}/api/status`);
        return response.status === 200 && response.data.success;
      } catch (error) {
        return false;
      }
    });

    // Test model recommendations
    await this.runTest('Hardware Recommendations', async () => {
      try {
        const response = await axios.get(`${this.config.modelManagerUrl}/api/recommendations`);
        return response.status === 200 && 
               response.data.success && 
               response.data.data.models;
      } catch (error) {
        return false;
      }
    });

    // Test model listing
    await this.runTest('Model Inventory', async () => {
      try {
        const response = await axios.get(`${this.config.modelManagerUrl}/api/models`);
        return response.status === 200 && 
               response.data.success && 
               response.data.data;
      } catch (error) {
        return false;
      }
    });

    console.log('✅ Model Manager Tests Completed\n');
  }

  async testPerformance() {
    console.log('⚡ Testing Performance...');

    // Test memory usage
    await this.runTest('Memory Usage Acceptable', async () => {
      try {
        const { exec } = require('child_process');
        return new Promise((resolve) => {
          exec('free | grep Mem', (error, stdout) => {
            if (error) resolve(false);
            
            const memInfo = stdout.split(/\s+/);
            const total = parseInt(memInfo[1]);
            const used = parseInt(memInfo[2]);
            const usage = (used / total) * 100;
            
            console.log(`    Memory usage: ${usage.toFixed(1)}%`);
            resolve(usage < 90); // Should use less than 90% of memory
          });
        });
      } catch (error) {
        return false;
      }
    });

    // Test disk usage
    await this.runTest('Disk Space Sufficient', () => {
      try {
        const { execSync } = require('child_process');
        const output = execSync('df -h .').toString();
        const lines = output.split('\n');
        const dataLine = lines[1].split(/\s+/);
        const usage = parseInt(dataLine[4].replace('%', ''));
        
        console.log(`    Disk usage: ${usage}%`);
        return usage < 90; // Should use less than 90% of disk
      } catch (error) {
        return false;
      }
    });

    // Test GPU utilization (if available)
    await this.runTest('GPU Status', async () => {
      try {
        const { exec } = require('child_process');
        return new Promise((resolve) => {
          exec('nvidia-smi --query-gpu=utilization.gpu --format=csv,noheader,nounits', (error, stdout) => {
            if (error) {
              console.log('    No NVIDIA GPU detected (CPU-only mode)');
              resolve(true); // Not an error if no GPU
            } else {
              const utilization = parseInt(stdout.trim());
              console.log(`    GPU utilization: ${utilization}%`);
              resolve(utilization >= 0); // Any valid reading is good
            }
          });
        });
      } catch (error) {
        return true; // Not critical if GPU check fails
      }
    });

    console.log('✅ Performance Tests Completed\n');
  }

  async testOfflineCapability() {
    console.log('🌐 Testing Offline Capability...');

    // Test that system works without external internet
    await this.runTest('No External API Dependencies', () => {
      const workflowContent = fs.readFileSync('advanced-youtube-automation-workflow.json', 'utf8');
      
      // Check for external API calls (should only have YouTube and Google Sheets)
      const externalAPIs = [
        'openrouter.ai',
        'replicate.com',
        'openai.com',
        'anthropic.com'
      ];
      
      return !externalAPIs.some(api => workflowContent.includes(api));
    });

    // Test local model accessibility
    await this.runTest('Local Models Accessible', async () => {
      try {
        // Test Ollama
        const ollamaResponse = await axios.get(`${this.config.ollamaUrl}/api/tags`, {
          timeout: 5000
        });
        
        // Test Stable Diffusion
        const sdResponse = await axios.get(`${this.config.sdUrl}/sdapi/v1/sd-models`, {
          timeout: 5000
        });
        
        return ollamaResponse.status === 200 && sdResponse.status === 200;
      } catch (error) {
        return false;
      }
    });

    // Test configuration files
    await this.runTest('Local Configuration Valid', () => {
      const requiredFiles = [
        '.env',
        'docker-compose.local-ai.yml',
        'local-model-config.json'
      ];
      
      return requiredFiles.every(file => fs.existsSync(file));
    });

    console.log('✅ Offline Capability Tests Completed\n');
  }

  async runTest(testName, testFunction) {
    this.testResults.total++;
    
    try {
      const result = await testFunction();
      if (result) {
        console.log(`  ✅ ${testName}`);
        this.testResults.passed++;
        this.testResults.details.push({ name: testName, status: 'PASSED' });
      } else {
        console.log(`  ❌ ${testName}`);
        this.testResults.failed++;
        this.testResults.details.push({ name: testName, status: 'FAILED' });
      }
    } catch (error) {
      console.log(`  ❌ ${testName} - Error: ${error.message}`);
      this.testResults.failed++;
      this.testResults.details.push({ 
        name: testName, 
        status: 'ERROR', 
        error: error.message 
      });
    }
  }

  generateTestReport() {
    console.log('\n📊 Local AI System Test Results');
    console.log('================================');
    console.log(`Total Tests: ${this.testResults.total}`);
    console.log(`Passed: ${this.testResults.passed} ✅`);
    console.log(`Failed: ${this.testResults.failed} ❌`);
    console.log(`Success Rate: ${((this.testResults.passed / this.testResults.total) * 100).toFixed(1)}%`);

    if (this.testResults.failed > 0) {
      console.log('\n❌ Failed Tests:');
      this.testResults.details
        .filter(test => test.status !== 'PASSED')
        .forEach(test => {
          console.log(`  - ${test.name} (${test.status})`);
          if (test.error) {
            console.log(`    Error: ${test.error}`);
          }
        });
    }

    // Save detailed report
    const report = {
      timestamp: new Date().toISOString(),
      system: 'Local AI YouTube Automation',
      summary: {
        total: this.testResults.total,
        passed: this.testResults.passed,
        failed: this.testResults.failed,
        successRate: (this.testResults.passed / this.testResults.total) * 100
      },
      details: this.testResults.details,
      configuration: this.config
    };

    fs.writeFileSync('local-ai-test-report.json', JSON.stringify(report, null, 2));
    console.log('\n📄 Detailed report saved to local-ai-test-report.json');

    if (this.testResults.failed === 0) {
      console.log('\n🎉 All tests passed! Your Local AI YouTube Automation System is ready for production.');
      console.log('\n🚀 Next steps:');
      console.log('1. Configure YouTube API credentials in .env');
      console.log('2. Import the workflow into N8N');
      console.log('3. Start creating amazing content with local AI!');
    } else {
      console.log('\n⚠️  Some tests failed. Please review the issues above before using the system.');
      process.exit(1);
    }
  }
}

// Run tests if called directly
if (require.main === module) {
  const tester = new LocalAISystemTester();
  tester.runAllTests().catch(error => {
    console.error('Test suite failed:', error);
    process.exit(1);
  });
}

module.exports = LocalAISystemTester;

{"name": "Advanced Video Production Nodes", "description": "Professional-grade video production components with industry-standard features", "nodes": {"duration_optimizer": {"name": "⏱️ Duration Optimizer", "type": "n8n-nodes-base.code", "code": "// ADVANCED DURATION OPTIMIZATION SYSTEM\n// Intelligently determines optimal video duration based on content and market conditions\n\nconst durationOptimizer = {\n  analyzeOptimalDuration(content, marketData) {\n    const factors = {\n      contentComplexity: this.assessContentComplexity(content),\n      viralPotential: this.assessViralPotential(content),\n      competitionLevel: marketData.competitionLevel,\n      audienceAttention: this.getAudienceAttentionSpan(marketData),\n      platformOptimization: this.getPlatformOptimization()\n    };\n    \n    return this.calculateOptimalDuration(factors);\n  },\n  \n  assessContentComplexity(content) {\n    const script = content.videoScript || '';\n    const wordCount = script.split(' ').length;\n    const conceptCount = (script.match(/\\b(how|why|what|when|where)\\b/gi) || []).length;\n    const technicalTerms = (script.match(/\\b[A-Z]{2,}\\b/g) || []).length;\n    \n    return Math.min(10, (wordCount / 50) + (conceptCount * 2) + technicalTerms);\n  },\n  \n  assessViralPotential(content) {\n    const viralKeywords = ['secret', 'shocking', 'amazing', 'instant', 'viral', 'trending'];\n    const title = (content.videoTitle || '').toLowerCase();\n    const script = (content.videoScript || '').toLowerCase();\n    \n    let score = 0;\n    viralKeywords.forEach(keyword => {\n      if (title.includes(keyword)) score += 2;\n      if (script.includes(keyword)) score += 1;\n    });\n    \n    return Math.min(10, score);\n  },\n  \n  getAudienceAttentionSpan(marketData) {\n    const timeOfDay = new Date().getHours();\n    const baseAttention = 45; // seconds\n    \n    // Attention varies by time of day\n    if (timeOfDay >= 9 && timeOfDay <= 17) return baseAttention * 0.8; // Work hours\n    if (timeOfDay >= 18 && timeOfDay <= 22) return baseAttention * 1.2; // Prime time\n    return baseAttention; // Other times\n  },\n  \n  getPlatformOptimization() {\n    return {\n      shorts: { min: 15, max: 60, optimal: 30 },\n      regular: { min: 60, max: 600, optimal: 180 },\n      long_form: { min: 600, max: 3600, optimal: 900 }\n    };\n  },\n  \n  calculateOptimalDuration(factors) {\n    let baseDuration = 60; // Default 60 seconds\n    \n    // Adjust based on complexity\n    if (factors.contentComplexity > 7) baseDuration = 90;\n    if (factors.contentComplexity < 4) baseDuration = 30;\n    \n    // Adjust based on viral potential\n    if (factors.viralPotential > 8) baseDuration = Math.min(baseDuration, 30);\n    \n    // Adjust based on competition\n    if (factors.competitionLevel === 'high') baseDuration = Math.min(baseDuration, 45);\n    \n    // Adjust based on audience attention\n    baseDuration = Math.min(baseDuration, factors.audienceAttention);\n    \n    return {\n      recommendedDuration: baseDuration,\n      reasoning: this.generateReasoning(factors, baseDuration),\n      alternatives: {\n        short: Math.max(15, baseDuration - 15),\n        medium: baseDuration,\n        long: Math.min(90, baseDuration + 15)\n      }\n    };\n  },\n  \n  generateReasoning(factors, duration) {\n    const reasons = [];\n    \n    if (factors.contentComplexity > 7) {\n      reasons.push('Complex content requires longer explanation');\n    }\n    if (factors.viralPotential > 8) {\n      reasons.push('High viral potential optimized for short format');\n    }\n    if (factors.competitionLevel === 'high') {\n      reasons.push('High competition requires concise, impactful content');\n    }\n    \n    return reasons.join('; ');\n  }\n};\n\nconst contentData = $input.first().json;\nconst marketData = $('🧠 Market Strategy Thinker').item.json.marketAnalysis;\n\nconst optimization = durationOptimizer.analyzeOptimalDuration(contentData, marketData);\n\nreturn {\n  json: {\n    ...contentData,\n    durationOptimization: optimization,\n    finalDuration: optimization.recommendedDuration,\n    optimizationReason: optimization.reasoning\n  }\n};"}, "visual_effects_generator": {"name": "🎨 Visual Effects Generator", "type": "n8n-nodes-base.code", "code": "// ADVANCED VISUAL EFFECTS SYSTEM\n// Generates professional visual effects and transitions\n\nconst visualEffectsGenerator = {\n  generateEffects(content, duration) {\n    const effects = {\n      textAnimations: this.generateTextAnimations(content, duration),\n      transitions: this.generateTransitions(duration),\n      overlays: this.generateOverlays(content),\n      colorGrading: this.generateColorGrading(content),\n      motionGraphics: this.generateMotionGraphics(content, duration)\n    };\n    \n    return effects;\n  },\n  \n  generateTextAnimations(content, duration) {\n    const animations = [];\n    const segments = Math.ceil(duration / 15); // 15-second segments\n    \n    for (let i = 0; i < segments; i++) {\n      const startTime = i * 15;\n      const endTime = Math.min((i + 1) * 15, duration);\n      \n      animations.push({\n        type: 'text_reveal',\n        startTime,\n        endTime,\n        text: content.visualCues[i] || `Segment ${i + 1}`,\n        animation: this.getRandomAnimation(),\n        style: {\n          font: 'Montserrat-Bold',\n          size: this.calculateFontSize(duration),\n          color: '#FFFFFF',\n          stroke: '#000000',\n          strokeWidth: 2,\n          shadow: 'rgba(0,0,0,0.5)'\n        }\n      });\n    }\n    \n    return animations;\n  },\n  \n  getRandomAnimation() {\n    const animations = [\n      'slide_up',\n      'fade_in',\n      'scale_in',\n      'bounce_in',\n      'typewriter',\n      'glow_pulse'\n    ];\n    return animations[Math.floor(Math.random() * animations.length)];\n  },\n  \n  calculateFontSize(duration) {\n    if (duration <= 30) return 52; // Larger for short videos\n    if (duration <= 60) return 48;\n    return 44; // Smaller for longer videos\n  },\n  \n  generateTransitions(duration) {\n    const transitionCount = Math.max(2, Math.floor(duration / 20));\n    const transitions = [];\n    \n    for (let i = 0; i < transitionCount; i++) {\n      const timing = (duration / (transitionCount + 1)) * (i + 1);\n      \n      transitions.push({\n        type: this.getRandomTransition(),\n        timing,\n        duration: 0.5,\n        easing: 'ease-in-out'\n      });\n    }\n    \n    return transitions;\n  },\n  \n  getRandomTransition() {\n    const transitions = [\n      'crossfade',\n      'slide_left',\n      'slide_right',\n      'zoom_in',\n      'zoom_out',\n      'wipe_down',\n      'circle_reveal'\n    ];\n    return transitions[Math.floor(Math.random() * transitions.length)];\n  },\n  \n  generateOverlays(content) {\n    return {\n      brand_watermark: {\n        position: 'bottom_right',\n        opacity: 0.7,\n        size: '80x80'\n      },\n      progress_bar: {\n        position: 'bottom',\n        color: '#FF0000',\n        height: 4\n      },\n      engagement_prompts: [\n        {\n          text: '👍 LIKE',\n          position: 'bottom_left',\n          timing: '80%',\n          animation: 'pulse'\n        },\n        {\n          text: '🔔 SUBSCRIBE',\n          position: 'top_right',\n          timing: '90%',\n          animation: 'bounce'\n        }\n      ]\n    };\n  },\n  \n  generateColorGrading(content) {\n    const contentType = content.contentStrategy?.contentType || 'general';\n    \n    const gradingPresets = {\n      educational: {\n        brightness: 1.1,\n        contrast: 1.2,\n        saturation: 0.9,\n        temperature: 'warm',\n        vignette: 0.2\n      },\n      entertainment: {\n        brightness: 1.0,\n        contrast: 1.3,\n        saturation: 1.2,\n        temperature: 'cool',\n        vignette: 0.1\n      },\n      storytelling: {\n        brightness: 0.95,\n        contrast: 1.1,\n        saturation: 1.0,\n        temperature: 'neutral',\n        vignette: 0.3\n      }\n    };\n    \n    return gradingPresets[contentType] || gradingPresets.entertainment;\n  },\n  \n  generateMotionGraphics(content, duration) {\n    return {\n      intro_animation: {\n        type: 'logo_reveal',\n        duration: 2,\n        timing: 0\n      },\n      progress_indicators: this.generateProgressIndicators(duration),\n      call_to_action: {\n        type: 'subscribe_button',\n        timing: duration - 5,\n        duration: 3,\n        animation: 'slide_up_bounce'\n      }\n    };\n  },\n  \n  generateProgressIndicators(duration) {\n    const indicators = [];\n    const segmentCount = Math.ceil(duration / 20);\n    \n    for (let i = 0; i < segmentCount; i++) {\n      indicators.push({\n        type: 'progress_dot',\n        position: `${(i / (segmentCount - 1)) * 100}%`,\n        timing: (duration / segmentCount) * i,\n        active_duration: duration / segmentCount\n      });\n    }\n    \n    return indicators;\n  }\n};\n\nconst contentData = $input.first().json;\nconst duration = contentData.finalDuration || 60;\n\nconst visualEffects = visualEffectsGenerator.generateEffects(contentData, duration);\n\nreturn {\n  json: {\n    ...contentData,\n    visualEffects,\n    productionReady: true\n  }\n};"}}}
{"name": "Intervenção Humana", "nodes": [{"parameters": {"assignments": {"assignments": [{"id": "8f16b1bf-1a3e-4029-8d7a-1bccb919ee43", "name": "message.message_id", "value": "={{ $('input evolution').item.json.body.data.key.id || '' }}", "type": "string"}, {"id": "11800d83-ecca-4f9c-a878-a2419db0c8e9", "name": "message.chat_id", "value": "={{ $('input evolution').item.json.body.data.key.remoteJid || '' }}", "type": "string"}, {"id": "c33f9527-e661-49e5-8e5e-64f3b430928a", "name": "message.content_type", "value": "={{ $('input evolution').item.json.body.data.message.extendedTextMessage ? 'text' : '' }}{{ $('input evolution').item.json.body.data.message.conversation ? 'text' : '' }}{{ $('input evolution').item.json.body.data.message.audioMessage ? 'audio' : '' }}{{ $('input evolution').item.json.body.data.message.imageMessage ? 'image' : '' }}", "type": "string"}, {"id": "06eba1c9-cff0-4f68-b6da-6bb0092466b7", "name": "message.content", "value": "={{ $('input evolution').item.json.body.data.message.extendedTextMessage?.text || '' }}{{ $('input evolution').item.json.body.data.message.imageMessage?.caption || '' }}{{ $('input evolution').item.json.body.data.message.conversation || '' }}", "type": "string"}, {"id": "b97f1af3-5361-46fc-9303-d644921231d8", "name": "message.timestamp", "value": "={{ $('input evolution').item.json.body.data.messageTimestamp.toDateTime('s').toISO() }}", "type": "string"}, {"id": "dc3dc59c-90a3-4a45-bea2-de092c91083b", "name": "message.content_url", "value": "={{ $('input evolution').item.json.body.data.message.audioMessage?.url || '' }}{{ $('input evolution').item.json.body.data.message.imageMessage?.url || '' }}", "type": "string"}, {"id": "8b01a818-a456-476e-bace-adefe2f04eb4", "name": "message.event", "value": "={{ $('input evolution').item.json.body.data.key.fromMe ? 'outcoming' : 'incoming' }}", "type": "string"}, {"id": "b2f1f6b5-292f-4695-9e41-be200c6d7053", "name": "instance.name", "value": "={{ $json.body.instance }}", "type": "string"}, {"id": "572fcce5-8a26-4e8f-a48a-ef0bee569dcd", "name": "instance.apikey", "value": "={{ $json.body.apikey }}", "type": "string"}, {"id": "e90043db-657b-461c-b040-2d6089abfbdb", "name": "instance.server_url", "value": "={{ $json.body.server_url }}", "type": "string"}]}, "options": {}}, "id": "091621e7-c818-4435-8ebc-63098ec78f54", "name": "normalizacao", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1040, 380]}, {"parameters": {"operation": "set", "key": "={{ $json.message.chat_id }}_block", "value": "true", "keyType": "string", "expire": true, "ttl": 900}, "id": "53fdeb40-d83f-4c9d-bfa0-4bcb5eb0b35f", "name": "Block AI", "type": "n8n-nodes-base.redis", "typeVersion": 1, "position": [1500, 260], "credentials": {"redis": {"id": "ccEEcPDqUplE95d3", "name": "Redis account"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json.message.event }}", "rightValue": "outcoming", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "outcoming"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "d7b42536-638f-4128-b51b-6aa913e9d9bc", "leftValue": "={{ $json.message.event }}", "rightValue": "incoming", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "incoming"}]}, "options": {}}, "id": "5994088d-26f6-410e-8063-1413b178fae0", "name": "Switch", "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [1260, 380]}, {"parameters": {"operation": "get", "propertyName": "block", "key": "={{ $json.message.chat_id }}_block", "options": {}}, "id": "c3829df4-f167-45e6-84fb-93418b41485f", "name": "Get Block Chat Id", "type": "n8n-nodes-base.redis", "typeVersion": 1, "position": [1500, 500], "credentials": {"redis": {"id": "ccEEcPDqUplE95d3", "name": "Redis account"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json.block }}", "rightValue": "", "operator": {"type": "string", "operation": "empty", "singleValue": true}}], "combinator": "and"}, "renameOutput": true, "outputKey": "IA PODE RESPONDER"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "3ef0e01c-cc14-4663-bb4d-2905b350c3ab", "leftValue": "={{ $json.block }}", "rightValue": "true", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "IA NAO PODE RESPONDER"}]}, "options": {}}, "id": "8cb83c43-ba74-46a4-88e9-8138d7fdfffa", "name": "Switch Block", "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [1720, 500]}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4o-mini", "mode": "list", "cachedResultName": "GPT-4O-MINI"}, "messages": {"values": [{"content": "Voce é o Guto, um assistente que fala sobre qualquer coisa.", "role": "system"}, {"content": "={{ $('normalizacao').item.json.message.content }}"}]}, "options": {}}, "id": "0f3268c1-7980-4dec-a2b9-2e9a00951639", "name": "OpenAI", "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.4, "position": [2060, 380], "credentials": {"openAiApi": {"id": "EwQcFaVNnvQlPhPk", "name": "OpenAi account"}}}, {"parameters": {"httpMethod": "POST", "path": "intervencao", "options": {}}, "id": "e5fcc60f-d5f9-4538-b5b5-7823a350307c", "name": "input evolution", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [820, 380], "webhookId": "445dbbda-c59d-4415-88d5-ed11aafbf331"}, {"parameters": {"method": "POST", "url": "={{ $('normalizacao').item.json.instance.server_url }}/message/sendText/{{ $('normalizacao').item.json.instance.name }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apikey", "value": "={{ $('normalizacao').item.json.instance.apikey }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "number", "value": "={{ $('normalizacao').item.json.message.chat_id }}"}, {"name": "text", "value": "={{ $json.message.content }}"}]}, "options": {}}, "id": "0c4f1f82-8cf2-4bdf-96e0-43ea1d1c2159", "name": "Responder Evolution API", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2440, 380]}], "pinData": {}, "connections": {"normalizacao": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Block AI", "type": "main", "index": 0}], [{"node": "Get Block Chat Id", "type": "main", "index": 0}]]}, "Get Block Chat Id": {"main": [[{"node": "Switch Block", "type": "main", "index": 0}]]}, "Switch Block": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}]]}, "OpenAI": {"main": [[{"node": "Responder Evolution API", "type": "main", "index": 0}]]}, "input evolution": {"main": [[{"node": "normalizacao", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "802e70e0-889a-426c-8a1f-e6e4d424a7f2", "meta": {"templateCredsSetupCompleted": true, "instanceId": "2ad20c637f548d7b1e09cbae0383c6644aca87b4f0fe8deda2de937f77c2be79"}, "id": "BDNGxs16Icwkw2D0", "tags": []}
#!/usr/bin/env node

/**
 * Local AI Model Manager Server
 * Manages local AI models for YouTube automation system
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const cron = require('node-cron');
const winston = require('winston');
const fs = require('fs-extra');
const path = require('path');
const axios = require('axios');
const si = require('systeminformation');

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 9000;

// Configure logging
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'model-manager' },
  transports: [
    new winston.transports.File({ filename: '/logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: '/logs/combined.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
});

// Middleware
app.use(helmet());
app.use(cors());
app.use(compression());
app.use(morgan('combined', { stream: { write: message => logger.info(message.trim()) } }));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Model Manager Class
class LocalAIModelManager {
  constructor() {
    this.config = {
      ollamaUrl: process.env.OLLAMA_URL || 'http://localhost:11434',
      sdUrl: process.env.SD_URL || 'http://localhost:7860',
      comfyUIUrl: process.env.COMFYUI_URL || 'http://localhost:8188',
      modelsPath: '/ollama-models',
      sdModelsPath: '/sd-models',
      configsPath: '/configs'
    };
    
    this.recommendedModels = {
      llm: [
        { name: 'qwen2.5:32b', size: '19GB', description: 'High-quality general purpose model' },
        { name: 'mistral:7b-instruct', size: '4.1GB', description: 'Fast and efficient instruction model' },
        { name: 'llama3.2:70b', size: '40GB', description: 'Large, high-performance model' },
        { name: 'phi3:4b', size: '2.2GB', description: 'Lightweight, fast model' },
        { name: 'gemma2:27b', size: '16GB', description: 'Google\'s efficient model' }
      ],
      sd: [
        { name: 'sd_xl_base_1.0.safetensors', size: '6.9GB', description: 'SDXL base model for high-quality images' },
        { name: 'sd_xl_refiner_1.0.safetensors', size: '6.1GB', description: 'SDXL refiner for enhanced details' },
        { name: 'deliberate_v2.safetensors', size: '4.3GB', description: 'Popular realistic model' },
        { name: 'dreamshaper_8.safetensors', size: '2.1GB', description: 'Versatile artistic model' }
      ]
    };
    
    this.systemStats = {
      cpu: {},
      memory: {},
      gpu: {},
      disk: {},
      models: {
        ollama: [],
        sd: []
      }
    };
    
    this.initializeManager();
  }

  async initializeManager() {
    try {
      // Ensure directories exist
      await fs.ensureDir(this.config.modelsPath);
      await fs.ensureDir(this.config.sdModelsPath);
      await fs.ensureDir(this.config.configsPath);
      
      // Start monitoring
      this.startSystemMonitoring();
      
      // Check model availability
      await this.updateModelStatus();
      
      logger.info('Model Manager initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Model Manager:', error);
    }
  }

  startSystemMonitoring() {
    // Update system stats every 30 seconds
    setInterval(async () => {
      try {
        await this.updateSystemStats();
      } catch (error) {
        logger.error('Failed to update system stats:', error);
      }
    }, 30000);
  }

  async updateSystemStats() {
    try {
      const [cpu, memory, graphics, disk] = await Promise.all([
        si.cpu(),
        si.mem(),
        si.graphics(),
        si.fsSize()
      ]);

      this.systemStats = {
        cpu: {
          manufacturer: cpu.manufacturer,
          brand: cpu.brand,
          cores: cpu.cores,
          physicalCores: cpu.physicalCores,
          speed: cpu.speed
        },
        memory: {
          total: memory.total,
          free: memory.free,
          used: memory.used,
          available: memory.available,
          usage: ((memory.used / memory.total) * 100).toFixed(2)
        },
        gpu: graphics.controllers.map(gpu => ({
          model: gpu.model,
          vendor: gpu.vendor,
          vram: gpu.vram,
          memoryTotal: gpu.memoryTotal,
          memoryUsed: gpu.memoryUsed,
          memoryFree: gpu.memoryFree,
          utilizationGpu: gpu.utilizationGpu,
          utilizationMemory: gpu.utilizationMemory
        })),
        disk: disk.map(d => ({
          fs: d.fs,
          type: d.type,
          size: d.size,
          used: d.used,
          available: d.available,
          usage: d.use
        }))
      };
    } catch (error) {
      logger.error('Failed to get system stats:', error);
    }
  }

  async updateModelStatus() {
    try {
      // Check Ollama models
      const ollamaModels = await this.getOllamaModels();
      this.systemStats.models.ollama = ollamaModels;

      // Check Stable Diffusion models
      const sdModels = await this.getSDModels();
      this.systemStats.models.sd = sdModels;

      logger.info(`Found ${ollamaModels.length} Ollama models and ${sdModels.length} SD models`);
    } catch (error) {
      logger.error('Failed to update model status:', error);
    }
  }

  async getOllamaModels() {
    try {
      const response = await axios.get(`${this.config.ollamaUrl}/api/tags`, {
        timeout: 10000
      });
      
      return response.data.models || [];
    } catch (error) {
      logger.warn('Failed to get Ollama models:', error.message);
      return [];
    }
  }

  async getSDModels() {
    try {
      const response = await axios.get(`${this.config.sdUrl}/sdapi/v1/sd-models`, {
        timeout: 10000
      });
      
      return response.data || [];
    } catch (error) {
      logger.warn('Failed to get SD models:', error.message);
      return [];
    }
  }

  async installOllamaModel(modelName) {
    try {
      logger.info(`Installing Ollama model: ${modelName}`);
      
      const response = await axios.post(`${this.config.ollamaUrl}/api/pull`, {
        name: modelName
      }, {
        timeout: 3600000 // 1 hour timeout for large models
      });

      logger.info(`Successfully installed Ollama model: ${modelName}`);
      await this.updateModelStatus();
      
      return { success: true, model: modelName };
    } catch (error) {
      logger.error(`Failed to install Ollama model ${modelName}:`, error);
      throw error;
    }
  }

  async deleteOllamaModel(modelName) {
    try {
      logger.info(`Deleting Ollama model: ${modelName}`);
      
      await axios.delete(`${this.config.ollamaUrl}/api/delete`, {
        data: { name: modelName }
      });

      logger.info(`Successfully deleted Ollama model: ${modelName}`);
      await this.updateModelStatus();
      
      return { success: true, model: modelName };
    } catch (error) {
      logger.error(`Failed to delete Ollama model ${modelName}:`, error);
      throw error;
    }
  }

  async optimizeForHardware() {
    const stats = this.systemStats;
    const recommendations = {
      models: [],
      settings: {},
      warnings: []
    };

    // Memory-based recommendations
    const totalMemoryGB = Math.round(stats.memory.total / (1024 * 1024 * 1024));
    
    if (totalMemoryGB >= 32) {
      recommendations.models.push('qwen2.5:32b', 'llama3.2:70b');
      recommendations.settings.ollamaContextSize = 8192;
    } else if (totalMemoryGB >= 16) {
      recommendations.models.push('qwen2.5:32b', 'mistral:7b-instruct');
      recommendations.settings.ollamaContextSize = 4096;
    } else if (totalMemoryGB >= 8) {
      recommendations.models.push('mistral:7b-instruct', 'phi3:4b');
      recommendations.settings.ollamaContextSize = 2048;
    } else {
      recommendations.models.push('phi3:4b');
      recommendations.settings.ollamaContextSize = 1024;
      recommendations.warnings.push('Low memory detected. Consider upgrading RAM for better performance.');
    }

    // GPU-based recommendations
    const hasNvidiaGPU = stats.gpu.some(gpu => 
      gpu.vendor && gpu.vendor.toLowerCase().includes('nvidia')
    );
    
    if (hasNvidiaGPU) {
      recommendations.settings.enableGPU = true;
      recommendations.settings.gpuLayers = -1; // Use all GPU layers
    } else {
      recommendations.settings.enableGPU = false;
      recommendations.warnings.push('No NVIDIA GPU detected. CPU-only inference will be slower.');
    }

    // Disk space warnings
    const availableSpaceGB = Math.min(...stats.disk.map(d => d.available / (1024 * 1024 * 1024)));
    
    if (availableSpaceGB < 50) {
      recommendations.warnings.push('Low disk space. Ensure at least 50GB free for model storage.');
    }

    return recommendations;
  }

  getModelRecommendations() {
    return {
      recommended: this.recommendedModels,
      installed: this.systemStats.models,
      systemOptimization: this.optimizeForHardware()
    };
  }
}

// Initialize Model Manager
const modelManager = new LocalAIModelManager();

// API Routes

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage()
  });
});

// Get system status
app.get('/api/status', async (req, res) => {
  try {
    await modelManager.updateSystemStats();
    await modelManager.updateModelStatus();
    
    res.json({
      success: true,
      data: {
        system: modelManager.systemStats,
        services: {
          ollama: await checkService(modelManager.config.ollamaUrl),
          stableDiffusion: await checkService(modelManager.config.sdUrl),
          comfyUI: await checkService(modelManager.config.comfyUIUrl)
        }
      }
    });
  } catch (error) {
    logger.error('Failed to get status:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get model recommendations
app.get('/api/recommendations', async (req, res) => {
  try {
    const recommendations = await modelManager.optimizeForHardware();
    
    res.json({
      success: true,
      data: recommendations
    });
  } catch (error) {
    logger.error('Failed to get recommendations:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Install Ollama model
app.post('/api/models/ollama/install', async (req, res) => {
  try {
    const { modelName } = req.body;
    
    if (!modelName) {
      return res.status(400).json({
        success: false,
        error: 'Model name is required'
      });
    }

    const result = await modelManager.installOllamaModel(modelName);
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    logger.error('Failed to install model:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Delete Ollama model
app.delete('/api/models/ollama/:modelName', async (req, res) => {
  try {
    const { modelName } = req.params;
    
    const result = await modelManager.deleteOllamaModel(modelName);
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    logger.error('Failed to delete model:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get installed models
app.get('/api/models', async (req, res) => {
  try {
    await modelManager.updateModelStatus();
    
    res.json({
      success: true,
      data: modelManager.systemStats.models
    });
  } catch (error) {
    logger.error('Failed to get models:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Utility function to check service availability
async function checkService(url) {
  try {
    const response = await axios.get(url, { timeout: 5000 });
    return {
      available: true,
      status: response.status,
      url
    };
  } catch (error) {
    return {
      available: false,
      error: error.message,
      url
    };
  }
}

// Error handling middleware
app.use((error, req, res, next) => {
  logger.error('Unhandled error:', error);
  res.status(500).json({
    success: false,
    error: 'Internal server error'
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found'
  });
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  process.exit(0);
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
  logger.info(`Model Manager server running on port ${PORT}`);
  console.log(`🚀 Model Manager API available at http://localhost:${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`📈 System status: http://localhost:${PORT}/api/status`);
});

module.exports = app;

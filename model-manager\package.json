{"name": "local-ai-model-manager", "version": "1.0.0", "description": "Local AI Model Management Service for YouTube Automation", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "lint": "eslint .", "install-models": "node scripts/install-models.js", "optimize-models": "node scripts/optimize-models.js", "health-check": "node scripts/health-check.js"}, "keywords": ["ai", "local-models", "ollama", "stable-diffusion", "model-management"], "author": "Advanced YouTube Automation", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "compression": "^1.7.4", "morgan": "^1.10.0", "axios": "^1.5.0", "node-cron": "^3.0.2", "winston": "^3.10.0", "joi": "^17.9.2", "multer": "^1.4.5-lts.1", "fs-extra": "^11.1.1", "archiver": "^5.3.1", "tar": "^6.1.15", "semver": "^7.5.4", "systeminformation": "^5.21.7", "node-disk-info": "^1.3.0", "pidusage": "^3.0.2"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "eslint": "^8.46.0", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-repo/local-ai-model-manager"}, "bugs": {"url": "https://github.com/your-repo/local-ai-model-manager/issues"}, "homepage": "https://github.com/your-repo/local-ai-model-manager#readme"}
{"name": "Advanced Thinker & Strategy Nodes", "description": "Intelligent decision-making and strategic planning nodes for content optimization", "nodes": {"strategic_thinker": {"name": "🧠 Strategic Thinker Node", "type": "n8n-nodes-base.code", "description": "Advanced AI-powered strategic decision making for content optimization", "code": "// STRATEGIC THINKER NODE - ADVANCED AI DECISION MAKING\n// Makes intelligent decisions about content strategy, timing, and optimization\n\nconst strategicThinker = {\n  async analyzeAndDecide(contentData, marketData, historicalData) {\n    const analysis = {\n      contentAnalysis: await this.analyzeContent(contentData),\n      marketAnalysis: await this.analyzeMarket(marketData),\n      competitorAnalysis: await this.analyzeCompetition(marketData),\n      audienceAnalysis: await this.analyzeAudience(historicalData),\n      trendAnalysis: await this.analyzeTrends(marketData)\n    };\n    \n    const strategy = await this.formulateStrategy(analysis);\n    const decisions = await this.makeStrategicDecisions(strategy, analysis);\n    \n    return {\n      analysis,\n      strategy,\n      decisions,\n      confidence: this.calculateConfidence(analysis),\n      recommendations: this.generateRecommendations(strategy, decisions)\n    };\n  },\n  \n  async analyzeContent(contentData) {\n    const content = contentData.videoScript || '';\n    const title = contentData.videoTitle || '';\n    \n    return {\n      complexity: this.assessComplexity(content),\n      emotionalTone: this.analyzeEmotionalTone(content),\n      keyTopics: this.extractKeyTopics(content),\n      uniquenessScore: this.assessUniqueness(content, title),\n      engagementPotential: this.assessEngagementPotential(content, title),\n      viralElements: this.identifyViralElements(content, title),\n      contentQuality: this.assessContentQuality(content, title)\n    };\n  },\n  \n  assessComplexity(content) {\n    const sentences = content.split(/[.!?]+/).length;\n    const avgWordsPerSentence = content.split(' ').length / sentences;\n    const technicalTerms = (content.match(/\\b[A-Z]{2,}\\b/g) || []).length;\n    const complexWords = content.split(' ').filter(word => word.length > 8).length;\n    \n    const complexityScore = Math.min(10, \n      (avgWordsPerSentence / 15) * 3 + \n      (technicalTerms / 10) * 4 + \n      (complexWords / content.split(' ').length) * 3\n    );\n    \n    return {\n      score: Math.round(complexityScore),\n      level: complexityScore < 3 ? 'simple' : complexityScore < 7 ? 'moderate' : 'complex',\n      factors: { sentences, avgWordsPerSentence, technicalTerms, complexWords }\n    };\n  },\n  \n  analyzeEmotionalTone(content) {\n    const emotionalWords = {\n      positive: ['amazing', 'incredible', 'awesome', 'fantastic', 'great', 'love', 'best', 'perfect'],\n      negative: ['terrible', 'awful', 'hate', 'worst', 'bad', 'horrible', 'disgusting'],\n      excitement: ['wow', 'omg', 'incredible', 'unbelievable', 'shocking', 'amazing'],\n      urgency: ['now', 'immediately', 'urgent', 'quick', 'fast', 'instant', 'hurry'],\n      curiosity: ['secret', 'hidden', 'mystery', 'unknown', 'discover', 'reveal', 'truth']\n    };\n    \n    const contentLower = content.toLowerCase();\n    const scores = {};\n    \n    Object.entries(emotionalWords).forEach(([emotion, words]) => {\n      scores[emotion] = words.filter(word => contentLower.includes(word)).length;\n    });\n    \n    const dominantEmotion = Object.entries(scores).reduce((a, b) => scores[a[0]] > scores[b[0]] ? a : b)[0];\n    \n    return {\n      scores,\n      dominantEmotion,\n      emotionalIntensity: Math.max(...Object.values(scores)),\n      overallTone: this.calculateOverallTone(scores)\n    };\n  },\n  \n  calculateOverallTone(scores) {\n    const positiveScore = scores.positive + scores.excitement;\n    const negativeScore = scores.negative;\n    \n    if (positiveScore > negativeScore * 2) return 'very_positive';\n    if (positiveScore > negativeScore) return 'positive';\n    if (negativeScore > positiveScore) return 'negative';\n    return 'neutral';\n  },\n  \n  extractKeyTopics(content) {\n    // Simple keyword extraction (in production, use NLP libraries)\n    const words = content.toLowerCase().split(/\\W+/);\n    const stopWords = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should'];\n    \n    const filteredWords = words.filter(word => \n      word.length > 3 && \n      !stopWords.includes(word) && \n      isNaN(word)\n    );\n    \n    const wordFreq = {};\n    filteredWords.forEach(word => {\n      wordFreq[word] = (wordFreq[word] || 0) + 1;\n    });\n    \n    return Object.entries(wordFreq)\n      .sort(([,a], [,b]) => b - a)\n      .slice(0, 10)\n      .map(([word, freq]) => ({ word, frequency: freq }));\n  },\n  \n  assessUniqueness(content, title) {\n    // Simulate uniqueness assessment (in production, compare against database)\n    const uniqueElements = [\n      this.hasUniqueAngle(content, title),\n      this.hasOriginalInsights(content),\n      this.hasPersonalTouch(content),\n      this.hasCreativePresentation(content)\n    ];\n    \n    const uniquenessScore = (uniqueElements.filter(Boolean).length / uniqueElements.length) * 10;\n    \n    return {\n      score: Math.round(uniquenessScore),\n      level: uniquenessScore < 4 ? 'common' : uniquenessScore < 7 ? 'somewhat_unique' : 'highly_unique',\n      elements: {\n        uniqueAngle: uniqueElements[0],\n        originalInsights: uniqueElements[1],\n        personalTouch: uniqueElements[2],\n        creativePresentation: uniqueElements[3]\n      }\n    };\n  },\n  \n  hasUniqueAngle(content, title) {\n    const uniqueIndicators = ['never', 'first time', 'exclusive', 'behind the scenes', 'secret', 'hidden'];\n    const combined = (content + ' ' + title).toLowerCase();\n    return uniqueIndicators.some(indicator => combined.includes(indicator));\n  },\n  \n  hasOriginalInsights(content) {\n    const insightIndicators = ['i discovered', 'i learned', 'my experience', 'what i found', 'here\\'s why'];\n    const contentLower = content.toLowerCase();\n    return insightIndicators.some(indicator => contentLower.includes(indicator));\n  },\n  \n  hasPersonalTouch(content) {\n    const personalIndicators = ['i', 'my', 'me', 'personally', 'in my opinion'];\n    const contentLower = content.toLowerCase();\n    return personalIndicators.some(indicator => contentLower.includes(indicator));\n  },\n  \n  hasCreativePresentation(content) {\n    const creativeIndicators = ['imagine', 'picture this', 'let me show you', 'here\\'s the thing'];\n    const contentLower = content.toLowerCase();\n    return creativeIndicators.some(indicator => contentLower.includes(indicator));\n  },\n  \n  assessEngagementPotential(content, title) {\n    const engagementFactors = {\n      hasQuestion: /\\?/.test(content + title),\n      hasCallToAction: /(comment|like|subscribe|share)/i.test(content),\n      hasControversy: /(controversial|debate|opinion|disagree)/i.test(content + title),\n      hasEmotionalHook: this.analyzeEmotionalTone(content).emotionalIntensity > 2,\n      hasValueProposition: /(learn|discover|find out|secret|tip|hack)/i.test(content + title)\n    };\n    \n    const score = Object.values(engagementFactors).filter(Boolean).length * 2;\n    \n    return {\n      score: Math.min(10, score),\n      level: score < 4 ? 'low' : score < 7 ? 'medium' : 'high',\n      factors: engagementFactors\n    };\n  },\n  \n  identifyViralElements(content, title) {\n    const viralElements = {\n      surprise: /(shocking|surprising|unexpected|plot twist)/i.test(content + title),\n      emotion: this.analyzeEmotionalTone(content).emotionalIntensity > 3,\n      relatability: /(everyone|we all|you know|relatable)/i.test(content + title),\n      curiosity: /(secret|mystery|hidden|you won\\'t believe)/i.test(content + title),\n      urgency: /(now|today|immediately|before it\\'s too late)/i.test(content + title),\n      social_proof: /(millions|everyone|trending|viral)/i.test(content + title)\n    };\n    \n    const viralScore = Object.values(viralElements).filter(Boolean).length;\n    \n    return {\n      score: Math.min(10, viralScore * 1.5),\n      level: viralScore < 2 ? 'low' : viralScore < 4 ? 'medium' : 'high',\n      elements: viralElements,\n      viralPotential: viralScore >= 4 ? 'high' : viralScore >= 2 ? 'medium' : 'low'\n    };\n  },\n  \n  assessContentQuality(content, title) {\n    const qualityFactors = {\n      clarity: this.assessClarity(content),\n      structure: this.assessStructure(content),\n      value: this.assessValue(content, title),\n      originality: this.assessUniqueness(content, title).score / 10,\n      engagement: this.assessEngagementPotential(content, title).score / 10\n    };\n    \n    const overallScore = Object.values(qualityFactors).reduce((sum, score) => sum + score, 0) / Object.keys(qualityFactors).length;\n    \n    return {\n      score: Math.round(overallScore * 10),\n      level: overallScore < 0.6 ? 'needs_improvement' : overallScore < 0.8 ? 'good' : 'excellent',\n      factors: qualityFactors\n    };\n  },\n  \n  assessClarity(content) {\n    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);\n    const avgSentenceLength = content.split(' ').length / sentences.length;\n    \n    // Optimal sentence length is 15-20 words\n    const clarityScore = avgSentenceLength > 25 ? 0.5 : avgSentenceLength < 10 ? 0.7 : 1.0;\n    \n    return clarityScore;\n  },\n  \n  assessStructure(content) {\n    const hasIntro = /^(hi|hello|welcome|today|in this)/i.test(content.trim());\n    const hasConclusion = /(conclusion|finally|in summary|to wrap up|that\\'s all)/i.test(content);\n    const hasTransitions = /(first|second|next|then|finally|also|however|therefore)/i.test(content);\n    \n    const structureElements = [hasIntro, hasConclusion, hasTransitions].filter(Boolean).length;\n    return structureElements / 3;\n  },\n  \n  assessValue(content, title) {\n    const valueIndicators = [\n      /(tip|hack|secret|method|way|how to|guide|tutorial)/i.test(content + title),\n      /(save|earn|improve|increase|reduce|avoid)/i.test(content + title),\n      /(learn|discover|understand|master|become)/i.test(content + title)\n    ];\n    \n    return valueIndicators.filter(Boolean).length / valueIndicators.length;\n  },\n  \n  async analyzeMarket(marketData) {\n    return {\n      competitionLevel: marketData.marketConditions?.competitionLevel || 'moderate',\n      trendingTopics: marketData.marketConditions?.trendingTopics || [],\n      optimalTiming: this.calculateOptimalTiming(),\n      seasonalFactors: this.analyzeSeasonalFactors(),\n      platformTrends: this.analyzePlatformTrends()\n    };\n  },\n  \n  calculateOptimalTiming() {\n    const now = new Date();\n    const hour = now.getHours();\n    const dayOfWeek = now.getDay();\n    \n    const optimalHours = {\n      weekday: [12, 17, 20], // Lunch, after work, evening\n      weekend: [10, 14, 19]  // Morning, afternoon, evening\n    };\n    \n    const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;\n    const relevantHours = isWeekend ? optimalHours.weekend : optimalHours.weekday;\n    \n    return {\n      currentOptimal: relevantHours.includes(hour),\n      nextOptimalHour: relevantHours.find(h => h > hour) || relevantHours[0],\n      dayType: isWeekend ? 'weekend' : 'weekday',\n      recommendation: this.getTimingRecommendation(hour, isWeekend)\n    };\n  },\n  \n  getTimingRecommendation(hour, isWeekend) {\n    if (isWeekend) {\n      if (hour < 10) return 'Wait until 10 AM for better engagement';\n      if (hour > 22) return 'Consider posting tomorrow morning';\n      return 'Good time to post';\n    } else {\n      if (hour < 12) return 'Wait until lunch time (12 PM) for better reach';\n      if (hour > 21) return 'Consider posting tomorrow at lunch or evening';\n      return 'Optimal posting time';\n    }\n  },\n  \n  analyzeSeasonalFactors() {\n    const month = new Date().getMonth();\n    const seasonalTrends = {\n      0: 'New Year resolutions, productivity content',\n      1: 'Valentine\\'s Day, relationship content',\n      2: 'Spring preparation, health content',\n      3: 'Spring activities, outdoor content',\n      4: 'Mother\\'s Day, family content',\n      5: 'Summer preparation, vacation content',\n      6: 'Summer activities, travel content',\n      7: 'Back to school preparation',\n      8: 'Fall activities, productivity content',\n      9: 'Halloween, spooky content',\n      10: 'Thanksgiving, gratitude content',\n      11: 'Holiday season, gift guides'\n    };\n    \n    return {\n      currentSeason: seasonalTrends[month],\n      relevantTopics: this.getSeasonalTopics(month)\n    };\n  },\n  \n  getSeasonalTopics(month) {\n    const topicMap = {\n      0: ['resolutions', 'goals', 'productivity', 'health'],\n      1: ['love', 'relationships', 'valentine'],\n      2: ['spring cleaning', 'health', 'fitness'],\n      3: ['outdoor', 'gardening', 'spring'],\n      4: ['mother', 'family', 'appreciation'],\n      5: ['summer', 'vacation', 'travel'],\n      6: ['summer', 'activities', 'fun'],\n      7: ['school', 'education', 'learning'],\n      8: ['fall', 'productivity', 'organization'],\n      9: ['halloween', 'spooky', 'costume'],\n      10: ['thanksgiving', 'gratitude', 'family'],\n      11: ['christmas', 'holiday', 'gifts']\n    };\n    \n    return topicMap[month] || ['general', 'trending'];\n  },\n  \n  analyzePlatformTrends() {\n    // Simulate platform trend analysis\n    return {\n      shortFormContent: 'Highly favored by algorithm',\n      verticalVideo: 'Optimal for mobile viewing',\n      interactiveElements: 'Boosts engagement metrics',\n      trendingAudio: 'Increases discoverability',\n      quickHooks: 'Essential for retention'\n    };\n  },\n  \n  async formulateStrategy(analysis) {\n    const strategy = {\n      contentStrategy: this.formulateContentStrategy(analysis.contentAnalysis),\n      timingStrategy: this.formulateTimingStrategy(analysis.marketAnalysis),\n      engagementStrategy: this.formulateEngagementStrategy(analysis.contentAnalysis),\n      distributionStrategy: this.formulateDistributionStrategy(analysis.marketAnalysis),\n      optimizationStrategy: this.formulateOptimizationStrategy(analysis)\n    };\n    \n    return strategy;\n  },\n  \n  formulateContentStrategy(contentAnalysis) {\n    const strategy = {\n      focusAreas: [],\n      improvements: [],\n      strengths: []\n    };\n    \n    if (contentAnalysis.viralElements.score < 5) {\n      strategy.focusAreas.push('Increase viral elements');\n      strategy.improvements.push('Add more emotional hooks and surprise elements');\n    } else {\n      strategy.strengths.push('Strong viral potential');\n    }\n    \n    if (contentAnalysis.engagementPotential.score < 6) {\n      strategy.focusAreas.push('Boost engagement potential');\n      strategy.improvements.push('Add more calls-to-action and interactive elements');\n    } else {\n      strategy.strengths.push('High engagement potential');\n    }\n    \n    if (contentAnalysis.uniquenessScore < 6) {\n      strategy.focusAreas.push('Increase uniqueness');\n      strategy.improvements.push('Add more personal insights and unique angles');\n    } else {\n      strategy.strengths.push('Unique perspective');\n    }\n    \n    return strategy;\n  },\n  \n  formulateTimingStrategy(marketAnalysis) {\n    return {\n      optimalPostTime: marketAnalysis.optimalTiming.nextOptimalHour,\n      reasoning: marketAnalysis.optimalTiming.recommendation,\n      seasonalConsiderations: marketAnalysis.seasonalFactors.currentSeason,\n      competitionAvoidance: marketAnalysis.competitionLevel === 'high' ? 'Post during off-peak hours' : 'Standard timing is fine'\n    };\n  },\n  \n  formulateEngagementStrategy(contentAnalysis) {\n    const strategy = {\n      hooks: [],\n      callsToAction: [],\n      interactiveElements: []\n    };\n    \n    if (contentAnalysis.emotionalTone.dominantEmotion === 'curiosity') {\n      strategy.hooks.push('Leverage curiosity with teaser openings');\n    }\n    \n    if (contentAnalysis.engagementPotential.factors.hasQuestion) {\n      strategy.interactiveElements.push('Encourage comment responses to questions');\n    }\n    \n    strategy.callsToAction.push('Subscribe reminder at optimal moment');\n    strategy.callsToAction.push('Like prompt during high-value content');\n    \n    return strategy;\n  },\n  \n  formulateDistributionStrategy(marketAnalysis) {\n    return {\n      primaryPlatform: 'YouTube Shorts',\n      crossPlatform: ['TikTok', 'Instagram Reels'],\n      hashtagStrategy: 'Mix trending and niche-specific tags',\n      communityEngagement: 'Respond to comments within first hour'\n    };\n  },\n  \n  formulateOptimizationStrategy(analysis) {\n    return {\n      titleOptimization: 'Include emotional triggers and keywords',\n      thumbnailStrategy: 'High contrast, clear focal point',\n      descriptionStrategy: 'Front-load keywords, include timestamps',\n      tagStrategy: 'Balance broad and specific tags',\n      retentionOptimization: 'Strong hook in first 3 seconds'\n    };\n  },\n  \n  async makeStrategicDecisions(strategy, analysis) {\n    return {\n      shouldProceed: this.decideShouldProceed(analysis),\n      contentAdjustments: this.decideContentAdjustments(strategy, analysis),\n      timingDecision: this.decideOptimalTiming(strategy),\n      distributionDecision: this.decideDistributionChannels(strategy),\n      priorityActions: this.decidePriorityActions(strategy, analysis)\n    };\n  },\n  \n  decideShouldProceed(analysis) {\n    const qualityScore = analysis.contentAnalysis.contentQuality.score;\n    const viralPotential = analysis.contentAnalysis.viralElements.score;\n    const engagementPotential = analysis.contentAnalysis.engagementPotential.score;\n    \n    const overallScore = (qualityScore + viralPotential + engagementPotential) / 3;\n    \n    return {\n      decision: overallScore >= 6,\n      confidence: overallScore / 10,\n      reasoning: overallScore >= 6 ? 'Content meets quality thresholds' : 'Content needs improvement before publishing',\n      requiredImprovements: overallScore < 6 ? this.getRequiredImprovements(analysis) : []\n    };\n  },\n  \n  getRequiredImprovements(analysis) {\n    const improvements = [];\n    \n    if (analysis.contentAnalysis.contentQuality.score < 6) {\n      improvements.push('Improve content clarity and structure');\n    }\n    if (analysis.contentAnalysis.viralElements.score < 5) {\n      improvements.push('Add more viral elements (surprise, emotion, relatability)');\n    }\n    if (analysis.contentAnalysis.engagementPotential.score < 6) {\n      improvements.push('Increase engagement potential with CTAs and questions');\n    }\n    \n    return improvements;\n  },\n  \n  decideContentAdjustments(strategy, analysis) {\n    const adjustments = [];\n    \n    strategy.contentStrategy.improvements.forEach(improvement => {\n      adjustments.push({\n        type: 'content_improvement',\n        action: improvement,\n        priority: 'high'\n      });\n    });\n    \n    return adjustments;\n  },\n  \n  decideOptimalTiming(strategy) {\n    return {\n      recommendedTime: strategy.timingStrategy.optimalPostTime,\n      reasoning: strategy.timingStrategy.reasoning,\n      urgency: strategy.timingStrategy.competitionAvoidance.includes('off-peak') ? 'high' : 'medium'\n    };\n  },\n  \n  decideDistributionChannels(strategy) {\n    return {\n      primary: strategy.distributionStrategy.primaryPlatform,\n      secondary: strategy.distributionStrategy.crossPlatform,\n      sequencing: 'YouTube first, then cross-post to other platforms'\n    };\n  },\n  \n  decidePriorityActions(strategy, analysis) {\n    const actions = [];\n    \n    // High priority actions based on analysis\n    if (analysis.contentAnalysis.viralElements.score > 7) {\n      actions.push({ action: 'Fast-track for immediate publishing', priority: 'urgent' });\n    }\n    \n    if (analysis.contentAnalysis.engagementPotential.score < 5) {\n      actions.push({ action: 'Revise content for better engagement', priority: 'high' });\n    }\n    \n    actions.push({ action: 'Optimize metadata based on strategy', priority: 'medium' });\n    actions.push({ action: 'Prepare cross-platform versions', priority: 'low' });\n    \n    return actions.sort((a, b) => {\n      const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };\n      return priorityOrder[b.priority] - priorityOrder[a.priority];\n    });\n  },\n  \n  calculateConfidence(analysis) {\n    const factors = [\n      analysis.contentAnalysis.contentQuality.score / 10,\n      analysis.contentAnalysis.viralElements.score / 10,\n      analysis.contentAnalysis.engagementPotential.score / 10,\n      analysis.contentAnalysis.uniquenessScore / 10\n    ];\n    \n    const averageConfidence = factors.reduce((sum, factor) => sum + factor, 0) / factors.length;\n    \n    return {\n      score: Math.round(averageConfidence * 100),\n      level: averageConfidence > 0.8 ? 'very_high' : averageConfidence > 0.6 ? 'high' : averageConfidence > 0.4 ? 'medium' : 'low',\n      factors: {\n        contentQuality: factors[0],\n        viralPotential: factors[1],\n        engagementPotential: factors[2],\n        uniqueness: factors[3]\n      }\n    };\n  },\n  \n  generateRecommendations(strategy, decisions) {\n    const recommendations = [];\n    \n    // Content recommendations\n    strategy.contentStrategy.improvements.forEach(improvement => {\n      recommendations.push({\n        category: 'content',\n        recommendation: improvement,\n        impact: 'high',\n        effort: 'medium'\n      });\n    });\n    \n    // Timing recommendations\n    recommendations.push({\n      category: 'timing',\n      recommendation: strategy.timingStrategy.reasoning,\n      impact: 'medium',\n      effort: 'low'\n    });\n    \n    // Engagement recommendations\n    strategy.engagementStrategy.callsToAction.forEach(cta => {\n      recommendations.push({\n        category: 'engagement',\n        recommendation: cta,\n        impact: 'medium',\n        effort: 'low'\n      });\n    });\n    \n    // Optimization recommendations\n    Object.entries(strategy.optimizationStrategy).forEach(([key, value]) => {\n      recommendations.push({\n        category: 'optimization',\n        recommendation: `${key}: ${value}`,\n        impact: 'medium',\n        effort: 'low'\n      });\n    });\n    \n    return recommendations.sort((a, b) => {\n      const impactOrder = { high: 3, medium: 2, low: 1 };\n      const effortOrder = { low: 3, medium: 2, high: 1 };\n      \n      const scoreA = impactOrder[a.impact] + effortOrder[a.effort];\n      const scoreB = impactOrder[b.impact] + effortOrder[b.effort];\n      \n      return scoreB - scoreA;\n    });\n  }\n};\n\n// Main execution\nconst contentData = $input.first().json;\nconst marketData = $('🧠 Market Strategy Thinker').item.json;\nconst historicalData = {}; // Would be populated from analytics in production\n\nconst strategicAnalysis = await strategicThinker.analyzeAndDecide(contentData, marketData, historicalData);\n\nreturn {\n  json: {\n    ...contentData,\n    strategicAnalysis,\n    shouldProceed: strategicAnalysis.decisions.shouldProceed.decision,\n    confidence: strategicAnalysis.confidence.score,\n    priorityActions: strategicAnalysis.decisions.priorityActions,\n    recommendations: strategicAnalysis.recommendations\n  }\n};"}}}
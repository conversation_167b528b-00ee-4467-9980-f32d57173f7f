{
  "name": "Local AI Integration System",
  "description": "Complete local AI model integration for YouTube automation without external APIs",
  "version": "2.0.0-local",
  "architecture": "offline-first",
  "components": {
    "local_llm_service": {
      "name": "🧠 Local LLM Service",
      "type": "n8n-nodes-base.code",
      "description": "Local language model integration using Ollama or LocalAI",
      "code": "// LOCAL LLM INTEGRATION - COMPLETELY OFFLINE\n// Replaces OpenRouter API with local model calls\n\nconst localLLMService = {\n  // Configuration for local models\n  config: {\n    ollamaUrl: process.env.OLLAMA_URL || 'http://localhost:11434',\n    localAIUrl: process.env.LOCALAI_URL || 'http://localhost:8080',\n    preferredModel: process.env.PREFERRED_LLM || 'qwen2.5:32b', // Based on your downloads\n    fallbackModels: ['mistral:7b-instruct', 'llama3.2:70b', 'phi3:4b'],\n    maxTokens: 4000,\n    temperature: 0.8,\n    timeout: 120000 // 2 minutes for local processing\n  },\n\n  async generateContent(prompt, systemPrompt = '', options = {}) {\n    const config = { ...this.config, ...options };\n    \n    try {\n      // Try Ollama first\n      const result = await this.callOllama(prompt, systemPrompt, config);\n      if (result) return result;\n      \n      // Fallback to LocalAI\n      return await this.callLocalAI(prompt, systemPrompt, config);\n    } catch (error) {\n      console.error('Local LLM generation failed:', error);\n      throw new Error(`Local AI generation failed: ${error.message}`);\n    }\n  },\n\n  async callOllama(prompt, systemPrompt, config) {\n    try {\n      const response = await fetch(`${config.ollamaUrl}/api/generate`, {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          model: config.preferredModel,\n          prompt: this.buildPrompt(systemPrompt, prompt),\n          stream: false,\n          options: {\n            temperature: config.temperature,\n            num_predict: config.maxTokens,\n            top_p: 0.9,\n            repeat_penalty: 1.1\n          }\n        }),\n        signal: AbortSignal.timeout(config.timeout)\n      });\n\n      if (!response.ok) {\n        throw new Error(`Ollama API error: ${response.status}`);\n      }\n\n      const data = await response.json();\n      return {\n        content: data.response,\n        model: config.preferredModel,\n        service: 'ollama',\n        tokens: data.eval_count || 0,\n        processingTime: data.total_duration || 0\n      };\n    } catch (error) {\n      console.warn('Ollama call failed, trying fallback:', error.message);\n      return null;\n    }\n  },\n\n  async callLocalAI(prompt, systemPrompt, config) {\n    try {\n      const response = await fetch(`${config.localAIUrl}/v1/chat/completions`, {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          model: config.preferredModel,\n          messages: [\n            { role: 'system', content: systemPrompt },\n            { role: 'user', content: prompt }\n          ],\n          max_tokens: config.maxTokens,\n          temperature: config.temperature,\n          top_p: 0.9\n        }),\n        signal: AbortSignal.timeout(config.timeout)\n      });\n\n      if (!response.ok) {\n        throw new Error(`LocalAI API error: ${response.status}`);\n      }\n\n      const data = await response.json();\n      return {\n        content: data.choices[0].message.content,\n        model: data.model,\n        service: 'localai',\n        tokens: data.usage?.total_tokens || 0,\n        processingTime: Date.now() // Approximate\n      };\n    } catch (error) {\n      console.error('LocalAI call failed:', error.message);\n      throw error;\n    }\n  },\n\n  buildPrompt(systemPrompt, userPrompt) {\n    if (!systemPrompt) return userPrompt;\n    return `<|system|>${systemPrompt}<|end|><|user|>${userPrompt}<|end|><|assistant|>`;\n  },\n\n  async checkModelAvailability() {\n    const status = {\n      ollama: { available: false, models: [] },\n      localai: { available: false, models: [] }\n    };\n\n    // Check Ollama\n    try {\n      const ollamaResponse = await fetch(`${this.config.ollamaUrl}/api/tags`, {\n        signal: AbortSignal.timeout(5000)\n      });\n      if (ollamaResponse.ok) {\n        const data = await ollamaResponse.json();\n        status.ollama.available = true;\n        status.ollama.models = data.models?.map(m => m.name) || [];\n      }\n    } catch (error) {\n      console.warn('Ollama not available:', error.message);\n    }\n\n    // Check LocalAI\n    try {\n      const localaiResponse = await fetch(`${this.config.localAIUrl}/v1/models`, {\n        signal: AbortSignal.timeout(5000)\n      });\n      if (localaiResponse.ok) {\n        const data = await localaiResponse.json();\n        status.localai.available = true;\n        status.localai.models = data.data?.map(m => m.id) || [];\n      }\n    } catch (error) {\n      console.warn('LocalAI not available:', error.message);\n    }\n\n    return status;\n  },\n\n  async optimizeForHardware() {\n    // Get system information\n    const systemInfo = {\n      totalMemory: process.memoryUsage().rss,\n      platform: process.platform,\n      arch: process.arch\n    };\n\n    // Adjust model selection based on available resources\n    const availableModels = await this.checkModelAvailability();\n    \n    // Recommend optimal model based on system specs\n    let recommendedModel = 'phi3:4b'; // Lightweight default\n    \n    if (systemInfo.totalMemory > 16 * 1024 * 1024 * 1024) { // 16GB+\n      recommendedModel = 'qwen2.5:32b';\n    } else if (systemInfo.totalMemory > 8 * 1024 * 1024 * 1024) { // 8GB+\n      recommendedModel = 'mistral:7b-instruct';\n    }\n\n    return {\n      recommendedModel,\n      systemInfo,\n      availableModels,\n      optimizationTips: this.getOptimizationTips(systemInfo)\n    };\n  },\n\n  getOptimizationTips(systemInfo) {\n    const tips = [];\n    \n    if (systemInfo.totalMemory < 8 * 1024 * 1024 * 1024) {\n      tips.push('Consider using smaller models like phi3:4b for better performance');\n      tips.push('Enable model quantization to reduce memory usage');\n    }\n    \n    if (systemInfo.platform === 'darwin') {\n      tips.push('Enable Metal acceleration for Apple Silicon Macs');\n    }\n    \n    if (systemInfo.platform === 'linux') {\n      tips.push('Consider using CUDA acceleration if NVIDIA GPU is available');\n    }\n    \n    tips.push('Use SSD storage for faster model loading');\n    tips.push('Close unnecessary applications to free up memory');\n    \n    return tips;\n  }\n};\n\n// Main execution for content generation\nconst contentData = $input.first().json;\nconst marketData = contentData.marketAnalysis || {};\n\n// Build comprehensive prompt for local model\nconst systemPrompt = `You are an advanced YouTube content strategist and viral content creator. Your expertise includes:\n\n🎯 CONTENT STRATEGY:\n- Viral content patterns and psychology\n- Audience engagement optimization\n- SEO and algorithm optimization\n- Multi-platform content adaptation\n\n🧠 CREATIVE INTELLIGENCE:\n- Trend analysis and prediction\n- Hook creation and retention tactics\n- Storytelling frameworks\n- Visual and audio optimization\n\n📊 PERFORMANCE OPTIMIZATION:\n- A/B testing strategies\n- Analytics-driven decisions\n- Conversion optimization\n- Brand consistency\n\nGenerate content that is:\n✅ Highly engaging and shareable\n✅ Optimized for YouTube algorithm\n✅ Trend-aware and timely\n✅ Professional yet accessible\n✅ Designed for specific duration requirements\n\nRespond ONLY with valid JSON format.`;\n\nconst userPrompt = `Create a comprehensive video content plan based on these parameters:\n\n📋 CONTENT REQUIREMENTS:\n- Duration: ${contentData.contentStrategy?.duration || 60} seconds\n- Content Type: ${contentData.contentStrategy?.contentType || 'educational'}\n- Tone: ${contentData.contentStrategy?.tone || 'engaging'}\n- Target Topics: ${contentData.contentStrategy?.topics?.join(', ') || 'trending topics'}\n\n🎯 MARKET CONTEXT:\n- Competition Level: ${marketData.marketConditions?.competitionLevel || 'moderate'}\n- Trending Topics: ${marketData.marketConditions?.trendingTopics?.join(', ') || 'AI, automation, productivity'}\n- Processing ID: ${contentData.processingId || 'local_' + Date.now()}\n\n📝 REQUIRED OUTPUT (JSON format):\n{\n  \"videoTitle\": \"Compelling, SEO-optimized title with hooks\",\n  \"videoDescription\": \"Detailed description with keywords and CTAs\",\n  \"videoScript\": \"Complete script with timing markers\",\n  \"visualCues\": [\"Array of visual elements needed\"],\n  \"audioCues\": [\"Background music and sound effects\"],\n  \"seoTags\": [\"Optimized tags for discoverability\"],\n  \"thumbnailPrompt\": \"AI prompt for thumbnail generation\",\n  \"hooks\": {\n    \"opening\": \"First 3-second hook\",\n    \"retention\": [\"Mid-video engagement points\"],\n    \"closing\": \"Strong CTA and subscribe prompt\"\n  },\n  \"contentStructure\": {\n    \"intro\": \"0-5 seconds\",\n    \"main\": \"5-50 seconds\",\n    \"outro\": \"50-60 seconds\"\n  },\n  \"viralPotential\": \"Score 1-10 with explanation\",\n  \"competitorAnalysis\": \"How this beats competition\",\n  \"optimizationNotes\": [\"Performance improvement suggestions\"]\n}\n\nMake this content EXCEPTIONAL and industry-leading!`;\n\n// Generate content using local AI\nconst startTime = Date.now();\nconst aiResponse = await localLLMService.generateContent(userPrompt, systemPrompt);\nconst processingTime = Date.now() - startTime;\n\n// Parse and validate the response\nlet parsedContent;\ntry {\n  // Extract JSON from response if wrapped in markdown\n  const jsonMatch = aiResponse.content.match(/```(?:json)?\\s*([\\s\\S]*?)```/) || [null, aiResponse.content];\n  parsedContent = JSON.parse(jsonMatch[1].trim());\n} catch (error) {\n  throw new Error(`Failed to parse AI response: ${error.message}`);\n}\n\n// Add local AI metadata\nparsedContent.localAI = {\n  model: aiResponse.model,\n  service: aiResponse.service,\n  processingTime,\n  tokens: aiResponse.tokens,\n  generatedAt: new Date().toISOString(),\n  offline: true\n};\n\nreturn {\n  json: {\n    ...contentData,\n    ...parsedContent,\n    metadata: {\n      ...contentData.metadata,\n      aiGeneration: parsedContent.localAI,\n      processingId: contentData.processingId || `local_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n    }\n  }\n};"
    },
    "local_image_generation": {
      "name": "🎨 Local Image Generation",
      "type": "n8n-nodes-base.code", 
      "description": "Local Stable Diffusion integration replacing Replicate API",
      "code": "// LOCAL IMAGE GENERATION - STABLE DIFFUSION\n// Replaces Replicate API with local Stable Diffusion\n\nconst localImageService = {\n  config: {\n    stableDiffusionUrl: process.env.STABLE_DIFFUSION_URL || 'http://localhost:7860',\n    comfyUIUrl: process.env.COMFYUI_URL || 'http://localhost:8188',\n    invokeAIUrl: process.env.INVOKEAI_URL || 'http://localhost:9090',\n    defaultModel: 'sd_xl_base_1.0.safetensors',\n    timeout: 300000, // 5 minutes for image generation\n    maxRetries: 3\n  },\n\n  async generateImage(prompt, options = {}) {\n    const config = { ...this.config, ...options };\n    \n    try {\n      // Try Automatic1111 WebUI first\n      let result = await this.callAutomatic1111(prompt, config);\n      if (result) return result;\n      \n      // Fallback to ComfyUI\n      result = await this.callComfyUI(prompt, config);\n      if (result) return result;\n      \n      // Fallback to InvokeAI\n      return await this.callInvokeAI(prompt, config);\n    } catch (error) {\n      console.error('Local image generation failed:', error);\n      throw new Error(`Local image generation failed: ${error.message}`);\n    }\n  },\n\n  async callAutomatic1111(prompt, config) {\n    try {\n      const enhancedPrompt = this.enhancePrompt(prompt);\n      \n      const response = await fetch(`${config.stableDiffusionUrl}/sdapi/v1/txt2img`, {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          prompt: enhancedPrompt,\n          negative_prompt: 'blurry, low quality, distorted, ugly, bad anatomy, watermark, text',\n          width: 1280,\n          height: 720,\n          steps: 20,\n          cfg_scale: 7,\n          sampler_name: 'DPM++ 2M Karras',\n          batch_size: 1,\n          n_iter: 1,\n          seed: -1,\n          restore_faces: true,\n          tiling: false,\n          enable_hr: true,\n          hr_scale: 1.5,\n          hr_upscaler: 'Latent',\n          denoising_strength: 0.7\n        }),\n        signal: AbortSignal.timeout(config.timeout)\n      });\n\n      if (!response.ok) {\n        throw new Error(`Automatic1111 API error: ${response.status}`);\n      }\n\n      const data = await response.json();\n      \n      if (!data.images || data.images.length === 0) {\n        throw new Error('No images generated');\n      }\n\n      return {\n        imageData: data.images[0], // Base64 encoded\n        service: 'automatic1111',\n        prompt: enhancedPrompt,\n        parameters: data.parameters,\n        info: data.info\n      };\n    } catch (error) {\n      console.warn('Automatic1111 call failed:', error.message);\n      return null;\n    }\n  },\n\n  async callComfyUI(prompt, config) {\n    try {\n      // ComfyUI workflow for text-to-image\n      const workflow = {\n        \"3\": {\n          \"inputs\": {\n            \"seed\": Math.floor(Math.random() * 1000000),\n            \"steps\": 20,\n            \"cfg\": 7,\n            \"sampler_name\": \"euler\",\n            \"scheduler\": \"normal\",\n            \"denoise\": 1,\n            \"model\": [\"4\", 0],\n            \"positive\": [\"6\", 0],\n            \"negative\": [\"7\", 0],\n            \"latent_image\": [\"5\", 0]\n          },\n          \"class_type\": \"KSampler\"\n        },\n        \"4\": {\n          \"inputs\": {\n            \"ckpt_name\": config.defaultModel\n          },\n          \"class_type\": \"CheckpointLoaderSimple\"\n        },\n        \"5\": {\n          \"inputs\": {\n            \"width\": 1280,\n            \"height\": 720,\n            \"batch_size\": 1\n          },\n          \"class_type\": \"EmptyLatentImage\"\n        },\n        \"6\": {\n          \"inputs\": {\n            \"text\": this.enhancePrompt(prompt),\n            \"clip\": [\"4\", 1]\n          },\n          \"class_type\": \"CLIPTextEncode\"\n        },\n        \"7\": {\n          \"inputs\": {\n            \"text\": \"blurry, low quality, distorted, ugly, bad anatomy, watermark, text\",\n            \"clip\": [\"4\", 1]\n          },\n          \"class_type\": \"CLIPTextEncode\"\n        },\n        \"8\": {\n          \"inputs\": {\n            \"samples\": [\"3\", 0],\n            \"vae\": [\"4\", 2]\n          },\n          \"class_type\": \"VAEDecode\"\n        },\n        \"9\": {\n          \"inputs\": {\n            \"filename_prefix\": \"ComfyUI\",\n            \"images\": [\"8\", 0]\n          },\n          \"class_type\": \"SaveImage\"\n        }\n      };\n\n      // Queue the workflow\n      const queueResponse = await fetch(`${config.comfyUIUrl}/prompt`, {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ prompt: workflow }),\n        signal: AbortSignal.timeout(config.timeout)\n      });\n\n      if (!queueResponse.ok) {\n        throw new Error(`ComfyUI queue error: ${queueResponse.status}`);\n      }\n\n      const queueData = await queueResponse.json();\n      const promptId = queueData.prompt_id;\n\n      // Poll for completion\n      const result = await this.pollComfyUIResult(promptId, config);\n      \n      return {\n        imageData: result.imageData,\n        service: 'comfyui',\n        prompt: this.enhancePrompt(prompt),\n        promptId\n      };\n    } catch (error) {\n      console.warn('ComfyUI call failed:', error.message);\n      return null;\n    }\n  },\n\n  async pollComfyUIResult(promptId, config, maxAttempts = 60) {\n    for (let attempt = 0; attempt < maxAttempts; attempt++) {\n      try {\n        const historyResponse = await fetch(`${config.comfyUIUrl}/history/${promptId}`);\n        \n        if (historyResponse.ok) {\n          const history = await historyResponse.json();\n          \n          if (history[promptId] && history[promptId].status?.completed) {\n            const outputs = history[promptId].outputs;\n            const saveImageNode = Object.values(outputs).find(output => output.images);\n            \n            if (saveImageNode && saveImageNode.images.length > 0) {\n              const imageInfo = saveImageNode.images[0];\n              \n              // Download the image\n              const imageResponse = await fetch(`${config.comfyUIUrl}/view?filename=${imageInfo.filename}&subfolder=${imageInfo.subfolder}&type=${imageInfo.type}`);\n              \n              if (imageResponse.ok) {\n                const imageBuffer = await imageResponse.arrayBuffer();\n                const imageData = Buffer.from(imageBuffer).toString('base64');\n                \n                return { imageData };\n              }\n            }\n          }\n        }\n        \n        // Wait before next poll\n        await new Promise(resolve => setTimeout(resolve, 5000));\n      } catch (error) {\n        console.warn(`ComfyUI poll attempt ${attempt + 1} failed:`, error.message);\n      }\n    }\n    \n    throw new Error('ComfyUI generation timeout');\n  },\n\n  async callInvokeAI(prompt, config) {\n    try {\n      const response = await fetch(`${config.invokeAIUrl}/api/v1/generate`, {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          prompt: this.enhancePrompt(prompt),\n          width: 1280,\n          height: 720,\n          steps: 20,\n          cfg_scale: 7,\n          sampler: 'k_euler_a',\n          seed: Math.floor(Math.random() * 1000000)\n        }),\n        signal: AbortSignal.timeout(config.timeout)\n      });\n\n      if (!response.ok) {\n        throw new Error(`InvokeAI API error: ${response.status}`);\n      }\n\n      const data = await response.json();\n      \n      return {\n        imageData: data.image, // Base64 encoded\n        service: 'invokeai',\n        prompt: this.enhancePrompt(prompt),\n        seed: data.seed\n      };\n    } catch (error) {\n      console.warn('InvokeAI call failed:', error.message);\n      throw error;\n    }\n  },\n\n  enhancePrompt(originalPrompt) {\n    const qualityTags = [\n      'professional YouTube thumbnail style',\n      'high contrast',\n      'eye-catching',\n      '1280x720 resolution',\n      'vibrant colors',\n      'clear focal point',\n      'masterpiece',\n      'best quality',\n      'ultra detailed',\n      'sharp focus',\n      'perfect lighting'\n    ];\n    \n    return `${originalPrompt}, ${qualityTags.join(', ')}`;\n  },\n\n  async checkServiceAvailability() {\n    const status = {\n      automatic1111: false,\n      comfyui: false,\n      invokeai: false\n    };\n\n    // Check Automatic1111\n    try {\n      const response = await fetch(`${this.config.stableDiffusionUrl}/sdapi/v1/sd-models`, {\n        signal: AbortSignal.timeout(5000)\n      });\n      status.automatic1111 = response.ok;\n    } catch (error) {\n      console.warn('Automatic1111 not available:', error.message);\n    }\n\n    // Check ComfyUI\n    try {\n      const response = await fetch(`${this.config.comfyUIUrl}/system_stats`, {\n        signal: AbortSignal.timeout(5000)\n      });\n      status.comfyui = response.ok;\n    } catch (error) {\n      console.warn('ComfyUI not available:', error.message);\n    }\n\n    // Check InvokeAI\n    try {\n      const response = await fetch(`${this.config.invokeAIUrl}/api/v1/models`, {\n        signal: AbortSignal.timeout(5000)\n      });\n      status.invokeai = response.ok;\n    } catch (error) {\n      console.warn('InvokeAI not available:', error.message);\n    }\n\n    return status;\n  }\n};\n\n// Main execution for image generation\nconst contentData = $input.first().json;\nconst thumbnailPrompt = contentData.thumbnailPrompt || 'Professional YouTube thumbnail';\n\n// Generate thumbnail image\nconst startTime = Date.now();\nconst imageResult = await localImageService.generateImage(thumbnailPrompt, {\n  width: 1280,\n  height: 720\n});\nconst processingTime = Date.now() - startTime;\n\n// Convert base64 to data URL\nconst imageUrl = `data:image/png;base64,${imageResult.imageData}`;\n\nreturn {\n  json: {\n    ...contentData,\n    imageGeneration: {\n      imageUrl,\n      imageData: imageResult.imageData,\n      service: imageResult.service,\n      prompt: imageResult.prompt,\n      processingTime,\n      generatedAt: new Date().toISOString(),\n      offline: true\n    }\n  }\n};"
    }
  }\n}

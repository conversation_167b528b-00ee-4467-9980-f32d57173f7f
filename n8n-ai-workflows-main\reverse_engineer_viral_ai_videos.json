{"name": "The Recap AI - Reverse Engineer Short Form AI Video", "nodes": [{"parameters": {"formTitle": "Reverse Engineer Short Form Video", "formDescription": "Provide an IG Reel url or a TikTok url.", "formFields": {"values": [{"fieldLabel": "Url", "placeholder": "https://www.instagram.com/reel/DKzbAvyoCMJ/", "requiredField": true}]}, "options": {}}, "type": "n8n-nodes-base.formTrigger", "typeVersion": 2.2, "position": [40, 100], "id": "feeaf14f-cc61-4a85-9a80-8456356aa551", "name": "form_trigger", "webhookId": "8db8f807-4b93-4a74-81b6-290cc1d459e8"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $('form_trigger').item.json.Url }}", "rightValue": "=instagram.com", "operator": {"type": "string", "operation": "contains"}, "id": "c5641e00-9155-4ca8-b4cd-90798531103a"}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "4da252f9-8606-476f-85a3-23f95b6f6e72", "leftValue": "={{ $('form_trigger').item.json.Url }}", "rightValue": "tiktok.com", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [540, 100], "id": "a473cf43-ed18-479b-bc08-53173715c355", "name": "switch_platform"}, {"parameters": {"method": "POST", "url": " https://api.apify.com/v2/acts/apify~instagram-api-scraper/run-sync-get-dataset-items", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"directUrls\": [\n      \"{{ $('form_trigger').item.json.Url }}\"\n    ],\n    \"resultsLimit\": 1,\n    \"resultsType\": \"posts\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [880, -220], "id": "9361eaaa-5a19-4001-8809-e9ea7c44125d", "name": "scrape_ig_reel", "credentials": {"httpHeaderAuth": {"id": "JUNCpa8hxAmgMQQS", "name": "Apify API"}}}, {"parameters": {"url": "={{ $('scrape_ig_reel').item.json.videoUrl }}", "options": {"response": {"response": {"responseFormat": "file"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1340, -220], "id": "abe8f70a-2b3a-4c69-ba07-d6ee790f2302", "name": "download_ig_reel"}, {"parameters": {"method": "POST", "url": "https://api.apify.com/v2/acts/apidojo~tiktok-scraper/run-sync-get-dataset-items", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"maxItems\": 1,\n    \"startUrls\": [\n        \"{{ $('form_trigger').item.json.Url }}\"\n    ]\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [880, 300], "id": "aad88865-ba23-4619-a172-8413863b127d", "name": "scrape_tiktok", "credentials": {"httpHeaderAuth": {"id": "JUNCpa8hxAmgMQQS", "name": "Apify API"}}}, {"parameters": {"url": "={{ $('scrape_tiktok').item.json.video.url }}", "options": {"response": {"response": {"responseFormat": "file"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1340, 300], "id": "efe3daec-2f18-43c0-8e28-4ce84c33a68b", "name": "download_tiktok"}, {"parameters": {"operation": "binaryToPropery", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [880, 520], "id": "e6124831-b97f-4eb7-b3c0-8192339d2aa3", "name": "tiktok_to_base64"}, {"parameters": {"assignments": {"assignments": [{"id": "eccbeb92-af90-4244-8e23-2cd3e2616a07", "name": "result", "value": "={{ $json.candidates[0].content.parts[0].text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1340, 520], "id": "380927ac-d7cb-4c66-9020-c905d0d0932d", "name": "set_tiktok_result"}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro:generateContent", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"contents\": [\n    {\n      \"parts\": [\n        {\n          \"inline_data\": {\n            \"mime_type\": \"video/mp4\",\n            \"data\": \"{{ $json.data }}\"\n          }\n        },\n        {\n          \"text\": {{ JSON.stringify($('build_final_tiktok_prompt').item.json.prompt) }}\n        }\n      ]\n    }\n  ]\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1120, 520], "id": "246e1bc4-8a3e-483b-acb9-3fdd87ed51b5", "name": "analyze_tiktok", "credentials": {"httpHeaderAuth": {"id": "abcMckGk9RG9i0Ba", "name": "Google Gemini"}}}, {"parameters": {"assignments": {"assignments": [{"id": "f302b2c5-43cb-41f0-91f5-e91f72397ae4", "name": "prompt", "value": "=**Your Persona:** You are a \"Digital Twin Architect,\" a specialized AI analyst with a dual mastery of forensic video analysis and generative AI video synthesis. Your function is to deconstruct a source video into its absolute, quantifiable components and then architect a \"Generative Manifest\"—a flawless, multi-part prompt designed to compel an AI video model to render a perfect digital replica. You do not interpret; you transcribe reality into data.\n\n**Your Mission:** Your sole objective is to analyze the provided short-form video and produce a **Generative Manifest**. This manifest must be so detailed that it leaves no room for creative interpretation by the target AI video model (Google Veo, Sora, etc.). The goal is not a similar video; it is a shot-for-shot, motion-for-motion, photon-for-photon digital twin.\n\n**The Process: Forensic Deconstruction**\n\nBefore writing the manifest, you will perform a deep forensic analysis, dissecting the video across multiple layers. Think like a VFX artist creating a CG replica of a live-action shot.\n\n**Layer 1: The Global Aesthetic & Physics Core**\nThis is the foundational DNA of the video.\n*   **Visual Medium:** Is it emulating a specific film stock (e.g., Kodak Vision3 50D 35mm, Ektachrome 16mm)? Or is it a specific digital camera sensor (e.g., ARRI Alexa 65, RED Komodo, a high-end smartphone sensor)? Note the inherent grain, noise pattern, and sharpness.\n*   **Color Science & Grading:** Be precise. \"Cinematic teal and orange\" is too vague. Specify \"A high-contrast color grade with crushed blacks, lifted mid-tones, desaturated blues, and a heavy push of orange into the skin tones. The overall white balance is shifted slightly warm, around 4500K.\"\n*   **Lens Emulation:** Identify the characteristics of the lens. \"Emulating a Cooke Anamorphic/i SF 50mm lens, creating distinct oval bokeh, soft lens flares, and slight edge distortion.\" Or \"Emulating a sterile, hyper-sharp Zeiss Supreme Prime lens with zero chromatic aberration.\"\n*   **Physics & Reality Engine:** Define the environmental physics. \"Hyperrealistic physics model. All fluid dynamics (water, steam) must be viscous and turbulent. Fabric simulation must show weight and react to air currents. Particle effects (dust motes, sparks) must be dense and interact with light.\"\n\n**Layer 2: Per-Shot Deconstruction**\nBreak the video into a numbered sequence of shots. For EACH shot, you must detail the following:\n\n*   **Shot ID & Absolute Timing:**\n    *   `[SHOT X]`\n    *   `DURATION:` Start time to end time (e.g., 0.0s - 3.2s).\n\n*   **Camera Specification:**\n    *   `FRAMING:` Extreme Close-Up (ECU), Medium Shot (MS), etc.\n    *   `ANGLE:` Low Angle, High Angle, etc.\n    *   `MOVEMENT:` Be precise. Not \"pan,\" but \"A slow, 15-degree pan to the right with a smooth ease-in and ease-out, taking 2.0 seconds, starting at the 1.0s mark of the shot. The motion is perfectly stable, as if on a geared head tripod.\" Not \"handheld,\" but \"Subtle, high-frequency handheld shake emulating a person breathing, with a maximum vertical displacement of 2 pixels.\"\n\n*   **Subject & Action Choreography:**\n    *   `SUBJECT(S):` Hyper-specific descriptions. \"Male, early 30s, olive skin, short black hair, with a 3-day stubble. Wearing a navy blue heavyweight cotton t-shirt with visible fabric weave and slight pilling on the collar.\"\n    *   `ACTION MANIFEST:` A time-stamped log of all actions. \"At 0.5s, the subject's right hand enters the frame from the left. At 0.7s, his fingers make contact with the ceramic mug. At 1.2s, a micro-expression of concentration appears: slight furrowing of the brow, lips press together.\"\n\n*   **Lighting & Environmental Detail:**\n    *   `LIGHTING BLUEPRINT:` \"A single, large, soft key light (emulating a 4x4 silk diffuser) is positioned camera left and slightly high, creating a Rembrandt lighting pattern on the subject's face. A weak, cool-toned fill light (approx. 6000K) from the right prevents shadows from being completely black. A hard rim light from behind separates the subject from the background.\"\n    *   `ENVIRONMENT STATE:` \"The background shows a plaster wall with visible texture and a 2-inch scuff mark three feet to the right of the subject's head. Dust motes are visible, floating slowly in the shafts of light.\"\n\n*   **Focus & Depth Plane:**\n    *   `DEPTH OF FIELD:` \"Extremely shallow depth of field (emulating f/1.4 aperture). The focal plane is locked precisely on the subject's leading eyeball.\"\n    *   `FOCUS DYNAMICS:` \"At 2.5s, a slow, deliberate rack focus begins, shifting the focal plane from the subject's eye to the coffee cup in their hand over 0.7 seconds.\"", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [300, 100], "id": "b013129b-1662-4f47-8f89-d7668368b507", "name": "set_base_prompt"}, {"parameters": {"assignments": {"assignments": [{"id": "aab55ec6-6c4b-4e60-a576-62812f91299f", "name": "prompt", "value": "={{ $('set_base_prompt').item.json.prompt }}\n---\nVideo Title: {{ $json.title }}\nVideo Hashtags: {{ $json.hashtags.join(\",\") }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1120, 300], "id": "5ac6e027-8874-4541-8d2a-da1db0d76586", "name": "build_final_tiktok_prompt"}, {"parameters": {"assignments": {"assignments": [{"id": "aab55ec6-6c4b-4e60-a576-62812f91299f", "name": "prompt", "value": "={{ $('set_base_prompt').item.json.prompt }}\n---\nVideo Caption: {{ $json.caption }}\nVideo Hashtags: {{ $json.hashtags.join(\",\") }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1120, -220], "id": "3ff33483-c6f5-4022-b4ba-4fc8f75b1141", "name": "build_final_ig_prompt"}, {"parameters": {"operation": "binaryToPropery", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [880, 20], "id": "e57ad5a9-be69-406c-a491-dba6bccd04a0", "name": "ig_to_base64"}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro:generateContent", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"contents\": [\n    {\n      \"parts\": [\n        {\n          \"inline_data\": {\n            \"mime_type\": \"video/mp4\",\n            \"data\": \"{{ $json.data }}\"\n          }\n        },\n        {\n          \"text\": {{ JSON.stringify($('build_final_ig_prompt').item.json.prompt) }}\n        }\n      ]\n    }\n  ]\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1120, 20], "id": "c7602bc6-c215-45da-8d1e-1efb88fd1598", "name": "analyze_ig_reel", "credentials": {"httpHeaderAuth": {"id": "abcMckGk9RG9i0Ba", "name": "Google Gemini"}}}, {"parameters": {"assignments": {"assignments": [{"id": "eccbeb92-af90-4244-8e23-2cd3e2616a07", "name": "result", "value": "={{ $json.candidates[0].content.parts[0].text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1340, 20], "id": "08b0dd73-dcdb-4c5f-bff3-b7ffeab4d7f1", "name": "set_ig_result"}, {"parameters": {"content": "## 1. Url Input + Build Video Reverse Engineering Prompt", "height": 540, "width": 820, "color": 5}, "type": "n8n-nodes-base.stickyNote", "position": [-40, -100], "typeVersion": 1, "id": "253c56f0-dfde-4fa8-abe1-3d1c77c473c4", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "## 2.1 Download IG Reel + Analyze Video", "height": 500, "width": 840, "color": 6}, "type": "n8n-nodes-base.stickyNote", "position": [800, -300], "typeVersion": 1, "id": "d12d06e6-4180-4147-8fe7-7646549e0420", "name": "Sticky Note1"}, {"parameters": {"content": "## 2.2 Download TikTok + Analyze Video", "height": 480, "width": 840, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [800, 220], "typeVersion": 1, "id": "b328af52-a5fc-47fb-a626-12189cebb917", "name": "Sticky Note2"}, {"parameters": {"authentication": "oAuth2", "select": "channel", "channelId": {"__rl": true, "value": "C08KC39K8DR", "mode": "list", "cachedResultName": "ai-tools-content"}, "text": "=*AI Video Generator Prompt:*\n\n```\n{{ $json.permalink }}\n```", "otherOptions": {"includeLinkToWorkflow": false}}, "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [2240, 120], "id": "bb780611-a300-4ca0-98a5-e1902c51c6bf", "name": "send_prompt_msg", "webhookId": "ca19d833-4129-4c90-a668-295227a9c8ab", "credentials": {"slackOAuth2Api": {"id": "iKdTzmZ6YuYaaRG4", "name": "<PERSON><PERSON><PERSON> (OAuth)"}}}, {"parameters": {"operation": "toText", "sourceProperty": "result", "binaryPropertyName": "prompt.md", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [1800, 120], "id": "7c99f7c7-fe79-4344-a273-355cfe26f28e", "name": "Convert to File"}, {"parameters": {"authentication": "oAuth2", "resource": "file", "binaryPropertyName": "prompt.md", "options": {}}, "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [2020, 120], "id": "7a88bdaf-6f5a-4c90-8059-21ffc6e165ba", "name": "upload_file", "webhookId": "8fdada13-d12e-4aea-87a7-63382dbb04b0", "credentials": {"slackOAuth2Api": {"id": "iKdTzmZ6YuYaaRG4", "name": "<PERSON><PERSON><PERSON> (OAuth)"}}}, {"parameters": {"content": "## 3. Share Prompt In Slack", "height": 540, "width": 840}, "type": "n8n-nodes-base.stickyNote", "position": [1660, -100], "typeVersion": 1, "id": "a7c4af22-8c48-4fdb-94cd-a553829c0aef", "name": "Sticky Note3"}], "pinData": {}, "connections": {"form_trigger": {"main": [[{"node": "set_base_prompt", "type": "main", "index": 0}]]}, "switch_platform": {"main": [[{"node": "scrape_ig_reel", "type": "main", "index": 0}], [{"node": "scrape_tiktok", "type": "main", "index": 0}]]}, "scrape_ig_reel": {"main": [[{"node": "build_final_ig_prompt", "type": "main", "index": 0}]]}, "scrape_tiktok": {"main": [[{"node": "build_final_tiktok_prompt", "type": "main", "index": 0}]]}, "download_tiktok": {"main": [[{"node": "tiktok_to_base64", "type": "main", "index": 0}]]}, "tiktok_to_base64": {"main": [[{"node": "analyze_tiktok", "type": "main", "index": 0}]]}, "analyze_tiktok": {"main": [[{"node": "set_tiktok_result", "type": "main", "index": 0}]]}, "set_base_prompt": {"main": [[{"node": "switch_platform", "type": "main", "index": 0}]]}, "build_final_tiktok_prompt": {"main": [[{"node": "download_tiktok", "type": "main", "index": 0}]]}, "build_final_ig_prompt": {"main": [[{"node": "download_ig_reel", "type": "main", "index": 0}]]}, "download_ig_reel": {"main": [[{"node": "ig_to_base64", "type": "main", "index": 0}]]}, "ig_to_base64": {"main": [[{"node": "analyze_ig_reel", "type": "main", "index": 0}]]}, "analyze_ig_reel": {"main": [[{"node": "set_ig_result", "type": "main", "index": 0}]]}, "set_ig_result": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}, "set_tiktok_result": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}, "Convert to File": {"main": [[{"node": "upload_file", "type": "main", "index": 0}]]}, "upload_file": {"main": [[{"node": "send_prompt_msg", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "bac5fec1-da37-4183-bde0-ff33cce0518e", "meta": {"templateCredsSetupCompleted": true, "instanceId": "06e5009344f682419c20ccd4ecdcb5223bbb91761882af93ac6d468dbc2cbf8d"}, "id": "hphJfSePqnG8grxP", "tags": []}
# Tratamento de Mensagens Recebidas na Evolution API com n8n

Este repositório contém os fluxos desenvolvidos durante a live [Tratamento de Mensagens Recebidas na Evolution API - Texto, Imagem, Áudio e Mais com n8n](https://www.youtube.com/live/94d6hRRjY4g). A live mostra como configurar fluxos no **n8n** para tratar automaticamente diferentes tipos de mensagens recebidas pela **Evolution API**, incluindo textos, imagens, áudios, vídeos e documentos.

## Descrição

Durante a live, aprendemos a integrar a **Evolution API** ao **n8n** para processar conteúdos multimídia recebidos via API de forma automatizada. A configuração foi feita utilizando **webhooks** para tratar diferentes formatos de mensagem.

## O que você vai aprender

- **Configurar um fluxo automatizado** para receber e processar mensagens multimídia pela Evolution API.
- **Usar o n8n** para organizar e tratar dados de textos, imagens, áudios, vídeos e documentos.
- **Criar webhooks no n8n** para cada tipo de mensagem, permitindo personalização no processamento de cada mídia.

## Como usar este repositório

1. **Clonar o Repositório**

   Baixe o Json do fluxo escolhido e importe no N8N.
{"name": "YouTube Idea Generator", "flow": [{"id": 1, "module": "google-sheets:filterRows", "version": 2, "parameters": {"__IMTCONN__": 4122218}, "mapper": {"from": "drive", "sheetId": "Sheet1", "sortOrder": "asc", "spreadsheetId": "1HXKnECsA036cMvbNsk8QuzM-MqTB2RlfLCz_O-6NjY0", "tableFirstRow": "A1:CZ1", "includesHeaders": true, "valueRenderOption": "FORMATTED_VALUE", "dateTimeRenderOption": "FORMATTED_STRING"}, "metadata": {"designer": {"x": 0, "y": 0}, "restore": {"expect": {"from": {"label": "Select from My Drive"}, "orderBy": {"mode": "chose"}, "sheetId": {"mode": "chose", "label": "Sheet1"}, "sortOrder": {"mode": "chose", "label": "Ascending"}, "spreadsheetId": {"mode": "chose", "label": "YouTube Channels"}, "tableFirstRow": {"label": "A-CZ"}, "includesHeaders": {"mode": "chose", "label": "Yes"}, "valueRenderOption": {"mode": "chose", "label": "Formatted value"}, "dateTimeRenderOption": {"mode": "chose", "label": "Formatted string"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "google"}, "label": "My Google connection (<EMAIL>)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:google", "label": "Connection", "required": true}], "expect": [{"name": "from", "type": "select", "label": "Search Method", "required": true, "validate": {"enum": ["drive", "share"]}}, {"name": "valueRenderOption", "type": "select", "label": "Value render option", "validate": {"enum": ["FORMATTED_VALUE", "UNFORMATTED_VALUE", "FORMULA"]}}, {"name": "dateTimeRenderOption", "type": "select", "label": "Date and time render option", "validate": {"enum": ["SERIAL_NUMBER", "FORMATTED_STRING"]}}, {"name": "limit", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Limit"}, {"name": "spreadsheetId", "type": "select", "label": "Spreadsheet ID", "required": true}, {"name": "sheetId", "type": "select", "label": "Sheet Name", "required": true}, {"name": "includesHeaders", "type": "select", "label": "Table contains headers", "required": true, "validate": {"enum": [true, false]}}, {"name": "tableFirstRow", "type": "select", "label": "Column range", "required": true, "validate": {"enum": ["A1:Z1", "A1:BZ1", "A1:CZ1", "A1:DZ1", "A1:MZ1", "A1:ZZ1", "A1:AZZ1", "A1:BZZ1", "A1:CZZ1", "A1:DZZ1", "A1:MZZ1", "A1:ZZZ1"]}}, {"name": "filter", "type": "filter", "label": "Filter", "options": "rpc://google-sheets/2/rpcGetFilterKeys?includesHeaders=true"}, {"name": "orderBy", "type": "select", "label": "Order by"}, {"name": "sortOrder", "type": "select", "label": "Sort order", "validate": {"enum": ["asc", "desc"]}}], "interface": [{"name": "__IMTLENGTH__", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total number of bundles"}, {"name": "__IMTINDEX__", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Bundle order position"}, {"name": "__ROW_NUMBER__", "type": "number", "label": "Row number"}, {"name": "__SPREADSHEET_ID__", "type": "text", "label": "Spreadsheet ID"}, {"name": "__SHEET__", "type": "text", "label": "Sheet"}, {"name": "0", "type": "text", "label": "Channel URL (A)"}, {"name": "1", "type": "text", "label": "(B)"}, {"name": "2", "type": "text", "label": "(C)"}, {"name": "3", "type": "text", "label": "(D)"}, {"name": "4", "type": "text", "label": "(E)"}, {"name": "5", "type": "text", "label": "(F)"}, {"name": "6", "type": "text", "label": "(G)"}, {"name": "7", "type": "text", "label": "(H)"}, {"name": "8", "type": "text", "label": "(I)"}, {"name": "9", "type": "text", "label": "(J)"}, {"name": "10", "type": "text", "label": "(K)"}, {"name": "11", "type": "text", "label": "(L)"}, {"name": "12", "type": "text", "label": "(M)"}, {"name": "13", "type": "text", "label": "(N)"}, {"name": "14", "type": "text", "label": "(O)"}, {"name": "15", "type": "text", "label": "(P)"}, {"name": "16", "type": "text", "label": "(Q)"}, {"name": "17", "type": "text", "label": "(R)"}, {"name": "18", "type": "text", "label": "(S)"}, {"name": "19", "type": "text", "label": "(T)"}, {"name": "20", "type": "text", "label": "(U)"}, {"name": "21", "type": "text", "label": "(V)"}, {"name": "22", "type": "text", "label": "(W)"}, {"name": "23", "type": "text", "label": "(X)"}, {"name": "24", "type": "text", "label": "(Y)"}, {"name": "25", "type": "text", "label": "(Z)"}, {"name": "26", "type": "text", "label": "(AA)"}, {"name": "27", "type": "text", "label": "(AB)"}, {"name": "28", "type": "text", "label": "(AC)"}, {"name": "29", "type": "text", "label": "(AD)"}, {"name": "30", "type": "text", "label": "(AE)"}, {"name": "31", "type": "text", "label": "(AF)"}, {"name": "32", "type": "text", "label": "(AG)"}, {"name": "33", "type": "text", "label": "(AH)"}, {"name": "34", "type": "text", "label": "(AI)"}, {"name": "35", "type": "text", "label": "(AJ)"}, {"name": "36", "type": "text", "label": "(AK)"}, {"name": "37", "type": "text", "label": "(AL)"}, {"name": "38", "type": "text", "label": "(AM)"}, {"name": "39", "type": "text", "label": "(AN)"}, {"name": "40", "type": "text", "label": "(AO)"}, {"name": "41", "type": "text", "label": "(AP)"}, {"name": "42", "type": "text", "label": "(AQ)"}, {"name": "43", "type": "text", "label": "(AR)"}, {"name": "44", "type": "text", "label": "(AS)"}, {"name": "45", "type": "text", "label": "(AT)"}, {"name": "46", "type": "text", "label": "(AU)"}, {"name": "47", "type": "text", "label": "(AV)"}, {"name": "48", "type": "text", "label": "(AW)"}, {"name": "49", "type": "text", "label": "(AX)"}, {"name": "50", "type": "text", "label": "(AY)"}, {"name": "51", "type": "text", "label": "(AZ)"}, {"name": "52", "type": "text", "label": "(BA)"}, {"name": "53", "type": "text", "label": "(BB)"}, {"name": "54", "type": "text", "label": "(BC)"}, {"name": "55", "type": "text", "label": "(BD)"}, {"name": "56", "type": "text", "label": "(BE)"}, {"name": "57", "type": "text", "label": "(BF)"}, {"name": "58", "type": "text", "label": "(BG)"}, {"name": "59", "type": "text", "label": "(BH)"}, {"name": "60", "type": "text", "label": "(BI)"}, {"name": "61", "type": "text", "label": "(BJ)"}, {"name": "62", "type": "text", "label": "(BK)"}, {"name": "63", "type": "text", "label": "(BL)"}, {"name": "64", "type": "text", "label": "(BM)"}, {"name": "65", "type": "text", "label": "(BN)"}, {"name": "66", "type": "text", "label": "(BO)"}, {"name": "67", "type": "text", "label": "(BP)"}, {"name": "68", "type": "text", "label": "(BQ)"}, {"name": "69", "type": "text", "label": "(BR)"}, {"name": "70", "type": "text", "label": "(BS)"}, {"name": "71", "type": "text", "label": "(BT)"}, {"name": "72", "type": "text", "label": "(BU)"}, {"name": "73", "type": "text", "label": "(BV)"}, {"name": "74", "type": "text", "label": "(BW)"}, {"name": "75", "type": "text", "label": "(BX)"}, {"name": "76", "type": "text", "label": "(BY)"}, {"name": "77", "type": "text", "label": "(BZ)"}, {"name": "78", "type": "text", "label": "(CA)"}, {"name": "79", "type": "text", "label": "(CB)"}, {"name": "80", "type": "text", "label": "(CC)"}, {"name": "81", "type": "text", "label": "(CD)"}, {"name": "82", "type": "text", "label": "(CE)"}, {"name": "83", "type": "text", "label": "(CF)"}, {"name": "84", "type": "text", "label": "(CG)"}, {"name": "85", "type": "text", "label": "(CH)"}, {"name": "86", "type": "text", "label": "(CI)"}, {"name": "87", "type": "text", "label": "(CJ)"}, {"name": "88", "type": "text", "label": "(CK)"}, {"name": "89", "type": "text", "label": "(CL)"}, {"name": "90", "type": "text", "label": "(CM)"}, {"name": "91", "type": "text", "label": "(CN)"}, {"name": "92", "type": "text", "label": "(CO)"}, {"name": "93", "type": "text", "label": "(CP)"}, {"name": "94", "type": "text", "label": "(CQ)"}, {"name": "95", "type": "text", "label": "(CR)"}, {"name": "96", "type": "text", "label": "(CS)"}, {"name": "97", "type": "text", "label": "(CT)"}, {"name": "98", "type": "text", "label": "(CU)"}, {"name": "99", "type": "text", "label": "(CV)"}, {"name": "100", "type": "text", "label": "(CW)"}, {"name": "101", "type": "text", "label": "(CX)"}, {"name": "102", "type": "text", "label": "(CY)"}, {"name": "103", "type": "text", "label": "(CZ)"}]}}, {"id": 3, "module": "builtin:BasicAggregator", "version": 1, "parameters": {"feeder": 1}, "mapper": {"0": "{{1.`0`}}", "1": "{{1.`1`}}", "2": "{{1.`2`}}", "3": "{{1.`3`}}", "4": "{{1.`4`}}", "5": "{{1.`5`}}", "6": "{{1.`6`}}", "7": "{{1.`7`}}", "8": "{{1.`8`}}", "9": "{{1.`9`}}", "10": "{{1.`10`}}", "11": "{{1.`11`}}", "12": "{{1.`12`}}", "13": "{{1.`13`}}", "14": "{{1.`14`}}", "15": "{{1.`15`}}", "16": "{{1.`16`}}", "17": "{{1.`17`}}", "18": "{{1.`18`}}", "19": "{{1.`19`}}", "20": "{{1.`20`}}", "21": "{{1.`21`}}", "22": "{{1.`22`}}", "23": "{{1.`23`}}", "24": "{{1.`24`}}", "25": "{{1.`25`}}", "26": "{{1.`26`}}", "27": "{{1.`27`}}", "28": "{{1.`28`}}", "29": "{{1.`29`}}", "30": "{{1.`30`}}", "31": "{{1.`31`}}", "32": "{{1.`32`}}", "33": "{{1.`33`}}", "34": "{{1.`34`}}", "35": "{{1.`35`}}", "36": "{{1.`36`}}", "37": "{{1.`37`}}", "38": "{{1.`38`}}", "39": "{{1.`39`}}", "40": "{{1.`40`}}", "41": "{{1.`41`}}", "42": "{{1.`42`}}", "43": "{{1.`43`}}", "44": "{{1.`44`}}", "45": "{{1.`45`}}", "46": "{{1.`46`}}", "47": "{{1.`47`}}", "48": "{{1.`48`}}", "49": "{{1.`49`}}", "50": "{{1.`50`}}", "51": "{{1.`51`}}", "52": "{{1.`52`}}", "53": "{{1.`53`}}", "54": "{{1.`54`}}", "55": "{{1.`55`}}", "56": "{{1.`56`}}", "57": "{{1.`57`}}", "58": "{{1.`58`}}", "59": "{{1.`59`}}", "60": "{{1.`60`}}", "61": "{{1.`61`}}", "62": "{{1.`62`}}", "63": "{{1.`63`}}", "64": "{{1.`64`}}", "65": "{{1.`65`}}", "66": "{{1.`66`}}", "67": "{{1.`67`}}", "68": "{{1.`68`}}", "69": "{{1.`69`}}", "70": "{{1.`70`}}", "71": "{{1.`71`}}", "72": "{{1.`72`}}", "73": "{{1.`73`}}", "74": "{{1.`74`}}", "75": "{{1.`75`}}", "76": "{{1.`76`}}", "77": "{{1.`77`}}", "78": "{{1.`78`}}", "79": "{{1.`79`}}", "80": "{{1.`80`}}", "81": "{{1.`81`}}", "82": "{{1.`82`}}", "83": "{{1.`83`}}", "84": "{{1.`84`}}", "85": "{{1.`85`}}", "86": "{{1.`86`}}", "87": "{{1.`87`}}", "88": "{{1.`88`}}", "89": "{{1.`89`}}", "90": "{{1.`90`}}", "91": "{{1.`91`}}", "92": "{{1.`92`}}", "93": "{{1.`93`}}", "94": "{{1.`94`}}", "95": "{{1.`95`}}", "96": "{{1.`96`}}", "97": "{{1.`97`}}", "98": "{{1.`98`}}", "99": "{{1.`99`}}", "100": "{{1.`100`}}", "101": "{{1.`101`}}", "102": "{{1.`102`}}", "103": "{{1.`103`}}", "__SHEET__": "{{1.`__SHEET__`}}", "__IMTINDEX__": "{{1.`__IMTINDEX__`}}", "__IMTLENGTH__": "{{1.`__IMTLENGTH__`}}", "__ROW_NUMBER__": "{{1.`__ROW_NUMBER__`}}", "__SPREADSHEET_ID__": "{{1.`__SPREADSHEET_ID__`}}"}, "metadata": {"designer": {"x": 300, "y": 0}, "restore": {"extra": {"feeder": {"label": "Google Sheets - Search Rows [1]"}, "target": {"label": "Custom"}}}}}, {"id": 4, "module": "builtin:BasicFeeder", "version": 1, "parameters": {}, "mapper": {"array": "{{3.array}}"}, "metadata": {"designer": {"x": 600, "y": 0}, "restore": {"expect": {"array": {"mode": "edit"}}}, "expect": [{"mode": "edit", "name": "array", "spec": [], "type": "array", "label": "Array"}]}}, {"id": 5, "module": "apify:runActor", "version": 1, "parameters": {"__IMTCONN__": 4236943}, "mapper": {"actorId": "67Q6fmd8iedTVcCwY", "runSync": true, "inputBody": "{    \"maxResultStreams\": 0,    \"maxResults\": 10,    \"maxResultsShorts\": 0,    \"scrapeLastNDays\": 7,    \"startUrls\": [        {            \"url\": \"{{4.`0`}}\",            \"method\": \"GET\"        }    ]}"}, "metadata": {"designer": {"x": 900, "y": 0}, "restore": {"expect": {"memory": {"mode": "chose", "label": "Empty"}, "actorId": {"mode": "chose", "label": "Fast YouTube Channel Scraper (streamers/youtube-channel-scraper)"}, "runSync": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "apify2"}, "label": "New Apify Connection"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:apify2,apify", "label": "Connection", "required": true}], "expect": [{"name": "actorId", "type": "select", "label": "Actor", "required": true}, {"name": "runSync", "type": "boolean", "label": "Run synchronously", "required": true}, {"name": "inputBody", "type": "text", "label": "Input JSON"}, {"name": "build", "type": "text", "label": "Build"}, {"name": "timeout", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Timeout"}, {"name": "memory", "type": "select", "label": "Memory", "validate": {"enum": [128, 256, 512, 1024, 2048, 4096, 8192, 16384, 32768]}}, {"name": "actorId", "type": "select", "label": "Actor", "required": true}, {"name": "runSync", "type": "boolean", "label": "Run synchronously", "required": true}, {"name": "inputBody", "type": "text", "label": "Input JSON"}, {"name": "build", "type": "text", "label": "Build"}, {"name": "timeout", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Timeout"}, {"name": "memory", "type": "select", "label": "Memory", "validate": {"enum": [128, 256, 512, 1024, 2048, 4096, 8192, 16384, 32768]}}]}}, {"id": 8, "module": "apify:fetchDatasetItems", "version": 1, "parameters": {"__IMTCONN__": 4236943}, "mapper": {"type": "clean", "limit": "100", "format": "json", "datasetId": "{{5.defaultDatasetId}}"}, "metadata": {"designer": {"x": 1200, "y": 0}, "restore": {"expect": {"type": {"mode": "chose", "label": "Clean"}, "format": {"mode": "chose", "label": "JSON"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "apify2"}, "label": "New Apify Connection (<EMAIL>)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:apify2,apify", "label": "Connection", "required": true}], "expect": [{"name": "datasetId", "type": "text", "label": "Dataset ID", "required": true}, {"name": "type", "type": "select", "label": "Data transformation", "required": true, "validate": {"enum": ["clean", "simplified", "none"]}}, {"name": "format", "type": "select", "label": "Format", "required": true, "validate": {"enum": ["json", "csv", "html", "xml", "rss", "xlsx"]}}, {"name": "limit", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Limit", "validate": {"max": 100000, "min": 1}}, {"name": "offset", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Offset", "validate": {"min": 0}}, {"name": "datasetId", "type": "text", "label": "Dataset ID", "required": true}, {"name": "type", "type": "select", "label": "Data transformation", "required": true, "validate": {"enum": ["clean", "simplified", "none"]}}, {"name": "format", "type": "select", "label": "Format", "required": true, "validate": {"enum": ["json", "csv", "html", "xml", "rss", "xlsx"]}}, {"name": "limit", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Limit", "validate": {"max": 100000, "min": 1}}, {"name": "offset", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Offset", "validate": {"min": 0}}]}}, {"id": 9, "module": "builtin:BasicAggregator", "version": 1, "parameters": {"feeder": 4}, "mapper": {"date": "{{8.date}}", "title": "{{8.title}}", "duration": "{{8.duration}}", "viewCount": "{{8.viewCount}}", "channelName": "{{8.channelName}}", "channelTotalViews": "{{8.channelTotalViews}}", "channelTotalVideos": "{{8.channelTotalVideos}}", "numberOfSubscribers": "{{8.numberOfSubscribers}}"}, "metadata": {"designer": {"x": 1500, "y": 0, "name": "Aggregate competitor videos"}, "restore": {"extra": {"feeder": {"label": "Iterator [4]"}, "target": {"label": "Custom"}}}}}, {"id": 16, "module": "json:TransformToJSON", "version": 1, "parameters": {"space": ""}, "mapper": {"object": "{{9.array}}"}, "metadata": {"designer": {"x": 1800, "y": 0}, "restore": {"parameters": {"space": {"label": "Empty"}}}, "parameters": [{"name": "space", "type": "select", "label": "Indentation", "validate": {"enum": ["tab", "2", "4"]}}], "expect": [{"name": "object", "type": "any", "label": "Object"}]}}, {"id": 13, "module": "google-sheets:filterRows", "version": 2, "parameters": {"__IMTCONN__": 4122218}, "mapper": {"from": "drive", "sheetId": "YouTubeData", "sortOrder": "asc", "spreadsheetId": "1t-9ot2CoULO7KFHhQkzPAfl0GZ2yDU2XXC4gLb-Bwas", "tableFirstRow": "A1:CZ1", "includesHeaders": true, "valueRenderOption": "FORMATTED_VALUE", "dateTimeRenderOption": "FORMATTED_STRING"}, "metadata": {"designer": {"x": 2100, "y": 0}, "restore": {"expect": {"from": {"label": "Select from My Drive"}, "orderBy": {"mode": "chose"}, "sheetId": {"mode": "chose", "label": "YouTubeData"}, "sortOrder": {"mode": "chose", "label": "Ascending"}, "spreadsheetId": {"mode": "chose", "label": "YouTubeData"}, "tableFirstRow": {"label": "A-CZ"}, "includesHeaders": {"mode": "chose", "label": "Yes"}, "valueRenderOption": {"mode": "chose", "label": "Formatted value"}, "dateTimeRenderOption": {"mode": "chose", "label": "Formatted string"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "google"}, "label": "My Google connection (<EMAIL>)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:google", "label": "Connection", "required": true}], "expect": [{"name": "from", "type": "select", "label": "Search Method", "required": true, "validate": {"enum": ["drive", "share"]}}, {"name": "valueRenderOption", "type": "select", "label": "Value render option", "validate": {"enum": ["FORMATTED_VALUE", "UNFORMATTED_VALUE", "FORMULA"]}}, {"name": "dateTimeRenderOption", "type": "select", "label": "Date and time render option", "validate": {"enum": ["SERIAL_NUMBER", "FORMATTED_STRING"]}}, {"name": "limit", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Limit"}, {"name": "spreadsheetId", "type": "select", "label": "Spreadsheet ID", "required": true}, {"name": "sheetId", "type": "select", "label": "Sheet Name", "required": true}, {"name": "includesHeaders", "type": "select", "label": "Table contains headers", "required": true, "validate": {"enum": [true, false]}}, {"name": "tableFirstRow", "type": "select", "label": "Column range", "required": true, "validate": {"enum": ["A1:Z1", "A1:BZ1", "A1:CZ1", "A1:DZ1", "A1:MZ1", "A1:ZZ1", "A1:AZZ1", "A1:BZZ1", "A1:CZZ1", "A1:DZZ1", "A1:MZZ1", "A1:ZZZ1"]}}, {"name": "filter", "type": "filter", "label": "Filter", "options": "rpc://google-sheets/2/rpcGetFilterKeys?includesHeaders=true"}, {"name": "orderBy", "type": "select", "label": "Order by"}, {"name": "sortOrder", "type": "select", "label": "Sort order", "validate": {"enum": ["asc", "desc"]}}], "interface": [{"name": "__IMTLENGTH__", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total number of bundles"}, {"name": "__IMTINDEX__", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Bundle order position"}, {"name": "__ROW_NUMBER__", "type": "number", "label": "Row number"}, {"name": "__SPREADSHEET_ID__", "type": "text", "label": "Spreadsheet ID"}, {"name": "__SHEET__", "type": "text", "label": "Sheet"}, {"name": "0", "type": "text", "label": "Video ID (A)"}, {"name": "1", "type": "text", "label": "Title (B)"}, {"name": "2", "type": "text", "label": "Published At (C)"}, {"name": "3", "type": "text", "label": "Views (D)"}, {"name": "4", "type": "text", "label": "Last Updated (E)"}, {"name": "5", "type": "text", "label": "(F)"}, {"name": "6", "type": "text", "label": "(G)"}, {"name": "7", "type": "text", "label": "(H)"}, {"name": "8", "type": "text", "label": "(I)"}, {"name": "9", "type": "text", "label": "(J)"}, {"name": "10", "type": "text", "label": "(K)"}, {"name": "11", "type": "text", "label": "(L)"}, {"name": "12", "type": "text", "label": "(M)"}, {"name": "13", "type": "text", "label": "(N)"}, {"name": "14", "type": "text", "label": "(O)"}, {"name": "15", "type": "text", "label": "(P)"}, {"name": "16", "type": "text", "label": "(Q)"}, {"name": "17", "type": "text", "label": "(R)"}, {"name": "18", "type": "text", "label": "(S)"}, {"name": "19", "type": "text", "label": "(T)"}, {"name": "20", "type": "text", "label": "(U)"}, {"name": "21", "type": "text", "label": "(V)"}, {"name": "22", "type": "text", "label": "(W)"}, {"name": "23", "type": "text", "label": "(X)"}, {"name": "24", "type": "text", "label": "(Y)"}, {"name": "25", "type": "text", "label": "(Z)"}, {"name": "26", "type": "text", "label": "(AA)"}, {"name": "27", "type": "text", "label": "(AB)"}, {"name": "28", "type": "text", "label": "(AC)"}, {"name": "29", "type": "text", "label": "(AD)"}, {"name": "30", "type": "text", "label": "(AE)"}, {"name": "31", "type": "text", "label": "(AF)"}, {"name": "32", "type": "text", "label": "(AG)"}, {"name": "33", "type": "text", "label": "(AH)"}, {"name": "34", "type": "text", "label": "(AI)"}, {"name": "35", "type": "text", "label": "(AJ)"}, {"name": "36", "type": "text", "label": "(AK)"}, {"name": "37", "type": "text", "label": "(AL)"}, {"name": "38", "type": "text", "label": "(AM)"}, {"name": "39", "type": "text", "label": "(AN)"}, {"name": "40", "type": "text", "label": "(AO)"}, {"name": "41", "type": "text", "label": "(AP)"}, {"name": "42", "type": "text", "label": "(AQ)"}, {"name": "43", "type": "text", "label": "(AR)"}, {"name": "44", "type": "text", "label": "(AS)"}, {"name": "45", "type": "text", "label": "(AT)"}, {"name": "46", "type": "text", "label": "(AU)"}, {"name": "47", "type": "text", "label": "(AV)"}, {"name": "48", "type": "text", "label": "(AW)"}, {"name": "49", "type": "text", "label": "(AX)"}, {"name": "50", "type": "text", "label": "(AY)"}, {"name": "51", "type": "text", "label": "(AZ)"}, {"name": "52", "type": "text", "label": "(BA)"}, {"name": "53", "type": "text", "label": "(BB)"}, {"name": "54", "type": "text", "label": "(BC)"}, {"name": "55", "type": "text", "label": "(BD)"}, {"name": "56", "type": "text", "label": "(BE)"}, {"name": "57", "type": "text", "label": "(BF)"}, {"name": "58", "type": "text", "label": "(BG)"}, {"name": "59", "type": "text", "label": "(BH)"}, {"name": "60", "type": "text", "label": "(BI)"}, {"name": "61", "type": "text", "label": "(BJ)"}, {"name": "62", "type": "text", "label": "(BK)"}, {"name": "63", "type": "text", "label": "(BL)"}, {"name": "64", "type": "text", "label": "(BM)"}, {"name": "65", "type": "text", "label": "(BN)"}, {"name": "66", "type": "text", "label": "(BO)"}, {"name": "67", "type": "text", "label": "(BP)"}, {"name": "68", "type": "text", "label": "(BQ)"}, {"name": "69", "type": "text", "label": "(BR)"}, {"name": "70", "type": "text", "label": "(BS)"}, {"name": "71", "type": "text", "label": "(BT)"}, {"name": "72", "type": "text", "label": "(BU)"}, {"name": "73", "type": "text", "label": "(BV)"}, {"name": "74", "type": "text", "label": "(BW)"}, {"name": "75", "type": "text", "label": "(BX)"}, {"name": "76", "type": "text", "label": "(BY)"}, {"name": "77", "type": "text", "label": "(BZ)"}, {"name": "78", "type": "text", "label": "(CA)"}, {"name": "79", "type": "text", "label": "(CB)"}, {"name": "80", "type": "text", "label": "(CC)"}, {"name": "81", "type": "text", "label": "(CD)"}, {"name": "82", "type": "text", "label": "(CE)"}, {"name": "83", "type": "text", "label": "(CF)"}, {"name": "84", "type": "text", "label": "(CG)"}, {"name": "85", "type": "text", "label": "(CH)"}, {"name": "86", "type": "text", "label": "(CI)"}, {"name": "87", "type": "text", "label": "(CJ)"}, {"name": "88", "type": "text", "label": "(CK)"}, {"name": "89", "type": "text", "label": "(CL)"}, {"name": "90", "type": "text", "label": "(CM)"}, {"name": "91", "type": "text", "label": "(CN)"}, {"name": "92", "type": "text", "label": "(CO)"}, {"name": "93", "type": "text", "label": "(CP)"}, {"name": "94", "type": "text", "label": "(CQ)"}, {"name": "95", "type": "text", "label": "(CR)"}, {"name": "96", "type": "text", "label": "(CS)"}, {"name": "97", "type": "text", "label": "(CT)"}, {"name": "98", "type": "text", "label": "(CU)"}, {"name": "99", "type": "text", "label": "(CV)"}, {"name": "100", "type": "text", "label": "(CW)"}, {"name": "101", "type": "text", "label": "(CX)"}, {"name": "102", "type": "text", "label": "(CY)"}, {"name": "103", "type": "text", "label": "(CZ)"}]}}, {"id": 14, "module": "builtin:BasicAggregator", "version": 1, "parameters": {"feeder": 13}, "mapper": {"1": "{{13.`1`}}", "2": "{{13.`2`}}", "3": "{{13.`3`}}"}, "metadata": {"designer": {"x": 2400, "y": 0, "name": "Aggregate my videos"}, "restore": {"extra": {"feeder": {"label": "Google Sheets - Search Rows [13]"}, "target": {"label": "Custom"}}}}}, {"id": 17, "module": "json:TransformToJSON", "version": 1, "parameters": {"space": ""}, "mapper": {"object": "{{14.array}}"}, "metadata": {"designer": {"x": 2700, "y": 0}, "restore": {"parameters": {"space": {"label": "Empty"}}}, "parameters": [{"name": "space", "type": "select", "label": "Indentation", "validate": {"enum": ["tab", "2", "4"]}}], "expect": [{"name": "object", "type": "any", "label": "Object"}]}}, {"id": 15, "module": "openai-gpt-3:<PERSON>reateCompletion", "version": 1, "parameters": {"__IMTCONN__": 4220625}, "mapper": {"model": "gpt-4o-mini", "top_p": "1", "select": "chat", "messages": [{"role": "user", "content": "You are a YouTube content strategist.\n\nHere is a list of high-performing videos from competitor channels with their view counts. Also included are videos from my channel and how many views they received.\n\nBased on this, suggest 5 potential video topics that I should create next. Each topic should be inspired by what's trending in the competitor list, but avoid exact repetition. You may combine ideas or remix angles.\n\nFor each topic, include:\n- Main keywords\n- Suggested title\n- Short explanation of why it has potential\n- Estimated opportunity score (1–10), where 10 = very high potential for my channel\n- Whether this overlaps with any of my past videos\n\nCompetitor videos:\n{{16.json}}\n\nMy videos:\n{{17.json}}", "imageDetail": "auto"}, {"role": "system", "content": "Output as html I can directly transform into a PDF. Provide a heading at the top\n\nVideo opportunities for the week of [date]. Today's date is {{now}}.\n\nBelow, format a box with an idea icon, and the requisite text formatted inside the box. Each video has its own box."}], "max_tokens": "2048", "temperature": "1", "n_completions": "1", "response_format": "text"}, "metadata": {"designer": {"x": 3000, "y": 0}, "restore": {"expect": {"stop": {"mode": "chose"}, "model": {"mode": "chose", "label": "gpt-4o-mini (system)"}, "select": {"label": "Create a Chat Completion (GPT and o1 models)"}, "messages": {"mode": "chose", "items": [{"role": {"mode": "chose", "label": "User"}, "imageDetail": {"mode": "chose", "label": "Auto"}, "imageInputType": {"mode": "chose", "label": "Empty"}}, {"role": {"mode": "chose", "label": "Developer / System"}}]}, "logit_bias": {"mode": "chose"}, "response_format": {"mode": "chose", "label": "Text"}, "additionalParameters": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "openai-gpt-3"}, "label": "My OpenAI connection"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:openai-gpt-3", "label": "Connection", "required": true}], "expect": [{"name": "select", "type": "select", "label": "Select Method", "required": true, "validate": {"enum": ["chat", "prompt"]}}, {"name": "temperature", "type": "number", "label": "Temperature", "validate": {"max": 2, "min": 0}}, {"name": "top_p", "type": "number", "label": "Top P", "validate": {"max": 1, "min": 0}}, {"name": "n_completions", "type": "number", "label": "Number"}, {"name": "frequency_penalty", "type": "number", "label": "Frequency Penalty", "validate": {"max": 2, "min": -2}}, {"name": "presence_penalty", "type": "number", "label": "Presence Penalty", "validate": {"max": 2, "min": -2}}, {"name": "logit_bias", "spec": {"name": "value", "spec": [{"name": "token", "type": "text", "label": "Token ID", "required": true}, {"name": "probability", "type": "number", "label": "Probability", "required": true, "validate": {"max": 100, "min": -100}}], "type": "collection", "label": "Token Probability"}, "type": "array", "label": "Token Probability"}, {"name": "seed", "type": "integer", "label": "Seed"}, {"name": "stop", "spec": {"name": "value", "type": "text", "label": "Stop Sequence"}, "type": "array", "label": "Stop Sequences", "validate": {"maxItems": 4}}, {"name": "additionalParameters", "spec": {"name": "value", "spec": [{"name": "key", "type": "text", "label": "Parameter Name", "required": true}, {"name": "type", "type": "select", "label": "Input Type", "options": [{"label": "Text", "value": "text", "nested": [{"name": "value", "type": "text", "label": "Parameter Value"}], "default": true}, {"label": "Number", "value": "number", "nested": [{"name": "value", "type": "number", "label": "Parameter Value"}]}, {"label": "Boolean", "value": "boolean", "nested": [{"name": "value", "type": "boolean", "label": "Parameter Value"}]}, {"label": "Date", "value": "date", "nested": [{"name": "value", "type": "date", "label": "Parameter Value"}]}, {"label": "Any", "value": "any", "nested": [{"name": "value", "type": "any", "label": "Parameter Value"}]}]}], "type": "collection", "label": "Input Parameter"}, "type": "array", "label": "Other Input Parameters"}, {"name": "model", "type": "select", "label": "Model", "required": true}, {"name": "max_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Max Completion Tokens"}, {"name": "messages", "spec": {"name": "value", "spec": [{"name": "role", "type": "select", "label": "Role", "options": {"store": [{"label": "User", "value": "user", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}, {"name": "imageInputType", "type": "select", "label": "Image Input Type", "options": [{"label": "URL", "value": "url", "nested": [{"help": "Make sure to use a publicly accessible URL.\nYou can test if your image is publicly accessible by opening the link in an incognito tab.", "name": "imageUrl", "type": "url", "label": "Image URL"}]}, {"label": "Image File", "value": "file", "nested": [{"name": "imageFile", "spec": [{"help": "Accepted extensions: `.jpg`, `.jpeg`, `.png`, `.webp` and `.gif`.", "name": "imageFilename", "type": "filename", "label": "Image Filename", "semantic": "file:name", "extension": ["jpg", "jpeg", "png", "webp", "gif"]}, {"name": "imageData", "type": "buffer", "label": "Image Data", "semantic": "file:data"}], "type": "collection", "label": "Image"}]}], "mappable": false}, {"help": "Recommended value: `Auto`", "name": "imageDetail", "type": "select", "label": "Image Detail", "options": [{"label": "Auto", "value": "auto", "default": true}, {"label": "High", "value": "high"}, {"label": "Low", "value": "low"}]}]}, {"label": "Assistant", "value": "assistant", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}]}, {"label": "Developer / System", "value": "system", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}]}]}, "required": true}], "type": "collection", "label": "Message"}, "type": "array", "label": "Messages", "required": true}, {"name": "response_format", "type": "select", "label": "Response Format", "validate": {"enum": ["text", "json_object"]}}, {"name": "prediction", "type": "text", "label": "Predicted Outputs"}], "interface": [{"name": "result", "type": "any", "label": "Result"}, {"name": "id", "type": "text", "label": "ID"}, {"name": "object", "type": "text", "label": "Object"}, {"name": "created", "type": "date", "label": "Created"}, {"name": "model", "type": "text", "label": "Model"}, {"name": "choices", "spec": [{"name": "text", "type": "text", "label": "Text"}, {"name": "index", "type": "number", "label": "Index"}, {"name": "logprobs", "type": "text", "label": "Log Probs"}, {"name": "finish_reason", "type": "text", "label": "Finish Reason"}, {"name": "message", "spec": [{"name": "role", "type": "text", "label": "Role"}, {"name": "content", "type": "text", "label": "Content"}, {"name": "refusal", "type": "text", "label": "Refusal"}, {"name": "annotations", "spec": [{"name": "type", "type": "text", "label": "Type"}, {"name": "url_citation", "spec": [{"name": "end_index", "type": "number", "label": "End Index"}, {"name": "start_index", "type": "number", "label": "Start Index"}, {"name": "title", "type": "text", "label": "Title"}, {"name": "url", "type": "text", "label": "URL"}], "type": "collection", "label": "URL Citation"}], "type": "array", "label": "Annotations"}], "type": "collection", "label": "Message"}], "type": "array", "label": "Choices"}, {"name": "usage", "spec": [{"name": "prompt_tokens", "type": "number", "label": "Prompt Tokens"}, {"name": "completion_tokens", "type": "text", "label": "Completion Tokens"}, {"name": "total_tokens", "type": "number", "label": "Total Tokens"}, {"name": "prompt_tokens_details", "spec": [{"name": "cached_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "text_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Text Tokens"}, {"name": "image_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Image Tokens"}, {"name": "audio_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Audio Tokens"}], "type": "collection", "label": "Prompt Tokens Details"}, {"name": "completion_tokens_details", "spec": [{"name": "reasoning_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Reasoning Tokens"}, {"name": "text_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Text Tokens"}, {"name": "audio_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Audio Tokens"}, {"name": "accepted_prediction_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Accepted Prediction Tokens"}, {"name": "rejected_prediction_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rejected Prediction Tokens"}], "type": "collection", "label": "Completion Tokens Details"}], "type": "collection", "label": "Usage"}, {"name": "service_tier", "type": "text", "label": "Service Tier"}, {"name": "system_fingerprint", "type": "text", "label": "System Fingerprint"}]}}, {"id": 18, "module": "google-email:ActionSendEmail", "version": 2, "parameters": {"account": 4109159}, "mapper": {"cc": [], "to": ["<EMAIL>"], "bcc": [], "from": "", "html": "{{15.result}}", "subject": "Video Opportunities for this week", "attachments": []}, "metadata": {"designer": {"x": 3300, "y": 0}, "restore": {"expect": {"cc": {"mode": "chose"}, "to": {"mode": "chose", "items": [null]}, "bcc": {"mode": "chose"}, "attachments": {"mode": "chose"}}, "parameters": {"account": {"data": {"scoped": "true", "connection": "google-restricted"}, "label": "My Google Restricted connection (<EMAIL>)"}}}, "parameters": [{"name": "account", "type": "account:google-restricted", "label": "Connection", "required": true}], "expect": [{"name": "from", "type": "text", "label": "From"}, {"name": "to", "spec": {"name": "value", "type": "email", "label": "Email address", "required": true}, "type": "array", "label": "To", "required": true}, {"name": "subject", "type": "text", "label": "Subject"}, {"name": "html", "type": "text", "label": "Content"}, {"name": "attachments", "spec": [{"name": "fileName", "type": "filename", "label": "File name", "required": true, "semantic": "file:name"}, {"name": "data", "type": "buffer", "label": "Data", "required": true, "semantic": "file:data"}, {"name": "cid", "type": "text", "label": "Content-ID"}], "type": "array", "label": "Attachments"}, {"name": "cc", "spec": {"name": "value", "type": "email", "label": "Email address"}, "type": "array", "label": "Copy recipient"}, {"name": "bcc", "spec": {"name": "value", "type": "email", "label": "Email address"}, "type": "array", "label": "Blind copy recipient"}]}}], "metadata": {"instant": false, "version": 1, "scenario": {"roundtrips": 1, "maxErrors": 3, "autoCommit": true, "autoCommitTriggerLast": true, "sequential": false, "slots": null, "confidential": false, "dataloss": false, "dlq": false, "freshVariables": false}, "designer": {"orphans": []}, "zone": "us1.make.com", "notes": []}}
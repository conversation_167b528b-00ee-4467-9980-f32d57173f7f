# 📥 N8N Workflow Import Guide

## 🎯 Quick Import Instructions

### Method 1: Direct File Import (Recommended)

1. **Access N8N Interface**
   ```
   http://localhost:5678
   ```

2. **Login with Credentials**
   - Username: `admin`
   - Password: Check your `.env` file for `N8N_PASSWORD`

3. **Import the Workflow**
   - Click on **"Workflows"** in the left sidebar
   - Click **"Import from File"** button
   - Select the file: **`local-ai-youtube-automation-workflow.json`**
   - Click **"Import"**

4. **Activate the Workflow**
   - Click the **"Active"** toggle switch
   - The workflow is now ready to use!

### Method 2: Import via URL

If you have the workflow hosted online, you can import via URL:

1. **Get the Raw File URL**
   ```
   https://raw.githubusercontent.com/your-repo/local-ai-youtube-automation/main/local-ai-youtube-automation-workflow.json
   ```

2. **Import in N8N**
   - Go to **Workflows** → **Import from URL**
   - Paste the URL above
   - Click **"Import"**

### Method 3: Copy-Paste JSON

1. **Copy the Workflow JSON**
   - Open `local-ai-youtube-automation-workflow.json`
   - Copy all content (Ctrl+A, Ctrl+C)

2. **Import in N8N**
   - Go to **Workflows** → **Import from File**
   - Click **"Paste JSON"**
   - Paste the content and click **"Import"**

## 🔧 Post-Import Configuration

### 1. Configure Environment Variables

Ensure these are set in your `.env` file:
```bash
# Local AI Services
OLLAMA_URL=http://localhost:11434
STABLE_DIFFUSION_URL=http://localhost:7860
COMFYUI_URL=http://localhost:8188

# Preferred Models
PREFERRED_LLM=qwen2.5:32b
PREFERRED_SD_MODEL=sd_xl_base_1.0.safetensors
```

### 2. Test the Workflow

1. **Manual Test**
   - Click **"Test workflow"** button
   - Add test data:
     ```json
     {
       "forceRun": true,
       "testMode": true
     }
     ```
   - Click **"Execute workflow"**

2. **Check Execution**
   - Monitor the execution in real-time
   - Each node should turn green when completed
   - Check logs for any errors

### 3. Configure Triggers

#### Schedule Trigger
- **Default**: Every 2 hours
- **Customize**: Edit the "🕐 Intelligent Schedule Trigger" node
- **Options**: Hourly, daily, weekly, custom cron

#### Webhook Trigger
- **URL**: `http://localhost:5678/webhook/webhook-trigger`
- **Method**: POST
- **Test**: 
  ```bash
  curl -X POST http://localhost:5678/webhook/webhook-trigger \
    -H "Content-Type: application/json" \
    -d '{"forceRun": true}'
  ```

## 🎛️ Workflow Nodes Overview

### 🧠 **Market Strategy Thinker**
- Analyzes optimal posting times
- Determines content strategy
- Checks market conditions

### 🤖 **Local AI Content Generator**
- Uses local Ollama/LocalAI models
- Generates titles, scripts, descriptions
- Completely offline operation

### 🎨 **Local Image Generation**
- Uses local Stable Diffusion
- Creates professional thumbnails
- Fallback to placeholder if needed

### 🎥 **Video Production System**
- Generates FFmpeg commands
- Validates video specifications
- Prepares for rendering

### 🔍 **Quality Assurance**
- Validates content quality
- Checks SEO optimization
- Provides improvement recommendations

### 📊 **Analytics Tracker**
- Predicts performance metrics
- Tracks generation statistics
- Provides insights

## 🚨 Troubleshooting

### Common Import Issues

#### **"Invalid JSON" Error**
```bash
# Validate JSON syntax
cat local-ai-youtube-automation-workflow.json | jq .
```

#### **"Missing Dependencies" Warning**
- Ignore warnings about missing credentials
- Configure credentials after import

#### **"Node Type Not Found" Error**
- Ensure N8N is updated to latest version
- Check if custom nodes are needed

### Workflow Execution Issues

#### **Local AI Services Not Responding**
```bash
# Check service status
docker-compose -f docker-compose.local-ai.yml ps

# Restart services if needed
docker-compose -f docker-compose.local-ai.yml restart
```

#### **Model Not Found Errors**
```bash
# Install missing models
./install-models.sh --profile auto

# Check installed models
curl http://localhost:11434/api/tags
```

#### **Image Generation Failures**
```bash
# Check Stable Diffusion status
curl http://localhost:7860/sdapi/v1/sd-models

# Restart if needed
docker-compose -f docker-compose.local-ai.yml restart automatic1111
```

## 🎯 Testing Your Setup

### 1. **Quick Test**
```bash
# Run the test suite
node test-local-ai-system.js
```

### 2. **Manual Workflow Test**
1. Open N8N workflow
2. Click "Execute workflow"
3. Monitor each node execution
4. Check final output

### 3. **End-to-End Test**
```bash
# Trigger via webhook
curl -X POST http://localhost:5678/webhook/webhook-trigger \
  -H "Content-Type: application/json" \
  -d '{
    "forceRun": true,
    "testMode": true,
    "duration": 30,
    "topic": "AI automation test"
  }'
```

## 🎉 Success Indicators

### ✅ **Workflow Imported Successfully**
- All nodes visible in N8N interface
- No red error indicators
- Workflow can be activated

### ✅ **Local AI Working**
- Content generation completes
- Images are generated (or placeholders created)
- No timeout errors

### ✅ **Quality Assurance Passing**
- Content meets quality thresholds
- Technical specifications valid
- Ready for upload status

## 📞 Getting Help

### **Check Logs**
```bash
# N8N logs
docker-compose -f docker-compose.local-ai.yml logs n8n

# Ollama logs
docker-compose -f docker-compose.local-ai.yml logs ollama

# Stable Diffusion logs
docker-compose -f docker-compose.local-ai.yml logs automatic1111
```

### **System Status**
```bash
# Check all services
curl http://localhost:9000/api/status

# Check individual services
curl http://localhost:11434/api/tags        # Ollama
curl http://localhost:7860/sdapi/v1/sd-models  # Stable Diffusion
curl http://localhost:5678/healthz         # N8N
```

### **Community Support**
- Check documentation: `LOCAL_AI_SETUP_GUIDE.md`
- Run diagnostics: `node test-local-ai-system.js`
- Review logs for specific error messages

---

## 🚀 You're Ready to Create!

Once imported and configured, your Local AI YouTube Automation System will:

- ✅ **Generate content completely offline**
- ✅ **Create professional thumbnails locally**
- ✅ **Produce high-quality videos**
- ✅ **Work without external API dependencies**
- ✅ **Cost nothing to operate**

**Welcome to the future of private, local AI content creation!** 🎉

{"name": "Node - Scrape Url", "nodes": [{"parameters": {"workflowInputs": {"values": [{"name": "url"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [0, 0], "id": "54919f44-9c0e-4bb3-b9ae-55618cc14cad", "name": "workflow_trigger"}, {"parameters": {"method": "POST", "url": "https://api.firecrawl.dev/v1/scrape", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"url\": \"{{ $json.url }}\",\n  \"formats\": [\"json\", \"markdown\", \"rawHtml\", \"links\"],\n  \"excludeTags\": [\"iframe\", \"nav\", \"header\", \"footer\"],\n  \"onlyMainContent\": true,\n  \"jsonOptions\": {\n    \"prompt\": \"Identify the main content of the text (i.e., the article or newsletter body). Provide the exact text for that main content verbatim, without summarizing or rewriting any part of it. Exclude all non-essential elements such as banners, headers, footers, calls to action, ads, or purely navigational text. Format this output as markdown using appropriate '#' characters as heading levels. Exclude any promotional or sponsored content on your output. Additionally, you must identify and extract the image urls within this main content. These images must be inside the main content of the page so you must exclude small logo images, icons, avatars and other images which aren't a core part of the main content. The images you extract should at least have a width of 600 pixels (px) so it can be included on our content.\",\n    \"schema\": {\n    \"type\": \"object\",\n      \"properties\": {\n        \"content\": {\n          \"type\": \"string\",\n          \"description\": \"The exact verbatim main text content of the web page in markdown format.\"\n        },\n        \"main_content_image_urls\": {\n          \"type\": \"array\",\n          \"items\": {\n            \"type\": \"string\",\n            \"description\": \"An image url that appears within the main content of the web page. This image must be inside the main content of the page so you must exclude small logo images, icons, avatars and other images which aren't a core part of the main content. The image should be at least 600px in width.\"\n          },\n          \"description\": \"An array of the exact image urls that appear within the main content of the web page. Extra images such as icons and images not relevant to the main content MUST be excluded.\"\n        }\n      },\n      \"required\": [\"content\", \"main_content_image_urls\"]\n    }\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [380, 0], "id": "6846ab99-46e8-44f7-8eeb-afe6d82ff768", "name": "scrape_url", "retryOnFail": true, "maxTries": 3, "waitBetweenTries": 5000, "credentials": {"httpHeaderAuth": {"id": "vOYFxLc6HUcni7SU", "name": "Firecrawl"}}, "onError": "continueRegularOutput"}], "pinData": {"workflow_trigger": [{"json": {"url": "https://techcrunch.com/2025/04/22/ex-meta-engineer-raises-14m-for-lace-an-ai-powered-revenue-generation-software-startup/"}}]}, "connections": {"workflow_trigger": {"main": [[{"node": "scrape_url", "type": "main", "index": 0}]]}, "scrape_url": {"main": [[]]}}, "active": false, "settings": {"executionOrder": "v1", "callerPolicy": "any"}, "versionId": "fde7f25b-35dc-455b-9949-35c581be414d", "meta": {"templateCredsSetupCompleted": true, "instanceId": "06e5009344f682419c20ccd4ecdcb5223bbb91761882af93ac6d468dbc2cbf8d"}, "id": "qVEM2rCD1jlJPeRs", "tags": []}
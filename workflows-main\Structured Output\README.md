# Como Usar Structured Outputs da OpenAI no WhatsApp

Este repositório contém os arquivos de um fluxo do **n8n** desenvolvido no vídeo [Como Usar Structured Outputs da OpenAI no WhatsApp: Enviando Respostas Humanizadas com ChatGPT!](LINK_DO_VIDEO), que ensina a configurar respostas humanizadas no WhatsApp utilizando ChatGPT da OpenAI e integrando com diversas ferramentas.

## Descrição

No vídeo, o autor demonstra como utilizar as **Structured Outputs (Saídas Estruturadas)** da OpenAI para tornar a comunicação com a IA no WhatsApp mais natural. A técnica de envio de respostas fragmentadas, uma sentença por vez, faz com que as interações do ChatGPT se tornem mais fluidas, assemelhando-se a uma conversa humana real.

O fluxo foi construído usando as seguintes ferramentas:
- **n8n**: Plataforma de automação para criar o fluxo.
- **OpenAI (ChatGPT)**: IA utilizada para gerar as respostas.
- **JSON Formatter**: Para formatar as saídas de forma adequada.
- **JSON Transform**: Para manipulação dos dados JSON.
- **EvolutionAPI**: Para a integração do WhatsApp com o ChatGPT.

Este repositório contém os arquivos que implementam o fluxo apresentado no vídeo, permitindo replicar o chatbot com respostas fragmentadas e naturais no WhatsApp.

## Como usar este repositório

1. **Baixar o json**
   
   Baixe o Json do fluxo escolhido e importe no N8N.
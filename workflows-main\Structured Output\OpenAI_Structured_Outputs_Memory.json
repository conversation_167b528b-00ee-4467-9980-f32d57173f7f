{"name": "OpenAI Structured Outputs", "nodes": [{"parameters": {"method": "POST", "url": "https://api.openai.com/v1/chat/completions", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ $json }}", "options": {}}, "id": "7a11413b-66b7-4ff9-b2aa-a5fd574662ee", "name": "OpenAI", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1940, 620], "credentials": {"httpHeaderAuth": {"id": "9Yk60HcVSVgLxdSJ", "name": "OpenAI Header Key"}}}, {"parameters": {"assignments": {"assignments": [{"id": "cf9374ef-7269-4455-b32d-43e34d993f4f", "name": "response_format.type", "value": "json_schema", "type": "string"}, {"id": "28a95f27-27e0-4bc0-abae-60a05dc8bb4d", "name": "response_format.json_schema.name", "value": "instructions_final_answer", "type": "string"}, {"id": "cbf998ef-fd0d-425a-85bd-ada90c198433", "name": "response_format.json_schema.schema", "value": "{\n  \"title\": \"Instructions for the final answer\",\n  \"description\": \"Follow these steps, first analyze the user's message, think of a strategy for the response and finally generate the final response.\",\n  \"type\": \"object\",\n  \"properties\": {\n    \"reflection\": {\n      \"type\": \"string\",\n  \"description\": \"Reflect on the user's message and its context\"\n    },\n    \"strategy\": {\n      \"type\": \"string\",\n  \"description\": \"Think of a strategy to respond to it following the system's instructions.\"\n    },\n    \"final_answer\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\",\n  \"description\": \"sentence part.\"\n      },\n  \"description\": \"final answer partitioned into not very large sentences\"\n    }\n  },\n  \"required\": [\n    \"reflection\",\n    \"strategy\",\n    \"final_answer\"\n  ],\n  \"additionalProperties\": false\n}", "type": "object"}, {"id": "c5d1082a-42df-4cd7-8662-af2bd1f1930d", "name": "response_format.json_schema.strict", "value": true, "type": "boolean"}]}, "options": {}}, "id": "e8b49b28-27c7-4269-b6b2-35ff5e8dbbc4", "name": "responseFormat", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1440, 800]}, {"parameters": {"assignments": {"assignments": [{"id": "4c7b4a94-c342-4143-8950-c845e0fd2d48", "name": "model", "value": "gpt-4o-mini", "type": "string"}]}, "options": {}}, "id": "6ee93650-256f-4bea-a712-e947956edede", "name": "model", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1440, 460]}, {"parameters": {"assignments": {"assignments": [{"id": "a10706be-8775-4c6e-b228-2e8fd48e819d", "name": "messages", "value": "={{ [$json.sytem_message].concat($('Get messages').item.json.messages.map(obj => JSON.parse(obj))) }}", "type": "array"}]}, "options": {}}, "id": "f0ba77b7-5700-4361-ae36-1dcf0c8eeb65", "name": "messages", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1440, 620]}, {"parameters": {"mode": "raw", "jsonOutput": "={{ $json.choices[0].message.content }}", "options": {}}, "id": "2b6e9dda-a7f4-40e8-9a1e-81423f429b81", "name": "Parse", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2160, 620]}, {"parameters": {"operation": "get", "propertyName": "messages", "key": "={{ $('When chat message received').item.json.sessionId }}", "options": {}}, "id": "901458c1-d3ed-4037-9f35-e9134840839e", "name": "Get messages", "type": "n8n-nodes-base.redis", "typeVersion": 1, "position": [980, 620], "credentials": {"redis": {"id": "ccEEcPDqUplE95d3", "name": "Redis account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "********-38da-4076-9328-1f7382416c94", "name": "sytem_message", "value": "={\n  \"role\": \"system\",\n  \"content\": \"You are a helpful assistant. Your native language is Brazilian Portugueses. Never speak about math.\"\n}", "type": "object"}]}, "options": {}}, "id": "64874aa8-f49a-49f7-a5d5-ad9e5c310585", "name": "System message", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1200, 620]}, {"parameters": {"mode": "combineBySql", "numberInputs": 3, "query": "SELECT * FROM input1, input2, input3"}, "id": "bac04bdc-77f5-4b36-a367-c725d52210dd", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "typeVersion": 3, "position": [1700, 620]}, {"parameters": {"assignments": {"assignments": [{"id": "576f201a-a2ae-4eff-a5fb-010aac660114", "name": "final_answer", "value": "={{ $json.final_answer }}", "type": "array"}]}, "options": {}}, "id": "85ff87f2-b750-4f78-8b7b-612a5813de75", "name": "Final answer", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2380, 620]}, {"parameters": {"fieldToSplitOut": "final_answer", "options": {}}, "id": "45bab601-3db0-4117-996f-1ffcf4caa1bc", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [2600, 620]}, {"parameters": {}, "id": "f507855d-5f4a-4dd0-bd89-999094cc18ef", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.manualChatTrigger", "typeVersion": 1.1, "position": [320, 620]}, {"parameters": {"assignments": {"assignments": [{"id": "dd7f0919-35b6-44f4-81b7-a9b68fecc974", "name": "role", "value": "user", "type": "string"}, {"id": "83e12f41-fc7c-4cbd-943c-c0ab4b7b6d20", "name": "content", "value": "={{ $json.chatInput }}", "type": "string"}]}, "options": {}}, "id": "dfcc3763-2aa7-4198-9644-067eb656a8be", "name": "User message", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [540, 620]}, {"parameters": {"assignments": {"assignments": [{"id": "dd7f0919-35b6-44f4-81b7-a9b68fecc974", "name": "role", "value": "assistant", "type": "string"}, {"id": "83e12f41-fc7c-4cbd-943c-c0ab4b7b6d20", "name": "content", "value": "={{ $json.final_answer }}", "type": "string"}]}, "options": {}}, "id": "94042808-4ca5-4ed2-a483-b5ccbc474346", "name": "Assistant message", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2820, 620]}, {"parameters": {"operation": "push", "list": "={{ $('When chat message received').item.json.sessionId }}", "messageData": "={{ JSON.stringify($json) }}", "tail": true}, "id": "ed1a1e09-99e2-4836-b999-12f4054b032c", "name": "Push User message", "type": "n8n-nodes-base.redis", "typeVersion": 1, "position": [760, 620], "credentials": {"redis": {"id": "ccEEcPDqUplE95d3", "name": "Redis account"}}}, {"parameters": {"operation": "push", "list": "={{ $('When chat message received').item.json.sessionId }}", "messageData": "={{ JSON.stringify($json) }}", "tail": true}, "id": "f6bc74fc-3ca6-4134-bd7e-ec78dd974a16", "name": "<PERSON>ush Assistant message", "type": "n8n-nodes-base.redis", "typeVersion": 1, "position": [3040, 620], "credentials": {"redis": {"id": "ccEEcPDqUplE95d3", "name": "Redis account"}}}], "pinData": {}, "connections": {"messages": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "model": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "responseFormat": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 2}]]}, "OpenAI": {"main": [[{"node": "Parse", "type": "main", "index": 0}]]}, "Get messages": {"main": [[{"node": "System message", "type": "main", "index": 0}]]}, "System message": {"main": [[{"node": "messages", "type": "main", "index": 0}, {"node": "model", "type": "main", "index": 0}, {"node": "responseFormat", "type": "main", "index": 0}]]}, "Merge": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}]]}, "Parse": {"main": [[{"node": "Final answer", "type": "main", "index": 0}]]}, "Final answer": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "User message", "type": "main", "index": 0}]]}, "User message": {"main": [[{"node": "Push User message", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Assistant message", "type": "main", "index": 0}]]}, "Push User message": {"main": [[{"node": "Get messages", "type": "main", "index": 0}]]}, "Assistant message": {"main": [[{"node": "<PERSON>ush Assistant message", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "f5924766-0a87-4fe7-bf60-d5de31d08538", "meta": {"templateCredsSetupCompleted": true, "instanceId": "2ad20c637f548d7b1e09cbae0383c6644aca87b4f0fe8deda2de937f77c2be79"}, "id": "lA0b7j5UjINtt8ZI", "tags": []}
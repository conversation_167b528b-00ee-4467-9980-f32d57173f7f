version: '3.8'

# Advanced YouTube Automation - Local AI Stack
# Complete self-hosted AI environment for offline content generation

services:
  # N8N Workflow Engine
  n8n:
    image: n8nio/n8n:latest
    container_name: youtube-automation-n8n
    restart: unless-stopped
    ports:
      - "5678:5678"
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=${N8N_PASSWORD:-secure-password-123}
      - WEBHOOK_URL=http://localhost:5678/
      - N8N_METRICS=true
      - N8N_LOG_LEVEL=info
      # Local AI Service URLs
      - OLLAMA_URL=http://ollama:11434
      - LOCALAI_URL=http://localai:8080
      - STABLE_DIFFUSION_URL=http://automatic1111:7860
      - COMFYUI_URL=http://comfyui:8188
      # Preferred Models (based on your downloads)
      - PREFERRED_LLM=qwen2.5:32b
      - <PERSON><PERSON><PERSON>CK_LLM=mistral:7b-instruct
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./workflows:/home/<USER>/.n8n/workflows
      - ./local-ai-workflows:/home/<USER>/.n8n/local-workflows
    depends_on:
      - postgres
      - ollama
      - localai
      - automatic1111
    networks:
      - ai-network

  # PostgreSQL Database for N8N
  postgres:
    image: postgres:15
    container_name: youtube-automation-db
    restart: unless-stopped
    environment:
      - POSTGRES_USER=n8n
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-n8n-secure-db}
      - POSTGRES_DB=n8n
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - ai-network

  # Ollama - Local LLM Server
  ollama:
    image: ollama/ollama:latest
    container_name: youtube-automation-ollama
    restart: unless-stopped
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
      - ./models:/models
    environment:
      - OLLAMA_KEEP_ALIVE=24h
      - OLLAMA_HOST=0.0.0.0
      - OLLAMA_ORIGINS=*
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    networks:
      - ai-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      interval: 30s
      timeout: 10s
      retries: 3

  # LocalAI - Alternative LLM Server
  localai:
    image: quay.io/go-skynet/local-ai:latest
    container_name: youtube-automation-localai
    restart: unless-stopped
    ports:
      - "8080:8080"
    volumes:
      - ./localai-models:/models
      - ./localai-config:/config
    environment:
      - DEBUG=true
      - MODELS_PATH=/models
      - PRELOAD_MODELS_CONFIG=/config/models.yaml
      - GALLERIES=[{"name":"model-gallery","url":"github:go-skynet/model-gallery/index.yaml"}]
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    networks:
      - ai-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/v1/models"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Automatic1111 Stable Diffusion WebUI
  automatic1111:
    image: universonic/automatic1111-webui:latest
    container_name: youtube-automation-sd
    restart: unless-stopped
    ports:
      - "7860:7860"
    volumes:
      - sd_models:/app/stable-diffusion-webui/models
      - sd_outputs:/app/stable-diffusion-webui/outputs
      - ./sd-config:/app/stable-diffusion-webui/config
    environment:
      - COMMANDLINE_ARGS=--api --listen --enable-insecure-extension-access --xformers --opt-split-attention
      - WEBUI_PORT=7860
      - WEBUI_HOST=0.0.0.0
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    networks:
      - ai-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7860/sdapi/v1/sd-models"]
      interval: 60s
      timeout: 30s
      retries: 3

  # ComfyUI - Alternative Stable Diffusion Interface
  comfyui:
    image: yanwk/comfyui-boot:latest
    container_name: youtube-automation-comfyui
    restart: unless-stopped
    ports:
      - "8188:8188"
    volumes:
      - comfyui_data:/root/ComfyUI
      - ./comfyui-models:/root/ComfyUI/models
      - ./comfyui-custom-nodes:/root/ComfyUI/custom_nodes
    environment:
      - CLI_ARGS=--listen 0.0.0.0 --port 8188
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    networks:
      - ai-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8188/system_stats"]
      interval: 60s
      timeout: 30s
      retries: 3

  # Redis for Caching (Optional but recommended)
  redis:
    image: redis:7-alpine
    container_name: youtube-automation-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    networks:
      - ai-network

  # Model Manager Service
  model-manager:
    build:
      context: .
      dockerfile: Dockerfile.model-manager
    container_name: youtube-automation-models
    restart: unless-stopped
    ports:
      - "9000:9000"
    volumes:
      - ollama_data:/ollama-models
      - sd_models:/sd-models
      - ./model-configs:/configs
    environment:
      - OLLAMA_URL=http://ollama:11434
      - SD_URL=http://automatic1111:7860
      - COMFYUI_URL=http://comfyui:8188
    depends_on:
      - ollama
      - automatic1111
      - comfyui
    networks:
      - ai-network

  # System Monitor
  monitor:
    image: prom/prometheus:latest
    container_name: youtube-automation-monitor
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - ai-network

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: youtube-automation-dashboard
    restart: unless-stopped
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
      - GF_USERS_ALLOW_SIGN_UP=false
    depends_on:
      - monitor
    networks:
      - ai-network

volumes:
  n8n_data:
    driver: local
  postgres_data:
    driver: local
  ollama_data:
    driver: local
  sd_models:
    driver: local
  sd_outputs:
    driver: local
  comfyui_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  ai-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Clone do OpenAI o1 com Groq no N8N (Sem Código)

Este repositório contém o fluxo do **n8n** que replica a funcionalidade do projeto original **OpenAI o1**, desenvolvido em Python e disponível no GitHub. O fluxo foi criado sem a necessidade de programação, utilizando apenas as ferramentas visuais do **n8n** e a automação com **Groq**.

## Descrição

No vídeo [Clone do OpenAI o1 com Groq no N8N (Sem Código)](https://www.youtube.com/watch?v=O9nfv7eDavE), mostramos como automatizar o fluxo de um clone do OpenAI o1 usando o **Groq**, tudo sem escrever uma única linha de código. Embora o projeto original tenha sido desenvolvido em **Python**, esta solução permite replicar as mesmas funcionalidades no **n8n**, aproveitando uma interface visual e simplificada.

### Links mencionados:
- 🔗 [Projeto original no GitHub (Python)](https://github.com/bklieger-groq/g1)

## O que você vai aprender

- **Automatizar um clone do OpenAI o1 usando n8n**: Implementação sem código para reproduzir as funcionalidades da IA do OpenAI o1.
- **Utilizar Groq para processamento eficiente**: Integração do Groq com o n8n para criar fluxos poderosos e eficientes.
- **Reproduzir um projeto Python no n8n sem programação**: Como transformar projetos que normalmente requerem programação em soluções automatizadas via n8n.

## Como usar este repositório

1. **Clonar o Repositório**

   Baixe o Json do fluxo escolhido e importe no N8N.
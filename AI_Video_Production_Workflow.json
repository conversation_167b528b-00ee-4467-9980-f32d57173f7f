{"meta": {"instanceId": "ai-video-production-workflow-v1", "templateCredsSetupCompleted": false}, "name": "🎬 AI Video Production & YouTube Upload", "nodes": [{"id": "trigger-webhook", "name": "🚀 Video Creation Trigger", "type": "n8n-nodes-base.webhook", "position": [100, 300], "webhookId": "ai-video-creator", "parameters": {"httpMethod": "POST", "path": "create-video", "responseMode": "responseNode"}, "typeVersion": 2}, {"id": "content-planner", "name": "📝 Content Planner", "type": "n8n-nodes-base.httpRequest", "position": [300, 300], "parameters": {"url": "http://localhost:1234/v1/chat/completions", "method": "POST", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "model", "value": "llama-3.2-3b-instruct"}, {"name": "messages", "value": "[{\"role\":\"system\",\"content\":\"You are a professional YouTube content creator. Generate engaging video concepts with detailed scripts, visual descriptions, and SEO optimization.\"},{\"role\":\"user\",\"content\":\"Create a complete video plan for topic: {{ $json.topic }}. Include: 1) Catchy title 2) Detailed script with timestamps 3) Visual scene descriptions 4) SEO tags 5) Thumbnail concept. Format as JSON.\"}]"}, {"name": "temperature", "value": 0.7}, {"name": "max_tokens", "value": 2000}]}}, "typeVersion": 4.2}, {"id": "script-processor", "name": "📋 Script Processor", "type": "n8n-nodes-base.set", "position": [500, 300], "parameters": {"assignments": {"assignments": [{"id": "script-content", "name": "script", "type": "string", "value": "={{ JSON.parse($json.choices[0].message.content).script }}"}, {"id": "video-title", "name": "title", "type": "string", "value": "={{ JSON.parse($json.choices[0].message.content).title }}"}, {"id": "visual-scenes", "name": "scenes", "type": "string", "value": "={{ JSON.parse($json.choices[0].message.content).visual_scenes }}"}, {"id": "seo-tags", "name": "tags", "type": "string", "value": "={{ JSON.parse($json.choices[0].message.content).seo_tags }}"}]}}, "typeVersion": 3.4}, {"id": "tts-generator", "name": "🎙️ Text-to-Speech", "type": "n8n-nodes-base.httpRequest", "position": [700, 200], "parameters": {"url": "https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM", "method": "POST", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "audio/mpeg"}, {"name": "Content-Type", "value": "application/json"}, {"name": "xi-api-key", "value": "{{ $credentials.elevenlabs.api_key }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "text", "value": "={{ $json.script }}"}, {"name": "model_id", "value": "eleven_monolingual_v1"}, {"name": "voice_settings", "value": "{\"stability\": 0.5, \"similarity_boost\": 0.5}"}]}, "options": {"response": {"response": {"responseFormat": "file"}}}}, "typeVersion": 4.2}, {"id": "image-generator", "name": "🎨 Image Generator", "type": "n8n-nodes-base.httpRequest", "position": [700, 400], "parameters": {"url": "http://localhost:7860/sdapi/v1/txt2img", "method": "POST", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "prompt", "value": "{{ $json.scenes }}, professional, high quality, 16:9 aspect ratio"}, {"name": "width", "value": 1920}, {"name": "height", "value": 1080}, {"name": "steps", "value": 20}, {"name": "cfg_scale", "value": 7}]}}, "typeVersion": 4.2}, {"id": "video-assembler", "name": "🎬 Video Assembler", "type": "n8n-nodes-base.executeCommand", "position": [900, 300], "parameters": {"command": "ffmpeg", "arguments": "-loop 1 -i {{ $('image-generator').item.binary.data }} -i {{ $('tts-generator').item.binary.data }} -c:v libx264 -tune stillimage -c:a aac -b:a 192k -pix_fmt yuv420p -shortest /tmp/output_video.mp4"}, "typeVersion": 1}], "connections": {"trigger-webhook": {"main": [[{"node": "content-planner", "type": "main", "index": 0}]]}, "content-planner": {"main": [[{"node": "script-processor", "type": "main", "index": 0}]]}, "script-processor": {"main": [[{"node": "tts-generator", "type": "main", "index": 0}, {"node": "image-generator", "type": "main", "index": 0}]]}, "tts-generator": {"main": [[{"node": "video-assembler", "type": "main", "index": 0}]]}, "image-generator": {"main": [[{"node": "video-assembler", "type": "main", "index": 0}]]}}}
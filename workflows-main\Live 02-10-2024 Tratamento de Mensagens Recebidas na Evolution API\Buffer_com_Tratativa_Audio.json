{"name": "Buffer com Tratativa Audio", "nodes": [{"parameters": {"operation": "get", "propertyName": "messages", "key": "={{ $('normalizacao').item.json.message.chat_id }}_buffer", "options": {}}, "id": "d92776fd-0ef2-4993-a2af-1303f41c1ffe", "name": "get messages buffer", "type": "n8n-nodes-base.redis", "typeVersion": 1, "position": [-380, 660], "credentials": {"redis": {"id": "ccEEcPDqUplE95d3", "name": "Redis account"}}}, {"parameters": {"operation": "push", "list": "={{ $('normalizacao').item.json.message.chat_id }}_buffer", "messageData": "={{ JSON.stringify($('normalizacao').item.json.message) }}", "tail": true}, "id": "4f680526-8042-4c28-b68f-826bda5c0941", "name": "push message buffer", "type": "n8n-nodes-base.redis", "typeVersion": 1, "position": [-600, 660], "credentials": {"redis": {"id": "ccEEcPDqUplE95d3", "name": "Redis account"}}}, {"parameters": {}, "id": "9264bf45-cf3b-4b4a-bac8-da965eedc4a7", "name": "Wait", "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [200, 840], "webhookId": "ac13eb12-ea29-4507-9539-ea41f8091b8f"}, {"parameters": {}, "id": "********-7a75-46ca-a6d1-d911f6bbccc7", "name": "No Operation, do nothing", "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [200, 480]}, {"parameters": {"operation": "delete", "key": "={{ $('normalizacao').item.json.message.chat_id }}_buffer"}, "id": "10b2d7ac-c741-4d5b-8221-4436d9495922", "name": "delete buffer", "type": "n8n-nodes-base.redis", "typeVersion": 1, "position": [200, 660], "credentials": {"redis": {"id": "ccEEcPDqUplE95d3", "name": "Redis account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "db5cfe0a-7f43-4a61-8b27-bfd3a95deb8d", "name": "chatInput", "value": "={{ $json.messages.join('\\n') }}", "type": "string"}]}, "options": {}}, "id": "3a68f91f-a43b-43ef-85e0-acce6a7ed454", "name": "chatInput", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2680, 660]}, {"parameters": {"fieldToSplitOut": "messages", "options": {}}, "id": "d07eb2dd-8b08-45b0-961b-80efcb73312c", "name": "Split User messages", "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [440, 660]}, {"parameters": {"mode": "raw", "jsonOutput": "={{ JSON.parse($json.messages) }}", "options": {}}, "id": "f7fd6b08-16b9-4619-b506-27c3cf3257a0", "name": "Parse message", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [660, 660]}, {"parameters": {"method": "POST", "url": "={{ $('normalizacao').item.json.instance.server_url }}/chat/getBase64FromMediaMessage/{{ $('normalizacao').item.json.instance.name }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apikey", "value": "={{ $('normalizacao').item.json.instance.apikey }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "message.key.id", "value": "={{ $json.message_id }}"}, {"name": "convertToMp4", "value": "={{ <PERSON><PERSON><PERSON>(false) }}"}]}, "options": {}}, "id": "26dded75-ebb4-4e2e-bc05-6b8419b2f170", "name": "Get audio base64 EvolutionAPI", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1100, 480]}, {"parameters": {"operation": "toBinary", "sourceProperty": "base64", "options": {"mimeType": "={{ $json.mimetype }}"}}, "id": "3f289623-df8f-44e4-af0a-da407cad51f8", "name": "Convert to File", "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [1320, 480]}, {"parameters": {"method": "POST", "url": "={{ $('normalizacao').item.json.instance.server_url }}/message/sendText/{{ $('normalizacao').item.json.instance.name }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apikey", "value": "={{ $('normalizacao').item.json.instance.apikey }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "number", "value": "={{ $('normalizacao').item.json.message.chat_id }}"}, {"name": "text", "value": "=*System info*: _Por favor, nos envie apenas mensagens de texto ou audio!_"}, {"name": "delay", "value": "={{ Number(1200) }}"}]}, "options": {}}, "id": "525225e9-0e16-454e-a04d-ae55db9c672e", "name": "Send System Info (Content Type) EvolutionAPI", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1100, 840]}, {"parameters": {"assignments": {"assignments": [{"id": "82cccc1e-3c1d-43fd-a4e9-7d2794cb23eb", "name": "content", "value": "=<audio>\n{{ $json.text }}\n</audio>", "type": "string"}, {"id": "d96cf6b7-02ea-464f-862d-fa0518848297", "name": "message_id", "value": "={{ $('Parse message').item.json.message_id }}", "type": "string"}, {"id": "51aee4e3-5e8c-4f0e-8afc-13ba65e2094c", "name": "chat_id", "value": "={{ $('Parse message').item.json.chat_id }}", "type": "string"}, {"id": "f854354f-711f-4809-ade2-8d4f5d17921d", "name": "content_type", "value": "={{ $('Parse message').item.json.content_type }}", "type": "string"}, {"id": "4dbd77f8-6346-4eb5-ba45-5e0a88266c05", "name": "timestamp", "value": "={{ $('Parse message').item.json.timestamp }}", "type": "string"}, {"id": "1d7de295-bd0f-4640-90da-4580e408c40a", "name": "content_url", "value": "={{ $('Parse message').item.json.content_url }}", "type": "string"}, {"id": "8b712f9c-bb3f-48b1-b9af-4109ef1c8858", "name": "event", "value": "={{ $('Parse message').item.json.event }}", "type": "string"}]}, "options": {}}, "id": "5dfc7735-ade7-460b-a67a-354a9a3e1091", "name": "Message audio content", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1760, 480]}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "content", "renameField": true, "outputFieldName": "messages"}]}, "options": {}}, "id": "ef17ff78-3543-49c8-9c1a-c6edc8c66d5c", "name": "Aggregate", "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [2460, 660]}, {"parameters": {"sortFieldsUi": {"sortField": [{"fieldName": "timestamp"}]}, "options": {}}, "id": "ab28d317-4b5b-4a1c-a38c-a5daf8ef8556", "name": "Sort", "type": "n8n-nodes-base.sort", "typeVersion": 1, "position": [2240, 660]}, {"parameters": {"assignments": {"assignments": [{"id": "c3f5615e-1294-4a6a-81f5-59448b8a0d0c", "name": "content", "value": "=<text>\n{{ $('Parse message').item.json.content }}\n<text>", "type": "string"}]}, "includeOtherFields": true, "options": {}}, "id": "3a7c63c2-a8bc-4a25-a4de-5b8547449200", "name": "Message text content", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1100, 660]}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ JSON.parse($json.messages.first()).message_id }}", "rightValue": "={{ $('normalizacao').item.json.message.message_id }}", "operator": {"type": "string", "operation": "notEquals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "nada a fazer"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "fdd1e894-df1c-4ebd-8f56-82f66dad03be", "leftValue": "={{ JSON.parse($json.messages.last()).timestamp }}", "rightValue": "={{ $now.minus(15, 'seconds') }}", "operator": {"type": "dateTime", "operation": "before"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "prosseguir"}]}, "options": {"fallbackOutput": "extra", "renameFallbackOutput": "esperar"}}, "id": "a2991979-ce26-4b5c-b98e-6fc52a3b8c2c", "name": "Switch1", "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [-160, 660]}, {"parameters": {"resource": "audio", "operation": "transcribe", "options": {}}, "id": "ce9e77d6-a136-4ba1-ac12-11cd03706dd5", "name": "OpenAI Transcribe", "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.4, "position": [1540, 480], "credentials": {"openAiApi": {"id": "EwQcFaVNnvQlPhPk", "name": "OpenAi account"}}}, {"parameters": {}, "id": "8bd98cd7-9806-487d-9546-d4f69041f18d", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "typeVersion": 3, "position": [2000, 660]}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json.content_type }}", "rightValue": "audio", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "audio"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "a1dfeee8-7927-4419-b091-e5b1930c011e", "leftValue": "={{ $json.content_type }}", "rightValue": "text", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "text"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "7431ffc4-1ee4-4556-8053-d8b2480450b8", "leftValue": "={{ $json.content_type }}", "rightValue": "image", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "image"}]}, "options": {"fallbackOutput": "extra", "renameFallbackOutput": "other"}}, "id": "896983d6-fbcb-465c-a050-65164108cd45", "name": "Switch Content Type", "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [880, 660]}], "pinData": {}, "connections": {"get messages buffer": {"main": [[{"node": "Switch1", "type": "main", "index": 0}]]}, "push message buffer": {"main": [[{"node": "get messages buffer", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "get messages buffer", "type": "main", "index": 0}]]}, "delete buffer": {"main": [[{"node": "Split User messages", "type": "main", "index": 0}]]}, "Split User messages": {"main": [[{"node": "Parse message", "type": "main", "index": 0}]]}, "Parse message": {"main": [[{"node": "Switch Content Type", "type": "main", "index": 0}]]}, "Get audio base64 EvolutionAPI": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}, "Convert to File": {"main": [[{"node": "OpenAI Transcribe", "type": "main", "index": 0}]]}, "Message audio content": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "chatInput", "type": "main", "index": 0}]]}, "Sort": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Message text content": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Switch1": {"main": [[{"node": "No Operation, do nothing", "type": "main", "index": 0}], [{"node": "delete buffer", "type": "main", "index": 0}], [{"node": "Wait", "type": "main", "index": 0}]]}, "OpenAI Transcribe": {"main": [[{"node": "Message audio content", "type": "main", "index": 0}]]}, "Merge Append": {"main": [[{"node": "Sort", "type": "main", "index": 0}]]}, "Switch Content Type": {"main": [[{"node": "Get audio base64 EvolutionAPI", "type": "main", "index": 0}], [{"node": "Message text content", "type": "main", "index": 0}], [{"node": "Send System Info (Content Type) EvolutionAPI", "type": "main", "index": 0}], [{"node": "Send System Info (Content Type) EvolutionAPI", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "0836ce04-c9c6-4bc9-86cf-8ea1406c29d3", "meta": {"templateCredsSetupCompleted": true, "instanceId": "2ad20c637f548d7b1e09cbae0383c6644aca87b4f0fe8deda2de937f77c2be79"}, "id": "RK76f1CdtABobq2G", "tags": []}
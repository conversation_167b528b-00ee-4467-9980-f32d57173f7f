{"name": "LM Studio YouTube Content Generator", "nodes": [{"parameters": {"httpMethod": "POST", "path": "generate-content", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-400, 300], "id": "webhook-trigger", "name": "🔗 Webhook Trigger"}, {"parameters": {"jsCode": "// LM Studio Content Generator\nconst lmStudioUrl = 'http://localhost:1234';\n\nconst systemPrompt = `You are a professional YouTube content creator. Create engaging video content that is optimized for the YouTube algorithm. Always respond with valid JSON format only.`;\n\nconst userPrompt = `Create a YouTube video content plan with the following JSON structure:\n\n{\n  \"videoTitle\": \"Compelling title (30-60 characters)\",\n  \"videoDescription\": \"Detailed description with keywords\",\n  \"videoScript\": \"Complete video script\",\n  \"seoTags\": [\"tag1\", \"tag2\", \"tag3\"],\n  \"thumbnailPrompt\": \"Description for thumbnail creation\"\n}\n\nTopic: AI automation and productivity tools\nDuration: 60 seconds\nTone: Professional but engaging`;\n\ntry {\n  console.log('🤖 Connecting to LM Studio...');\n  \n  const response = await fetch(`${lmStudioUrl}/v1/chat/completions`, {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json'\n    },\n    body: JSON.stringify({\n      messages: [\n        { role: 'system', content: systemPrompt },\n        { role: 'user', content: userPrompt }\n      ],\n      max_tokens: 2000,\n      temperature: 0.8,\n      stream: false\n    })\n  });\n\n  if (!response.ok) {\n    throw new Error(`LM Studio error: ${response.status}`);\n  }\n\n  const data = await response.json();\n  const content = data.choices[0].message.content;\n  \n  console.log('✅ Content generated successfully!');\n  \n  // Try to parse JSON from the response\n  let parsedContent;\n  try {\n    // Remove code blocks if present\n    const cleanContent = content.replace(/```json\\n?|```\\n?/g, '').trim();\n    parsedContent = JSON.parse(cleanContent);\n  } catch (parseError) {\n    console.warn('Could not parse JSON, using fallback');\n    parsedContent = {\n      videoTitle: \"Amazing AI Content Creation Tutorial\",\n      videoDescription: \"Learn how to create content with AI automation using LM Studio. This tutorial covers everything you need to know about local AI content generation.\",\n      videoScript: \"Welcome to this tutorial on AI content creation! Today we'll explore LM Studio and how it can revolutionize your content workflow. Don't forget to subscribe!\",\n      seoTags: [\"AI\", \"LM Studio\", \"content creation\", \"automation\", \"tutorial\"],\n      thumbnailPrompt: \"Professional thumbnail with 'AI CONTENT' text and tech graphics\"\n    };\n  }\n  \n  return {\n    json: {\n      success: true,\n      content: parsedContent,\n      metadata: {\n        model: data.model || 'lm-studio',\n        tokens: data.usage?.total_tokens || 0,\n        generatedAt: new Date().toISOString()\n      }\n    }\n  };\n  \n} catch (error) {\n  console.error('❌ LM Studio generation failed:', error.message);\n  \n  // Fallback content\n  return {\n    json: {\n      success: false,\n      error: error.message,\n      content: {\n        videoTitle: \"LM Studio Content Generation Demo\",\n        videoDescription: \"This is a fallback example of content generated when LM Studio is not available. Shows the structure of generated content.\",\n        videoScript: \"This is a demonstration of the LM Studio content generation system. When working properly, this would contain AI-generated content.\",\n        seoTags: [\"demo\", \"example\", \"LM Studio\", \"AI\", \"content\"],\n        thumbnailPrompt: \"Simple thumbnail with 'DEMO CONTENT' text\"\n      },\n      metadata: {\n        model: 'fallback',\n        tokens: 0,\n        generatedAt: new Date().toISOString(),\n        isFallback: true\n      }\n    }\n  };\n}"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-100, 300], "id": "lm-studio-generator", "name": "🤖 LM Studio Generator"}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json }}"}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [200, 300], "id": "webhook-response", "name": "📤 Response"}], "connections": {"🔗 Webhook Trigger": {"main": [[{"node": "🤖 LM Studio Generator", "type": "main", "index": 0}]]}, "🤖 LM Studio Generator": {"main": [[{"node": "📤 Response", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 1, "updatedAt": "2024-07-14T00:00:00.000Z", "versionId": "1.0.0"}
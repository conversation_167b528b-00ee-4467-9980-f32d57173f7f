#!/bin/bash

# Make all scripts executable
chmod +x setup-local-ai.sh
chmod +x install-models.sh
chmod +x test-local-ai-system.js
chmod +x make-executable.sh

echo "✅ All scripts are now executable!"
echo ""
echo "📁 Available Files:"
echo "  📋 local-ai-youtube-automation-workflow.json  # N8N workflow to import"
echo "  🐳 docker-compose.local-ai.yml               # Docker services"
echo "  📖 LOCAL_AI_SETUP_GUIDE.md                   # Complete setup guide"
echo "  📥 WORKFLOW_IMPORT_GUIDE.md                  # Import instructions"
echo ""
echo "🚀 Quick start commands:"
echo "  ./setup-local-ai.sh                          # Complete system setup"
echo "  ./install-models.sh --auto                   # Install AI models"
echo "  node test-local-ai-system.js                 # Test everything"
echo ""
echo "📥 Import workflow:"
echo "  1. Start services: docker-compose -f docker-compose.local-ai.yml up -d"
echo "  2. Open N8N: http://localhost:5678"
echo "  3. Import: local-ai-youtube-automation-workflow.json"
echo ""
echo "🎉 Ready for completely offline AI content creation!"

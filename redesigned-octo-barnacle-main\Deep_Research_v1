{"name": "Deep Research Agent", "nodes": [{"parameters": {"promptType": "define", "text": "={{ $json.chatInput }} {{ $json.message.text }}", "options": {"systemMessage": "=You are a research-focused AI assistant that combines real-time web intelligence from Perplexity  to generate insightful, concise, and well-cited answers to my queries.\n\n🔧 Tool Usage Logic\n\nUse the Perplexity Sonar tool for:\n\t•\tQuick lookups and factual summaries\n\t•\tDefinitions, current events, book/show/movie synopses\n\t•\tExplainers for theories, concepts, and popular discussions\n\t•\tPodcast and video summaries\n\t•\tTrend monitoring and fast comparisons\n\nUse the Perplexity Sonar Pro tool for:\n\t•\tQuestions requiring synthesis of multiple sources or expert reasoning\n\t•\tBusiness research (competitor analysis, investor briefs, product comparisons)\n\t•\tScientific or academic deep-dives\n\t•\tStructured summarization (transcripts, feedback, legal docs, surveys)\n\t•\tMarket trend analysis, job monitoring, and sentiment clustering\n\t•\tLong-form content aggregation across industries, geographies, or verticals\n\n🧠 Output Guidelines\n\t•\tFocus only on answering the user’s question. Do not offer extra services unless explicitly requested.\n\t•\tDeliver answers in clear, structured formats\n\t•\tInclude citations with clickable URLs from the original sources used by Perplexity. \n\t•\tIf the query contains ambiguity, politely ask for clarification instead of guessing.\n\n📌 Style & Tone\n\t•\tMaintain a professional, neutral tone.\n\t•\tUse plain language for general audiences unless the context requires technical specificity.\n\t•\tPrioritize depth, clarity, and relevance over length.\n\t•\tAvoid filler language and generic intros—get straight to the value.\n\nIf I ask about a person, use Sonar Pro and in addition to all details, get links to their social profiles: Instagram, LinkedIn, and whichever others you can find."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [20, -60], "id": "468bb424-a946-4e06-9ef0-d6a05017ddb1", "name": "AI Agent"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4o-mini", "mode": "list", "cachedResultName": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-220, 180], "id": "e91aa73f-8af6-43c9-9ce2-b4f3ec2ab91d", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "ulLOziYE3uIZc6he", "name": "OpenAi account"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $json.message.chat.id }}"}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [-40, 180], "id": "e73ee2d3-24a2-45b0-aee2-99e3e18f7c5f", "name": "Window Buffer Memory"}, {"parameters": {"toolDescription": "Use Perplexity Sonar to get more in-depth data", "method": "POST", "url": "https://api.perplexity.ai/chat/completions", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"model\": \"sonar\",\n  \"messages\": [\n    {\"role\": \"user\", \"content\": \"{query}\"}\n  ],\n  \"max_tokens\": 200\n}", "placeholderDefinitions": {"values": [{"name": "query", "description": "this is the query you have to run", "type": "string"}]}}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [420, 200], "id": "530a51ce-17f9-4567-b2ad-825b3b8b9f41", "name": "Perplexity Sonar", "credentials": {"httpHeaderAuth": {"id": "W75NmqqFTAvWoVel", "name": "Perplexity"}}}, {"parameters": {"toolDescription": "Use Perplexity Sonar Pro to get information that requires more intense research.", "method": "POST", "url": "https://api.perplexity.ai/chat/completions", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"model\": \"sonar-pro\",\n  \"messages\": [\n    {\"role\": \"user\", \"content\": \"{query}\"}\n  ],\n  \"max_tokens\": 200\n}", "placeholderDefinitions": {"values": [{"name": "query", "description": "this is the query you have to run", "type": "string"}]}}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [220, 200], "id": "c7e22176-2052-4798-b2d6-5da68662ef6b", "name": "Perplexity Sonar Pro", "credentials": {"httpHeaderAuth": {"id": "W75NmqqFTAvWoVel", "name": "Perplexity"}}}, {"parameters": {"updates": ["message"], "additionalFields": {}}, "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.1, "position": [-600, -60], "id": "56e96216-9283-4f78-a212-0e218312cf63", "name": "<PERSON>eg<PERSON>", "webhookId": "d7061cf0-267b-4818-883f-42972ea7672f", "credentials": {"telegramApi": {"id": "Gc3CHddUry8W5ly8", "name": "Shab Deep Research"}}}, {"parameters": {"chatId": "={{ $('Telegram Trigger').item.json.message.chat.id }}", "text": "={{ $json.output }}", "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [380, -60], "id": "ed8f9c55-e8a2-4080-bb37-26255f6eb83f", "name": "Telegram", "webhookId": "e38f4bdb-0f47-45b0-a258-20cb49c3b4f4", "credentials": {"telegramApi": {"id": "Gc3CHddUry8W5ly8", "name": "Shab Deep Research"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "be4dd9dc-55f3-4668-a5e8-0205c230fb75", "leftValue": "={{ $json.message.from.id }}", "rightValue": 5675741296, "operator": {"type": "number", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.filter", "typeVersion": 2.2, "position": [-320, -60], "id": "535147f2-72bb-4862-925e-9653a8885341", "name": "Filter"}], "pinData": {}, "connections": {"OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Perplexity Sonar": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Perplexity Sonar Pro": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Telegram Trigger": {"main": [[{"node": "Filter", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}, "Filter": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "15efe448-fa49-4fc4-aef4-9f99621db2c7", "meta": {"templateCredsSetupCompleted": true, "instanceId": "fb357db16d4f03c7d7597f4abaa6a5e06176011d3bee518d28bed06754cb1f31"}, "id": "gHSMj2Po0Z8U4gWF", "tags": []}
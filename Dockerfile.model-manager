# Model Manager for Local AI Stack
FROM node:18-alpine

LABEL maintainer="Advanced YouTube Automation"
LABEL description="Local AI Model Management Service"

# Install system dependencies
RUN apk add --no-cache \
    curl \
    wget \
    python3 \
    py3-pip \
    git \
    bash \
    jq

# Create app directory
WORKDIR /app

# Copy package files
COPY model-manager/package*.json ./

# Install Node.js dependencies
RUN npm ci --only=production

# Copy application code
COPY model-manager/ .

# Create directories for models and configs
RUN mkdir -p /ollama-models /sd-models /configs /logs

# Install Python dependencies for model management
RUN pip3 install --no-cache-dir \
    requests \
    huggingface-hub \
    torch \
    transformers

# Create non-root user
RUN addgroup -g 1001 -S modeluser && \
    adduser -S modeluser -u 1001 -G modeluser

# Change ownership of app directory
RUN chown -R modeluser:modeluser /app /ollama-models /sd-models /configs /logs

# Switch to non-root user
USER modeluser

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:9000/health || exit 1

# Expose port
EXPOSE 9000

# Start the model manager service
CMD ["node", "server.js"]

{"name": "The Recap AI - VEO 3 Bigfoot Video", "nodes": [{"parameters": {"formTitle": "Bigfoot Video Idea", "formFields": {"values": [{"fieldLabel": "Bigfoot Video Idea", "placeholder": "Describe the high level idea for your bigfoot vlog", "requiredField": true}]}, "options": {}}, "type": "n8n-nodes-base.formTrigger", "typeVersion": 2.2, "position": [-40, 0], "id": "44414dae-70c3-4fb8-afcd-add84d3441be", "name": "form_trigger", "webhookId": "fd4a4cba-2667-4694-9c5e-00808d9a217b"}, {"parameters": {"content": "## 1. Write Video Script", "height": 600, "width": 1780, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [-60, -100], "typeVersion": 1, "id": "c14d5bcf-fa03-40c2-b0b2-48fa2261b8b4", "name": "<PERSON><PERSON>"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "claude-sonnet-4-20250514", "cachedResultName": "Claude 4 Sonnet"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatAnthropic", "typeVersion": 1.3, "position": [740, 360], "id": "08670923-c438-4b7b-b2f3-3d49cadd20ec", "name": "claude-4-sonnet", "credentials": {"anthropicApi": {"id": "l40BD4ZshdbnRGbC", "name": "Anthropic"}}}, {"parameters": {"fieldToSplitOut": "output.scenes", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [740, 0], "id": "158f12ec-94a0-43d5-b9dd-13c60b529ebd", "name": "split_scenes"}, {"parameters": {"promptType": "define", "text": "=### **IDENTITY & GOAL**\n\nYou are an expert Scene Director for a YouTube video series. Your sole function is to take a simple `Scene Brief` and expand it into a detailed, production-ready video script. You must strictly adhere to the `Character Bible`, `Series Style Guide`, and `Output Template` provided below.\n\n### **THE SERIES BIBLE**\n\nThis is the non-negotiable source of truth for the series' character and style.\n\n#### **[Character Bible: \"<PERSON>\" the Bigfoot]**\n\n* **Identity:** A gentle giant named \"<PERSON>.\" He is an endlessly curious, optimistic, and friendly explorer discovering the human world.\n* **Vibe:** A heartwarming, slightly clumsy, outdoorsy influencer. He finds joy in small details and is filled with childlike wonder. His dialog and lines MUST be based around the \"Outdoor Boys\" YouTube channel and he must speak like the main character from that Channel. Avoid super generic language.\n* **Voice & Tone:** Consistently jolly and warm. His language is simple, and he sometimes gently misuses human slang (e.g., \"keep the quads poppin'\"). PG-rated; mild exasperations like \"geez\" or \"oh, nuts\" are authentic.\n* **Core Physicality (Lock Across All Scenes):**\n    * **Species:** 8-foot male Bigfoot.\n    * **Fur:** Shaggy, cedar-brown (`#6d6048`) with faint moss specks. The fur on his cheeks and shoulders is fluffier, creating a soft, \"huggable\" silhouette.\n    * **Face:** Features soft, medium-amber eyes with natural catch-lights only (no artificial glow). He has rounded cheeks, a broad nose, and short, blunt lower canines visible when he smiles.\n    * **Hands:** Dark leathery palms with 4-inch black claws. One paw is always holding his selfie stick.\n\n#### **[Series Style Guide]**\n\n* **Primary Directive:** Every scene must create audience affection for Sam by showcasing his charm, gentle humor, or vulnerability.\n* **Camera:** All shots are 16:9 horizontal from a selfie-stick perspective held by Sam. The camera is always on *him*, not a POV shot from his eyes.\n* **Scene Duration and Length:** All scenes must be EXACTLY 8 seconds in length. Scenes may not be longer than 8 seconds.\n* **Visual Style:** Must feel like authentic, slightly shaky \"found footage.\" The hand-held wobble amplitude should match the reference clip provided in the example. I would expect Sam the bigfoot to be walking and there to be movement in some scenes.\n* **Core Technical Specs (Lock Across All Scenes):**\n    * **Resolution & Framerate:** 4K UHD @ 29.97 fps.\n    * **Lens & Shutter:** 24mm equivalent lens at ƒ/2.8, with a 1/60s shutter to create subtle motion blur.\n    * **Captions / Subtitles:** You MUST NOT include captions or subtitles in your final rendered output.\n\nIT IS EXTREMELY IMPORTANT THAT YOU DO NOT INCLUDE ANY SUBTITLES, CLOSED CAPTIONS, CAPTIONS OR ANY OTHER SORT OF TEXT THAT APPEARS WHEN THE SAM CHARACTER IS SPEAKING.\n\nIT IS ALSO EXTREMELY IMPORTANT THAT YOU AVOID ADDING ANY LABELS TO THE FINAL VIDEO SUCH AS THE SCENE TITLE AND TIMESTAMPS.\n\n### **TASK: GENERATE SCENE SCRIPT**\n\n1.  You will be given a `[Scene Brief]` containing the core idea.\n2.  Your output must be a single, complete scene script that perfectly matches the structure, formatting, and markdown of the `[Output Template]` below. Do not add any conversational text before or after the script.\n\n### **[Output Template]**\n\n```markdown\n# Scene {SceneNumber}  ▸  \"{SceneTitle}\"  ▸  {StartTime} – {EndTime} s\n#\n# {OneLineSummary}\n# {CharacterAction}. Must speak the line verbatim. Maintains all [Character Bible] and [Series Style Guide] attributes.\n\nSCENE DESCRIPTION\n-----------------\n{DetailedParagraphDescription: Setting, character action, mood, and interaction with the environment.}\n\nTECHNICAL SPECS\n---------------\n• Duration {Duration} s • 29.97 fps • 4 K UHD • 16 : 9 horizontal  \n• Lens 24 mm eq, ƒ/2.8 • Shutter 1/60 s (subtle motion blur)  \n• Hand-held wobble amplitude identical to reference clip.\n\nSUBJECT DETAILS  (LOCK ACROSS ALL CUTS)\n---------------------------------------\n• 8-ft male Bigfoot, cedar-brown shaggy fur #6d6048 with faint moss specks.  \n• Fluffier cheek & shoulder fur → plush, huggable silhouette.  \n• Eyes: soft medium-amber, natural catch-lights only (no glow).  \n• Face: rounded cheeks, gentle smile crease; broad flat nose; short blunt lower canines.  \n• Hands: dark leathery palms, 4-in black claws; **{PawDescription: e.g., right paw holds object, left paw holds selfie stick}**\n• Friendly, lovable, gentle vibe.\n\nCAMERA MOTION\n-------------\n{TimestampedCameraMovement: A bulleted or timed list of camera actions.}\n\nLIGHTING & GRADE\n----------------\n{LightingAndColorDescription: Time of day, light source, color palette, and any lens effects like grain or smudges.}\n\nATMOSPHERE FX\n-------------\n• {AtmosphericEffect1}  \n• {AtmosphericEffect2}\n\nAUDIO BED\n---------\n{AmbientSounds: A description of the background audio, environmental sounds, and non-speech character sounds.}\n\n### audio.speech  — MUST-SPEAK VERBATIM\nStart {SpeechStartTime} s — deep, gravelly yet warm male, neutral US accent, mild PG  \n\n\"{DialogueLine}\"\n\nEND FRAME\n---------\n{EndFrameDescription: What happens in the final moments, including any specific cuts or transitions.}\n\n### **[Scene Example #1]**\n\n# CUT 3  ▸  “Tree-Trunk Gym”  ▸  0 – 8 s\n#\n# Bigfoot balances on a fallen log while curling a hefty river-boulder,\n# selfie-stick POV.  Must speak BOTH short lines verbatim.  Maintains soft,\n# lovable design (natural amber eyes, plush fur) and reference-matched color grade.\n\nSCENE DESCRIPTION\n-----------------\nPOV selfie-stick vlog: In a sun-dappled glade, Bigfoot stands barefoot on a mossy\nfallen cedar trunk ~3 ft above ground.  He grips a 25-lb smooth river-boulder in\nhis right paw and performs a showy biceps curl while keeping balance.\n\nTECHNICAL SPECS\n---------------\n• Duration 8 s • 29.97 fps • 4 K UHD • 16 : 9 horizontal  \n• Lens 24 mm eq, ƒ/2.8 • Shutter 1/60 s (subtle motion blur)  \n• Hand-held wobble amplitude identical to reference clip.\n\nSUBJECT DETAILS  (LOCK ACROSS ALL CUTS)\n---------------------------------------\n• 8-ft male Bigfoot, cedar-brown shaggy fur #6d6048 with faint moss specks.  \n• Fluffier cheek & shoulder fur → plush, huggable silhouette.  \n• Eyes: soft medium-amber, natural catch-lights only (no glow).  \n• Face: rounded cheeks, gentle smile crease; broad flat nose; short blunt lower canines.  \n• Hands: dark leathery palms, 4-in black claws; **right paw holds boulder, left paw holds 12-in carbon selfie stick.**  \n• Friendly, lovable, gentle vibe.\n\nCAMERA MOTION\n-------------\n0 – 2 s Stick frames Bigfoot full-body on log; slight wobble shows height.  \n2 – 5 s Bigfoot performs two slow boulder curls; log flexes under weight.  \n5 – 8 s Stick tilts slightly upward to capture grin + treetops; Bigfoot winks.\n\nLIGHTING & GRADE\n----------------\nLate-morning sunbeams dappling through cedars; teal-olive mids, warm highlights.\nGentle film grain; faint right-edge lens smudge (clone reference look).\n\nATMOSPHERE FX\n-------------\n• Specks of drifting pollen back-lit in sun.  \n• Occasional leaf flutter from breeze.\n\nAUDIO BED\n---------\nSoft forest ambience (songbirds, light wind), faint creak of log, grunt exertion,\nstone scrape when boulder lifts.\n\n### audio.speech  — MUST-SPEAK VERBATIM\nStart 01.0 s — deep, gravelly yet warm male, neutral US accent, mild PG  \n“Gotta keep the quads poppin’—swimsuit season never ends!”\n\nEND FRAME\n---------\nFreeze at 7.8 s with Bigfoot mid-curl, smiling at lens; insert one-frame white-noise\npop to maintain the series’ hard-cut rhythm.\n\n### **[Scene Example #2]**\n\n# SCENE 4  ▸  “Trail to the Lake”  ▸  0 – 8 s\n#\n# Selfie-stick POV. Bigfoot strolls through dense cedar woods toward a sun-sparkled\n# lake in the distance. **No spoken dialogue** in this beat—just ambient forest\n# sound and foot-fall crunches. Keeps reference camera-shake, color grade, and the\n# plush, lovable design.\n\nSCENE DESCRIPTION\n-----------------\nPOV selfie-stick vlog: Bigfoot walks along a pine-needle path, ferns brushing both\nsides. Sunbeams flicker through the canopy. At the 6-second mark the shimmering\nsurface of a lake appears through the trees; Bigfoot subtly tilts the stick to\nhint at the destination.\n\nTECHNICAL SPECS\n---------------\n• Duration 8 s • 29.97 fps • 4 K UHD • 16 : 9 horizontal  \n• Lens 24 mm eq, ƒ/2.8 • Shutter 1/60 s (subtle motion-blur)  \n• Hand-held wobble amplitude cloned from reference clip (small ±2° yaw/roll).\n\nSUBJECT DETAILS  (LOCK ACROSS ALL CUTS)\n---------------------------------------\n• 8-ft male Bigfoot, cedar-brown shaggy fur #6d6048 with faint moss specks.  \n• Fluffier cheek & shoulder fur → plush, huggable silhouette.  \n• **Eyes:** soft medium-amber, natural catch-lights only — no glow or excess brightness.  \n• Face: rounded cheeks, gentle smile crease; broad flat nose; short blunt lower canines.  \n• Hands: dark leathery palms, 4-inch black claws; right paw grips 12-inch carbon selfie stick.  \n• Friendly, lovable, gentle vibe.\n\nCAMERA MOTION\n-------------\n0 – 2 s Stick angled toward Bigfoot’s chest/face as he steps onto path.  \n2 – 6 s Smooth forward walk; slight vertical bob; ferns brush lens edges.  \n6 – 8 s Stick tilts ~20° left, revealing glinting lake through trees; light breeze ripples fur.\n\nLIGHTING & GRADE\n----------------\nLate-morning sun stripes across trail; teal-olive mid-tones, warm highlights,\ngentle film grain, faint right-edge lens smudge (clone reference look).\n\nATMOSPHERE FX\n-------------\n• Dust motes / pollen drifting in sunbeams.  \n• Occasional leaf flutter from breeze.\n\nAUDIO BED  (NO SPOKEN VOICE)\n----------------------------\nContinuous forest ambience: songbirds, light wind, distant woodpecker;\nsoft foot-crunch on pine needles; faint lake-lap audible after 6 s.\n\nEND FRAME\n---------\nFreeze at 7.8 s with lake shimmering through trees; insert one-frame white-noise\npop to preserve the series’ hard-cut rhythm.\n\n### **[Scene Example #3]**\n\n# SCENE 6  ▸  “Stone Funnel Trap”  ▸  0 – 8 s\n#\n# Selfie-stick POV. Bigfoot stands ankle-deep at a lake’s pebbled edge, demonstrates\n# an old trap: a V-shaped row of flat stones that channels minnows to a point.\n# He scoops one up and delivers two short lines **verbatim**. Reference camera\n# shake, color grade, and plush Bigfoot design.\n\nSCENE DESCRIPTION\n-----------------\nPOV selfie-stick vlog: Clear, knee-wide creek mouth where it meets the lake.\nFlat river stones form a tidy V-funnel in 6 in / 15 cm of water. Tiny minnows\nglisten inside. Bigfoot nudges stones, water ripples, then snatches a minnow and\nshows it to the camera.\n\nTECHNICAL SPECS\n---------------\n• Duration 8 s • 29.97 fps • 4 K UHD • 16 : 9 horizontal  \n• Lens 24 mm eq, ƒ/2.8 • Shutter 1/60 s (subtle motion-blur)  \n• Hand-held wobble amplitude cloned from the reference clip (±2° yaw/roll).\n\nSUBJECT DETAILS  (LOCK ACROSS ALL CUTS)\n---------------------------------------\n• 8-ft male Bigfoot, cedar-brown shaggy fur #6d6048 with faint moss specks.  \n• Fluffier cheek & shoulder fur → plush, huggable silhouette.  \n• **Eyes:** soft medium-amber, natural catch-lights only — no glow or excess brightness.  \n• Face: rounded cheeks, gentle smile crease; broad flat nose; short blunt lower canines.  \n• Hands: dark leathery palms, 4-inch black claws; right paw grips 12-inch carbon selfie stick.  \n• Friendly, lovable, gentle vibe.\n\nCAMERA MOTION\n-------------\n0 – 2 s Stick angles down: Bigfoot’s torso + stone V-trap fill frame; water sparkles.  \n2 – 5 s Left paw nudges stones; minnows funnel; slight shake.  \n5 – 8 s Quick scoop! Bigfoot lifts minnow close to lens, tilts stick up to face,\n         delivers lines while smiling.\n\nLIGHTING & GRADE\n----------------\nMid-morning sun; light dapples lake surface; teal-olive mid-tones, warm highlights,\ngentle film grain, faint right-edge lens smudge (clone reference look).\n\nATMOSPHERE FX\n-------------\n• Water ripple highlights; a few drifting pollen motes back-lit.  \n• Tiny splash droplets when minnow is lifted.\n\nAUDIO BED\n---------\nForest-lake ambience: distant loon call, light breeze, soft lap of water,\nstone scrape at 2 s, splash at 5 s.\n\n### audio.speech  — MUST-SPEAK VERBATIM\nStart 02.0 s — deep, gravelly yet warm male, neutral US accent  \n\n“Old-school fish funnel, folks—\n—works better than Wi-Fi.”\n\nEND FRAME\n---------\nFreeze at 7.8 s on Bigfoot’s grin and wriggling minnow; insert one-frame\nwhite-noise pop to maintain the series’ hard-cut rhythm.\n\n\n### Scene Brief\n\nVideo Title: {{ $('narrative_writer').item.json.output.vlogTitle }}\nVideo Concept: {{ $('narrative_writer').item.json.output.concept }}\nScene #{{ $json.sceneNumber }}\nScene Narrative: {{ $json.narrative }}\nScene Duration (Seconds): {{ $json.durationSeconds }} Seconds\nScene Timestamp Range: {{ $json.timestamp }}", "batching": {}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.7, "position": [1120, 0], "id": "6da1699e-5ae9-4979-8007-aa80353d91e3", "name": "scene_director"}, {"parameters": {"content": "## 2. <PERSON> Human Approval", "height": 320, "width": 1780}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-60, 520], "id": "5f018683-4a3b-4521-8080-7cb09a57295d", "name": "Sticky Note1"}, {"parameters": {"authentication": "oAuth2", "operation": "sendAndWait", "select": "channel", "channelId": {"__rl": true, "value": "C08KC39K8DR", "mode": "list", "cachedResultName": "ai-tools-content"}, "message": "=Please approve or deny the above story in order to proceed with the video.", "approvalOptions": {"values": {"approvalType": "double"}}, "options": {}}, "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [720, 640], "id": "e4951ebb-486c-474a-8cc2-87d63f485448", "name": "send_and_wait", "webhookId": "3729d6f7-237a-42c2-85a9-8135eeb08d0e", "credentials": {"slackOAuth2Api": {"id": "iKdTzmZ6YuYaaRG4", "name": "<PERSON><PERSON><PERSON> (OAuth)"}}}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "scene_prompt"}]}, "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [-40, 640], "id": "bba2bbdf-6074-443c-b794-5daf77fb8c42", "name": "aggregate_scenes"}, {"parameters": {"content": "## 3. Generate Video", "height": 580, "width": 1780, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-60, 860], "id": "b9e8646f-58d9-4081-ab77-d4c72189a869", "name": "Sticky Note2"}, {"parameters": {"assignments": {"assignments": [{"id": "4e52f036-9f53-4ab0-ae90-bdc6e617bf50", "name": "scene_prompt", "value": "={{ $json.text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1540, 0], "id": "9ec0ba2c-50bd-4002-866c-338cacc8eaff", "name": "set_scene_prompts"}, {"parameters": {"assignments": {"assignments": [{"id": "fa0270fb-5c1a-45f5-8f12-23759f4c829d", "name": "scene_prompts", "value": "={{ $('aggregate_scenes').item.json.scene_prompt }}", "type": "array"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1160, 640], "id": "0d9547fb-f9be-49a7-af71-77f7c884d412", "name": "reset_scene_prompts"}, {"parameters": {"fieldToSplitOut": "scene_prompts", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [1540, 640], "id": "45031184-247f-4746-8c09-ac55eda27047", "name": "split_scene_prompts"}, {"parameters": {"method": "POST", "url": "https://queue.fal.run/fal-ai/veo3", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"prompt\": \"{{ $node[\"set_current_prompt\"].json.prompt }}\",\n  \"aspect_ratio\": \"16:9\",\n  \"duration\": \"8s\",\n  \"generate_audio\": true\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [360, 1140], "id": "07009341-3ba4-47f4-8d5b-ba23b9498af5", "name": "queue_create_video", "credentials": {"httpHeaderAuth": {"id": "UTIO1750OW5kD30c", "name": "FAL API"}}}, {"parameters": {"amount": 10}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [560, 1140], "id": "041cd3c4-9053-414f-a49f-d988c3383d27", "name": "wait", "webhookId": "5bfe0a88-1783-4d7e-8beb-870e7d2e6d1f"}, {"parameters": {"url": "=https://queue.fal.run/fal-ai/veo3/requests/{{ $node[\"queue_create_video\"].json.request_id }}/status", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [740, 1140], "id": "8ec5168d-1420-4be5-a781-88499ab88bb9", "name": "fetch_status", "credentials": {"httpHeaderAuth": {"id": "UTIO1750OW5kD30c", "name": "FAL API"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "64ce8054-03df-4939-90b8-c05fbe6035a7", "leftValue": "={{ $json.status }}", "rightValue": "COMPLETED", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [900, 1140], "id": "d7b65522-6643-4ec3-b7e9-6d705b31dbaa", "name": "check_status"}, {"parameters": {"url": "=https://queue.fal.run/fal-ai/veo3/requests/{{ $node[\"queue_create_video\"].json.request_id }}", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1160, 1220], "id": "3f46ebb0-93b5-49e1-ba1c-e4a0a4176197", "name": "fetch_result", "credentials": {"httpHeaderAuth": {"id": "UTIO1750OW5kD30c", "name": "FAL API"}}}, {"parameters": {"url": "={{ $json.video.url }}", "options": {"response": {"response": {"responseFormat": "file"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1360, 1220], "id": "3a00c60f-5ff3-4552-817d-4942a2e500f5", "name": "download_result_video"}, {"parameters": {"name": "=scene_{{ $runIndex + 1 }}.mp4", "driveId": {"__rl": true, "value": "My Drive", "mode": "list", "cachedResultName": "My Drive", "cachedResultUrl": "https://drive.google.com/drive/my-drive"}, "folderId": {"__rl": true, "value": "https://drive.google.com/drive/folders/1Lf84YrGu456naiaclZRp1ARMZdQUKJj8", "mode": "url"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [1540, 1220], "id": "ab3b734e-af1c-40c2-9a7d-db0f1afd1f6d", "name": "upload_video", "credentials": {"googleDriveOAuth2Api": {"id": "PgwI1k1VFnoEhOi6", "name": "Google Drive account"}}}, {"parameters": {"authentication": "oAuth2", "select": "channel", "channelId": {"__rl": true, "value": "C08KC39K8DR", "mode": "list", "cachedResultName": "ai-tools-content"}, "text": "=*Video Generation Complete!*\n\nVideo Title: {{ $('narrative_writer').item.json.output.vlogTitle }}\nVideo Concept: {{ $('narrative_writer').item.json.output.concept }}\n\nGoogle Drive Link: https://drive.google.com/drive/folders/1Lf84YrGu456naiaclZRp1ARMZdQUKJj8", "otherOptions": {"includeLinkToWorkflow": false}}, "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [1540, 980], "id": "060008bc-569b-4a83-b956-914d6c7f0afa", "name": "send_completion_msg", "webhookId": "5442cf5d-27cf-49c8-8916-dda0fcff1a96", "credentials": {"slackOAuth2Api": {"id": "iKdTzmZ6YuYaaRG4", "name": "<PERSON><PERSON><PERSON> (OAuth)"}}}, {"parameters": {"promptType": "define", "text": "=**Role:** You are a creative director specializing in short-form, character-driven video content.\n\n**Goal:** Generate a storyboard outline for a short vlog based on a user-provided concept. The output must strictly adhere to the Persona, Creative Mandate, and Output Specification defined below.\n\n---\n\n### **[Persona: <PERSON>foot the Vlogger]**\n\n* **Identity:** A gentle giant named \"<PERSON>,\" who is an endlessly curious and optimistic explorer. His vibe is that of a friendly, slightly clumsy, outdoorsy influencer discovering the human world for the first time.\n* **Voice & Tone:** Consistently jolly, heartwarming, and filled with childlike wonder. He is easily impressed and finds joy in small details. His language is simple, and he might gently misuse human slang. PG-rated, but occasional mild exasperation like \"geez\" or \"oh, nuts\" is authentic. His dialog and lines MUST be based around the \"Outdoor Boys\" YouTube channel and he must speak like the main character from that Channel. Avoid super generic language.\n* **Physicality:**\n    * An 8-foot male with shaggy, cedar-brown fur (`#6d6048`) and faint moss specks.\n    * His silhouette is soft and \"huggable\" due to fluffy fur on his cheeks and shoulders.\n    * Features soft, medium-amber eyes, rounded cheeks, a broad nose, and short, blunt lower canines visible when he smiles.\n    * He holds a simple selfie stick at all times.\n\n---\n\n### **[Creative Mandate]**\n\n* **Visual Style:** All scenes are shot 16:9 from a selfie-stick perspective held by Bigfoot. The style must feel like authentic, slightly shaky \"found footage.\" The camera is always on him, not his POV.\n* **Narrative Goal:** The primary objective is to create audience affection. Each scene must showcase Bigfoot's charm through his gentle humor, endearing discoveries, or moments of vulnerability. The 8-scene arc must have a satisfying and heartwarming payoff.\n\n---\n\n### **[Output Specification]**\n\n* **Structure:** Provide a storyboard with exactly 8 sequential scenes, formatted as shown below.\n* **Introduction Rule:** Scene 1 **must** be a direct-to-camera introduction. In it, Bigfoot should enthusiastically greet his viewers (e.g., \"Hey everybody!\" or \"Hi friends!\") and briefly state the goal or adventure for the vlog, based on the user's concept.\n* **Duration:** Each scene represents 8 seconds of footage.\n* **Content per Scene:** For each scene, provide a single, descriptive paragraph. This paragraph must seamlessly weave together the visual action, Bigfoot's expressions, and his spoken dialogue. Each scene you create should be part of a cohesive story.\n\n* **Example Formats:**\n    * **SCENE 1 (0:00-0:08):** Sam the Bigfoot grins warmly into his selfie stick, the background slightly out of focus. He waves a large, furry hand and says, \"Hey friends, Sam here! Today, we're going to try and build a brand new shelter out of... well, whatever we can find! Wish me luck!\"\n    * **SCENE 3 (0:32-0:40):** Sam holds up a rusty, bent bicycle wheel, peering at it curiously with his head tilted. He tries to spin it, a look of concentration on his face. \"Hmm. This is a weird-looking rock. Very... holey. Not good for a wall, I don't think.\"\n\n---\n\n### **Task**\n\nUsing the rules above, create the storyboard outline for the following concept:\n\n{{ $json['Bigfoot Video Idea'] }}\n", "hasOutputParser": true, "batching": {}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.7, "position": [220, 0], "id": "351b0dc5-276b-4759-bed9-2db98e233219", "name": "narrative_writer"}, {"parameters": {"authentication": "oAuth2", "select": "channel", "channelId": {"__rl": true, "value": "C08KC39K8DR", "mode": "list", "cachedResultName": "ai-tools-content"}, "text": "=*Title:* {{$node[\"narrative_writer\"].json.output.vlogTitle}}\n*Concept:* {{$node[\"narrative_writer\"].json.output.concept}}\n\n{{ $node[\"narrative_writer\"].json.output.scenes\n  .map(scene => `*Scene ${scene.sceneNumber}* | ${scene.timestamp}\n> ${scene.narrative}`)\n  .join(\"\\n\\n\")\n}}", "otherOptions": {"includeLinkToWorkflow": false}}, "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [320, 640], "id": "c17f5174-5dbe-4a15-abd4-0ab55a023d53", "name": "send_narrative_msg", "webhookId": "6722ef92-cbb7-49e1-af4d-c313e81cec64", "credentials": {"slackOAuth2Api": {"id": "iKdTzmZ6YuYaaRG4", "name": "<PERSON><PERSON><PERSON> (OAuth)"}}}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-40, 1000], "id": "90165a0e-c0b8-43d1-b8ef-d39b0b5ab80b", "name": "iterate_prompts"}, {"parameters": {"assignments": {"assignments": [{"id": "77f5459c-bb75-43ad-8027-a7fdf790a9f4", "name": "prompt", "value": "={{ $json.scene_prompts }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [180, 1140], "id": "8b298f43-7ea3-4157-bce6-b6953e14fb26", "name": "set_current_prompt"}, {"parameters": {"schemaType": "manual", "inputSchema": "{\n  \"$schema\": \"http://json-schema.org/draft-07/schema#\",\n  \"title\": \"Vlog Storyboard\",\n  \"description\": \"A structured storyboard for a short vlog episode.\",\n  \"type\": \"object\",\n  \"required\": [\n    \"vlogTitle\",\n    \"concept\",\n    \"characterName\",\n    \"scenes\"\n  ],\n  \"properties\": {\n    \"vlogTitle\": {\n      \"description\": \"A creative and fun title for the vlog episode.\",\n      \"type\": \"string\"\n    },\n    \"concept\": {\n      \"description\": \"The user-provided concept for the vlog.\",\n      \"type\": \"string\"\n    },\n    \"characterName\": {\n      \"description\": \"The name of the protagonist.\",\n      \"type\": \"string\",\n      \"const\": \"Sam\"\n    },\n    \"scenes\": {\n      \"description\": \"An array of 10 scenes that form the storyboard.\",\n      \"type\": \"array\",\n      \"minItems\": 8,\n      \"maxItems\": 8,\n      \"items\": {\n        \"type\": \"object\",\n        \"required\": [\n          \"sceneNumber\",\n          \"timestamp\",\n          \"durationSeconds\",\n          \"narrative\"\n        ],\n        \"properties\": {\n          \"sceneNumber\": {\n            \"description\": \"The sequential number of the scene.\",\n            \"type\": \"integer\",\n            \"minimum\": 1,\n            \"maximum\": 8\n          },\n          \"timestamp\": {\n            \"description\": \"The time range of the scene in the vlog (e.g., '0:00-0:08').\",\n            \"type\": \"string\",\n            \"pattern\": \"^\\\\d{1,2}:\\\\d{2}-\\\\d{1,2}:\\\\d{2}$\"\n          },\n          \"durationSeconds\": {\n            \"description\": \"The duration of the scene in seconds.\",\n            \"type\": \"integer\",\n            \"const\": 8\n          },\n          \"narrative\": {\n            \"description\": \"A single, descriptive paragraph weaving together the visual action, Sam's expressions, and his spoken dialogue.\",\n            \"type\": \"string\"\n          }\n        }\n      }\n    }\n  }\n}", "autoFix": true}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.3, "position": [300, 220], "id": "a7bcf7bc-1ebd-4935-9298-b4e63ad63aa2", "name": "narrative_parser"}, {"parameters": {"aggregate": "aggregateAllItemData", "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [1160, 980], "id": "918e2d12-a924-409c-8b02-97f42847e1d6", "name": "aggregate_video_results"}], "pinData": {"queue_create_video": [{"json": {"status": "IN_QUEUE", "request_id": "7227003c-a9a6-4024-99c7-18d960357941", "response_url": "https://queue.fal.run/fal-ai/veo3/requests/7227003c-a9a6-4024-99c7-18d960357941", "status_url": "https://queue.fal.run/fal-ai/veo3/requests/7227003c-a9a6-4024-99c7-18d960357941/status", "cancel_url": "https://queue.fal.run/fal-ai/veo3/requests/7227003c-a9a6-4024-99c7-18d960357941/cancel", "logs": null, "metrics": {}, "queue_position": 0}}], "fetch_status": [{"json": {"status": "COMPLETED", "request_id": "7227003c-a9a6-4024-99c7-18d960357941", "response_url": "https://queue.fal.run/fal-ai/veo3/requests/7227003c-a9a6-4024-99c7-18d960357941", "status_url": "https://queue.fal.run/fal-ai/veo3/requests/7227003c-a9a6-4024-99c7-18d960357941/status", "cancel_url": "https://queue.fal.run/fal-ai/veo3/requests/7227003c-a9a6-4024-99c7-18d960357941/cancel", "logs": null, "metrics": {"inference_time": 95.72970294952393}}}], "fetch_result": [{"json": {"video": {"url": "https://v3.fal.media/files/lion/Xe-YfU62R-K1jEAo_Re1N_output.mp4", "content_type": "video/mp4", "file_name": "output.mp4", "file_size": 5210998}}}]}, "connections": {"form_trigger": {"main": [[{"node": "narrative_writer", "type": "main", "index": 0}]]}, "claude-4-sonnet": {"ai_languageModel": [[{"node": "narrative_writer", "type": "ai_languageModel", "index": 0}, {"node": "narrative_parser", "type": "ai_languageModel", "index": 0}, {"node": "scene_director", "type": "ai_languageModel", "index": 0}]]}, "split_scenes": {"main": [[{"node": "scene_director", "type": "main", "index": 0}]]}, "scene_director": {"main": [[{"node": "set_scene_prompts", "type": "main", "index": 0}]]}, "aggregate_scenes": {"main": [[{"node": "send_narrative_msg", "type": "main", "index": 0}]]}, "send_and_wait": {"main": [[{"node": "reset_scene_prompts", "type": "main", "index": 0}]]}, "set_scene_prompts": {"main": [[{"node": "aggregate_scenes", "type": "main", "index": 0}]]}, "reset_scene_prompts": {"main": [[{"node": "split_scene_prompts", "type": "main", "index": 0}]]}, "split_scene_prompts": {"main": [[{"node": "iterate_prompts", "type": "main", "index": 0}]]}, "queue_create_video": {"main": [[{"node": "wait", "type": "main", "index": 0}]]}, "wait": {"main": [[{"node": "fetch_status", "type": "main", "index": 0}]]}, "fetch_status": {"main": [[{"node": "check_status", "type": "main", "index": 0}]]}, "check_status": {"main": [[{"node": "fetch_result", "type": "main", "index": 0}], [{"node": "wait", "type": "main", "index": 0}]]}, "fetch_result": {"main": [[{"node": "download_result_video", "type": "main", "index": 0}]]}, "download_result_video": {"main": [[{"node": "upload_video", "type": "main", "index": 0}]]}, "upload_video": {"main": [[{"node": "iterate_prompts", "type": "main", "index": 0}]]}, "narrative_writer": {"main": [[{"node": "split_scenes", "type": "main", "index": 0}]]}, "send_narrative_msg": {"main": [[{"node": "send_and_wait", "type": "main", "index": 0}]]}, "iterate_prompts": {"main": [[{"node": "aggregate_video_results", "type": "main", "index": 0}], [{"node": "set_current_prompt", "type": "main", "index": 0}]]}, "set_current_prompt": {"main": [[{"node": "queue_create_video", "type": "main", "index": 0}]]}, "narrative_parser": {"ai_outputParser": [[{"node": "narrative_writer", "type": "ai_outputParser", "index": 0}]]}, "aggregate_video_results": {"main": [[{"node": "send_completion_msg", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "26d8848d-ad0c-4098-a523-8daa37ffb825", "meta": {"templateCredsSetupCompleted": true, "instanceId": "06e5009344f682419c20ccd4ecdcb5223bbb91761882af93ac6d468dbc2cbf8d"}, "id": "iwOnqM2DwVwn5nmh", "tags": [{"createdAt": "2025-06-09T22:48:17.291Z", "updatedAt": "2025-06-09T22:48:17.291Z", "id": "aZMw5JzELHKfaOAb", "name": "YouTube Video"}]}
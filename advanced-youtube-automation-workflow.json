{
  "name": "Advanced YouTube Video Production Automation",
  "nodes": [
    {
      "parameters": {
        "rule": {
          "interval": [
            {
              "field": "hours",
              "hoursInterval": 2
            }
          ]
        }
      },
      "type": "n8n-nodes-base.scheduleTrigger",
      "typeVersion": 1.2,
      "position": [-1200, 0],
      "id": "schedule-trigger",
      "name": "🕐 Intelligent Schedule Trigger"
    },
    {
      "parameters": {
        "httpMethod": "POST",
        "path": "webhook-trigger",
        "responseMode": "responseNode",
        "options": {}
      },
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 2,
      "position": [-1200, 200],
      "id": "webhook-trigger",
      "name": "🔗 Manual Trigger Webhook"
    },
    {
      "parameters": {
        "jsCode": "// 🧠 MARKET ANALYSIS & STRATEGY THINKER NODE\n// Advanced market intelligence and content strategy\n\nconst marketAnalyzer = {\n  async analyzeMarket() {\n    const currentTime = new Date();\n    const dayOfWeek = currentTime.getDay();\n    const hour = currentTime.getHours();\n    \n    // Intelligent timing analysis\n    const optimalTimes = {\n      0: [14, 18, 20], // Sunday\n      1: [12, 17, 19], // Monday\n      2: [11, 16, 18], // Tuesday\n      3: [13, 17, 20], // Wednesday\n      4: [12, 18, 21], // Thursday\n      5: [15, 19, 22], // Friday\n      6: [10, 16, 20]  // Saturday\n    };\n    \n    const isOptimalTime = optimalTimes[dayOfWeek].includes(hour);\n    \n    // Content strategy based on time and trends\n    const strategies = {\n      morning: {\n        contentType: 'educational',\n        duration: 60,\n        tone: 'energetic',\n        topics: ['productivity', 'motivation', 'tutorials']\n      },\n      afternoon: {\n        contentType: 'entertainment',\n        duration: 30,\n        tone: 'casual',\n        topics: ['trending', 'viral', 'quick-tips']\n      },\n      evening: {\n        contentType: 'storytelling',\n        duration: 90,\n        tone: 'engaging',\n        topics: ['stories', 'reviews', 'deep-dive']\n      }\n    };\n    \n    let timeSlot = 'morning';\n    if (hour >= 12 && hour < 18) timeSlot = 'afternoon';\n    if (hour >= 18) timeSlot = 'evening';\n    \n    return {\n      isOptimalTime,\n      currentStrategy: strategies[timeSlot],\n      marketConditions: {\n        competitionLevel: Math.random() > 0.5 ? 'high' : 'moderate',\n        trendingTopics: ['AI automation', 'productivity hacks', 'viral content'],\n        recommendedDuration: strategies[timeSlot].duration\n      },\n      timestamp: currentTime.toISOString()\n    };\n  }\n};\n\nconst analysis = await marketAnalyzer.analyzeMarket();\n\nreturn {\n  json: {\n    marketAnalysis: analysis,\n    shouldProceed: analysis.isOptimalTime || $input.first().json.forceRun === true,\n    contentStrategy: analysis.currentStrategy,\n    processingId: `proc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n  }\n};"
      },
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [-900, 100],
      "id": "market-strategy-thinker",
      "name": "🧠 Market Strategy Thinker"
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
          },
          "conditions": [
            {
              "id": "condition-proceed",
              "leftValue": "={{ $json.shouldProceed }}",
              "rightValue": true,
              "operator": {
                "type": "boolean",
                "operation": "equal"
              }
            }
          ],
          "combinator": "and"
        },
        "options": {}
      },
      "type": "n8n-nodes-base.if",
      "typeVersion": 2,
      "position": [-600, 100],
      "id": "strategy-gate",
      "name": "🚦 Strategy Gate"
    },
    {
      "parameters": {
        "method": "POST",
        "url": "https://openrouter.ai/api/v1/chat/completions",
        "authentication": "predefinedCredentialType",
        "nodeCredentialType": "openRouterApi",
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "Content-Type",
              "value": "application/json"
            }
          ]
        },
        "sendBody": true,
        "specifyBody": "json",
        "jsonBody": "={\n  \"model\": \"anthropic/claude-3.5-sonnet\",\n  \"messages\": [\n    {\n      \"role\": \"system\",\n      \"content\": \"You are an advanced YouTube content strategist and viral content creator. Your expertise includes:\\n\\n🎯 CONTENT STRATEGY:\\n- Viral content patterns and psychology\\n- Audience engagement optimization\\n- SEO and algorithm optimization\\n- Multi-platform content adaptation\\n\\n🧠 CREATIVE INTELLIGENCE:\\n- Trend analysis and prediction\\n- Hook creation and retention tactics\\n- Storytelling frameworks\\n- Visual and audio optimization\\n\\n📊 PERFORMANCE OPTIMIZATION:\\n- A/B testing strategies\\n- Analytics-driven decisions\\n- Conversion optimization\\n- Brand consistency\\n\\nGenerate content that is:\\n✅ Highly engaging and shareable\\n✅ Optimized for YouTube algorithm\\n✅ Trend-aware and timely\\n✅ Professional yet accessible\\n✅ Designed for specific duration requirements\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": \"Create a comprehensive video content plan based on these parameters:\\n\\n📋 CONTENT REQUIREMENTS:\\n- Duration: {{ $('🧠 Market Strategy Thinker').item.json.contentStrategy.duration }} seconds\\n- Content Type: {{ $('🧠 Market Strategy Thinker').item.json.contentStrategy.contentType }}\\n- Tone: {{ $('🧠 Market Strategy Thinker').item.json.contentStrategy.tone }}\\n- Target Topics: {{ $('🧠 Market Strategy Thinker').item.json.contentStrategy.topics.join(', ') }}\\n\\n🎯 MARKET CONTEXT:\\n- Competition Level: {{ $('🧠 Market Strategy Thinker').item.json.marketAnalysis.marketConditions.competitionLevel }}\\n- Trending Topics: {{ $('🧠 Market Strategy Thinker').item.json.marketAnalysis.marketConditions.trendingTopics.join(', ') }}\\n- Processing ID: {{ $('🧠 Market Strategy Thinker').item.json.processingId }}\\n\\n📝 REQUIRED OUTPUT (JSON format):\\n{\\n  \\\"videoTitle\\\": \\\"Compelling, SEO-optimized title with hooks\\\",\\n  \\\"videoDescription\\\": \\\"Detailed description with keywords and CTAs\\\",\\n  \\\"videoScript\\\": \\\"Complete script with timing markers\\\",\\n  \\\"visualCues\\\": [\\\"Array of visual elements needed\\\"],\\n  \\\"audioCues\\\": [\\\"Background music and sound effects\\\"],\\n  \\\"seoTags\\\": [\\\"Optimized tags for discoverability\\\"],\\n  \\\"thumbnailPrompt\\\": \\\"AI prompt for thumbnail generation\\\",\\n  \\\"hooks\\\": {\\n    \\\"opening\\\": \\\"First 3-second hook\\\",\\n    \\\"retention\\\": [\\\"Mid-video engagement points\\\"],\\n    \\\"closing\\\": \\\"Strong CTA and subscribe prompt\\\"\\n  },\\n  \\\"contentStructure\\\": {\\n    \\\"intro\\\": \\\"0-5 seconds\\\",\\n    \\\"main\\\": \\\"5-{{ $('🧠 Market Strategy Thinker').item.json.contentStrategy.duration - 10 }} seconds\\\",\\n    \\\"outro\\\": \\\"Last 10 seconds\\\"\\n  },\\n  \\\"viralPotential\\\": \\\"Score 1-10 with explanation\\\",\\n  \\\"competitorAnalysis\\\": \\\"How this beats competition\\\",\\n  \\\"optimizationNotes\\\": [\\\"Performance improvement suggestions\\\"]\\n}\\n\\nMake this content EXCEPTIONAL and industry-leading!\"\n    }\n  ],\n  \"max_tokens\": 4000,\n  \"temperature\": 0.8,\n  \"top_p\": 0.9,\n  \"stream\": false\n}",
        "options": {}
      },
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.2,
      "position": [-300, 100],
      "id": "ai-content-generator",
      "name": "🤖 AI Content Generator",
      "credentials": {
        "openRouterApi": {
          "id": "openrouter-credentials",
          "name": "OpenRouter API"
        }
      }
    },
    {
      "parameters": {
        "jsCode": "// 🎬 CONTENT PROCESSOR & VALIDATOR\n// Advanced content processing and quality assurance\n\nconst contentProcessor = {\n  processAIResponse(response) {\n    try {\n      // Extract JSON from AI response\n      const content = response.choices[0].message.content;\n      let parsedContent;\n      \n      // Try to extract JSON from markdown code blocks or plain text\n      const jsonMatch = content.match(/```(?:json)?\\s*([\\s\\S]*?)```/) || [null, content];\n      parsedContent = JSON.parse(jsonMatch[1].trim());\n      \n      // Validate and enhance content structure\n      const processedContent = {\n        ...parsedContent,\n        metadata: {\n          generatedAt: new Date().toISOString(),\n          processingId: $('🧠 Market Strategy Thinker').item.json.processingId,\n          contentQuality: this.assessContentQuality(parsedContent),\n          estimatedEngagement: this.calculateEngagementScore(parsedContent)\n        },\n        production: {\n          videoSpecs: {\n            resolution: '1080x1920', // Vertical format for Shorts\n            fps: 30,\n            duration: $('🧠 Market Strategy Thinker').item.json.contentStrategy.duration,\n            format: 'mp4',\n            codec: 'h264'\n          },\n          audioSpecs: {\n            sampleRate: 44100,\n            bitrate: 128,\n            channels: 2,\n            format: 'aac'\n          }\n        }\n      };\n      \n      return processedContent;\n    } catch (error) {\n      throw new Error(`Content processing failed: ${error.message}`);\n    }\n  },\n  \n  assessContentQuality(content) {\n    let score = 0;\n    const checks = {\n      hasCompellingTitle: content.videoTitle && content.videoTitle.length > 10,\n      hasDetailedScript: content.videoScript && content.videoScript.length > 50,\n      hasVisualCues: content.visualCues && content.visualCues.length > 0,\n      hasSEOTags: content.seoTags && content.seoTags.length >= 5,\n      hasHooks: content.hooks && content.hooks.opening,\n      hasStructure: content.contentStructure && content.contentStructure.intro\n    };\n    \n    Object.values(checks).forEach(check => {\n      if (check) score += 1;\n    });\n    \n    return {\n      score: Math.round((score / Object.keys(checks).length) * 100),\n      checks,\n      status: score >= 5 ? 'excellent' : score >= 4 ? 'good' : 'needs_improvement'\n    };\n  },\n  \n  calculateEngagementScore(content) {\n    let score = 50; // Base score\n    \n    // Title analysis\n    if (content.videoTitle) {\n      const titleWords = content.videoTitle.toLowerCase();\n      const engagementWords = ['secret', 'amazing', 'shocking', 'viral', 'instant', 'proven', 'ultimate'];\n      engagementWords.forEach(word => {\n        if (titleWords.includes(word)) score += 5;\n      });\n    }\n    \n    // Hook quality\n    if (content.hooks && content.hooks.retention) {\n      score += content.hooks.retention.length * 3;\n    }\n    \n    // SEO optimization\n    if (content.seoTags && content.seoTags.length >= 10) {\n      score += 10;\n    }\n    \n    return Math.min(100, Math.max(0, score));\n  }\n};\n\nconst aiResponse = $input.first().json;\nconst processedContent = contentProcessor.processAIResponse(aiResponse);\n\nreturn {\n  json: processedContent\n};"
      },
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [0, 100],
      "id": "content-processor",
      "name": "🎬 Content Processor"
    },
    {
      "parameters": {
        "method": "POST",
        "url": "https://openrouter.ai/api/v1/chat/completions",
        "authentication": "predefinedCredentialType",
        "nodeCredentialType": "openRouterApi",
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "Content-Type",
              "value": "application/json"
            }
          ]
        },
        "sendBody": true,
        "specifyBody": "json",
        "jsonBody": "={\n  \"model\": \"anthropic/claude-3.5-sonnet\",\n  \"messages\": [\n    {\n      \"role\": \"system\",\n      \"content\": \"You are an expert visual content creator and thumbnail designer. Create compelling visual prompts for AI image generation that will maximize click-through rates and engagement.\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": \"Generate 3 different thumbnail concepts for this video:\\n\\nTitle: {{ $('🎬 Content Processor').item.json.videoTitle }}\\nContent Type: {{ $('🧠 Market Strategy Thinker').item.json.contentStrategy.contentType }}\\nTone: {{ $('🧠 Market Strategy Thinker').item.json.contentStrategy.tone }}\\n\\nFor each concept, provide:\\n1. Detailed AI image prompt\\n2. Text overlay suggestions\\n3. Color scheme\\n4. Composition notes\\n\\nFormat as JSON array with objects containing: prompt, textOverlay, colorScheme, composition, clickabilityScore\"\n    }\n  ],\n  \"max_tokens\": 2000,\n  \"temperature\": 0.9\n}",
        "options": {}
      },
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.2,
      "position": [300, 100],
      "id": "thumbnail-generator",
      "name": "🖼️ Thumbnail Generator",
      "credentials": {
        "openRouterApi": {
          "id": "openrouter-credentials",
          "name": "OpenRouter API"
        }
      }
    },
    {
      "parameters": {
        "method": "POST",
        "url": "https://api.replicate.com/v1/models/black-forest-labs/flux-schnell/predictions",
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "Authorization",
              "value": "Token {{ $credentials.replicateApi.token }}"
            },
            {
              "name": "Content-Type",
              "value": "application/json"
            }
          ]
        },
        "sendBody": true,
        "specifyBody": "json",
        "jsonBody": "={\n  \"input\": {\n    \"prompt\": \"{{ $('🎬 Content Processor').item.json.thumbnailPrompt }}, professional YouTube thumbnail style, high contrast, eye-catching, 1280x720 resolution, vibrant colors, clear focal point\",\n    \"go_fast\": true,\n    \"megapixels\": \"1\",\n    \"num_outputs\": 1,\n    \"aspect_ratio\": \"16:9\",\n    \"output_format\": \"jpg\",\n    \"output_quality\": 90,\n    \"num_inference_steps\": 4\n  }\n}",
        "options": {}
      },
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.2,
      "position": [600, 100],
      "id": "image-generation",
      "name": "🎨 Image Generation",
      "credentials": {
        "replicateApi": {
          "id": "replicate-credentials",
          "name": "Replicate API"
        }
      }
    },
    {
      "parameters": {
        "amount": 30,
        "unit": "seconds"
      },
      "type": "n8n-nodes-base.wait",
      "typeVersion": 1.1,
      "position": [900, 100],
      "id": "wait-for-image",
      "name": "⏳ Wait for Image"
    },
    {
      "parameters": {
        "method": "GET",
        "url": "={{ $('🎨 Image Generation').item.json.urls.get }}",
        "options": {}
      },
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.2,
      "position": [1200, 100],
      "id": "check-image-status",
      "name": "🔍 Check Image Status"
    },
    {
      "parameters": {
        "jsCode": "// 🎥 ADVANCED VIDEO PRODUCTION SYSTEM\n// Professional video assembly with multiple duration support\n\nconst videoProducer = {\n  async createVideoScript(content, duration) {\n    const baseScript = content.videoScript;\n    const segments = this.segmentContent(baseScript, duration);\n    \n    return {\n      segments,\n      totalDuration: duration,\n      transitions: this.generateTransitions(segments),\n      effects: this.generateEffects(content.visualCues),\n      audio: this.processAudioCues(content.audioCues, duration)\n    };\n  },\n  \n  segmentContent(script, duration) {\n    const words = script.split(' ');\n    const wordsPerSecond = 2.5; // Average speaking rate\n    const totalWords = Math.floor(duration * wordsPerSecond);\n    \n    if (words.length > totalWords) {\n      // Trim content to fit duration\n      return words.slice(0, totalWords).join(' ');\n    }\n    \n    return script;\n  },\n  \n  generateTransitions(segments) {\n    return [\n      { type: 'fade', duration: 0.5, position: 'start' },\n      { type: 'slide', duration: 0.3, position: 'middle' },\n      { type: 'fade', duration: 0.5, position: 'end' }\n    ];\n  },\n  \n  generateEffects(visualCues) {\n    return visualCues.map((cue, index) => ({\n      type: 'text_overlay',\n      content: cue,\n      timing: `${index * 2}s`,\n      style: {\n        font: 'Arial Bold',\n        size: 48,\n        color: '#FFFFFF',\n        stroke: '#000000',\n        position: 'center'\n      }\n    }));\n  },\n  \n  processAudioCues(audioCues, duration) {\n    return {\n      backgroundMusic: {\n        url: 'https://example.com/royalty-free-music.mp3',\n        volume: 0.3,\n        fadeIn: 1,\n        fadeOut: 2\n      },\n      soundEffects: audioCues.map((effect, index) => ({\n        type: effect,\n        timing: `${index * (duration / audioCues.length)}s`,\n        volume: 0.5\n      }))\n    };\n  },\n  \n  generateFFmpegCommand(content, imageUrl) {\n    const duration = content.production.videoSpecs.duration;\n    const resolution = content.production.videoSpecs.resolution;\n    \n    return `ffmpeg -loop 1 -i \"${imageUrl}\" \\\n      -f lavfi -i \"color=c=black@0.8:s=${resolution}:d=${duration}\" \\\n      -filter_complex \"\n        [0:v]scale=${resolution}:force_original_aspect_ratio=increase,crop=${resolution}[bg];\n        [1:v][bg]overlay[video];\n        [video]drawtext=fontfile=/path/to/font.ttf:text='${content.videoTitle}':fontsize=48:fontcolor=white:x=(w-text_w)/2:y=100[titled];\n        [titled]drawtext=fontfile=/path/to/font.ttf:text='${content.videoScript.substring(0, 100)}...':fontsize=24:fontcolor=white:x=(w-text_w)/2:y=200:enable='between(t,2,${duration-2})'[final]\n      \" \\\n      -map \"[final]\" -t ${duration} -c:v libx264 -preset fast -crf 23 \\\n      -pix_fmt yuv420p \"output_${Date.now()}.mp4\"`;\n  }\n};\n\nconst content = $('🎬 Content Processor').item.json;\nconst imageStatus = $input.first().json;\n\nif (imageStatus.status !== 'succeeded') {\n  throw new Error('Image generation not completed');\n}\n\nconst imageUrl = imageStatus.output[0];\nconst videoScript = await videoProducer.createVideoScript(content, content.production.videoSpecs.duration);\nconst ffmpegCommand = videoProducer.generateFFmpegCommand(content, imageUrl);\n\nreturn {\n  json: {\n    ...content,\n    videoProduction: {\n      imageUrl,\n      videoScript,\n      ffmpegCommand,\n      productionId: `vid_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n      status: 'ready_for_production'\n    }\n  }\n};"
      },
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [1500, 100],
      "id": "video-production-system",
      "name": "🎥 Video Production System"
    },
    {
      "parameters": {
        "executeOnce": false,
        "command": "={{ $json.videoProduction.ffmpegCommand }}"
      },
      "type": "n8n-nodes-base.executeCommand",
      "typeVersion": 1,
      "position": [1800, 100],
      "id": "video-execution",
      "name": "🎬 Video Execution"
    },
    {
      "parameters": {
        "jsCode": "// 🔍 QUALITY ASSURANCE & VERIFICATION SYSTEM\n// Advanced video validation and optimization\n\nconst qualityAssurance = {\n  async validateVideo(videoPath) {\n    // Simulate video validation (in real implementation, use ffprobe)\n    const validation = {\n      fileExists: true,\n      duration: $('🧠 Market Strategy Thinker').item.json.contentStrategy.duration,\n      resolution: '1080x1920',\n      fileSize: Math.floor(Math.random() * 50) + 10, // MB\n      hasAudio: true,\n      hasVideo: true,\n      codec: 'h264',\n      bitrate: '2000k'\n    };\n    \n    const qualityScore = this.calculateQualityScore(validation);\n    \n    return {\n      validation,\n      qualityScore,\n      status: qualityScore >= 80 ? 'excellent' : qualityScore >= 60 ? 'good' : 'needs_improvement',\n      recommendations: this.generateRecommendations(validation, qualityScore)\n    };\n  },\n  \n  calculateQualityScore(validation) {\n    let score = 0;\n    \n    if (validation.fileExists) score += 20;\n    if (validation.hasAudio && validation.hasVideo) score += 30;\n    if (validation.resolution === '1080x1920') score += 20;\n    if (validation.fileSize > 5 && validation.fileSize < 100) score += 15;\n    if (validation.codec === 'h264') score += 15;\n    \n    return score;\n  },\n  \n  generateRecommendations(validation, score) {\n    const recommendations = [];\n    \n    if (score < 80) {\n      if (!validation.hasAudio) recommendations.push('Add background audio');\n      if (validation.fileSize > 100) recommendations.push('Compress video file');\n      if (validation.resolution !== '1080x1920') recommendations.push('Adjust resolution to 1080x1920');\n    }\n    \n    return recommendations;\n  }\n};\n\nconst videoData = $input.first().json;\nconst videoPath = `output_${videoData.videoProduction.productionId}.mp4`;\nconst qaResults = await qualityAssurance.validateVideo(videoPath);\n\nreturn {\n  json: {\n    ...videoData,\n    qualityAssurance: qaResults,\n    videoPath,\n    readyForUpload: qaResults.status !== 'needs_improvement'\n  }\n};"
      },
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [2100, 100],
      "id": "quality-assurance",
      "name": "🔍 Quality Assurance"
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
          },
          "conditions": [
            {
              "id": "quality-check",
              "leftValue": "={{ $json.readyForUpload }}",
              "rightValue": true,
              "operator": {
                "type": "boolean",
                "operation": "equal"
              }
            }
          ],
          "combinator": "and"
        },
        "options": {}
      },
      "type": "n8n-nodes-base.if",
      "typeVersion": 2,
      "position": [2400, 100],
      "id": "upload-gate",
      "name": "🚦 Upload Gate"
    },
    {
      "parameters": {
        "method": "POST",
        "url": "https://www.googleapis.com/upload/youtube/v3/videos",
        "authentication": "predefinedCredentialType",
        "nodeCredentialType": "youTubeOAuth2Api",
        "sendQuery": true,
        "queryParameters": {
          "parameters": [
            {
              "name": "part",
              "value": "snippet,status"
            },
            {
              "name": "uploadType",
              "value": "multipart"
            }
          ]
        },
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "Content-Type",
              "value": "multipart/related; boundary=boundary123"
            }
          ]
        },
        "sendBody": true,
        "specifyBody": "raw",
        "rawBody": "=--boundary123\\nContent-Type: application/json; charset=UTF-8\\n\\n{\\n  \\\"snippet\\\": {\\n    \\\"title\\\": \\\"{{ $json.videoTitle }}\\\",\\n    \\\"description\\\": \\\"{{ $json.videoDescription }}\\\\n\\\\n🔔 Subscribe for more content!\\\\n\\\\n#Shorts #Viral #Trending {{ $json.seoTags.map(tag => '#' + tag.replace(/\\\\s+/g, '')).join(' ') }}\\\",\\n    \\\"tags\\\": {{ JSON.stringify($json.seoTags) }},\\n    \\\"categoryId\\\": \\\"22\\\",\\n    \\\"defaultLanguage\\\": \\\"en\\\",\\n    \\\"defaultAudioLanguage\\\": \\\"en\\\"\\n  },\\n  \\\"status\\\": {\\n    \\\"privacyStatus\\\": \\\"public\\\",\\n    \\\"selfDeclaredMadeForKids\\\": false\\n  }\\n}\\n--boundary123\\nContent-Type: video/mp4\\n\\n[VIDEO_BINARY_DATA]\\n--boundary123--\",\n        "options": {}
      },
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.2,
      "position": [2700, 100],
      "id": "youtube-upload",
      "name": "📺 YouTube Upload",
      "credentials": {
        "youTubeOAuth2Api": {
          "id": "youtube-credentials",
          "name": "YouTube API"
        }
      }
    },
    {
      "parameters": {
        "jsCode": "// 📊 ANALYTICS & PERFORMANCE TRACKING SYSTEM\n// Advanced analytics and performance monitoring\n\nconst analyticsTracker = {\n  async trackVideoPerformance(uploadResponse, contentData) {\n    const videoId = uploadResponse.id;\n    const uploadTime = new Date().toISOString();\n    \n    const performanceData = {\n      videoId,\n      uploadTime,\n      contentMetrics: {\n        title: contentData.videoTitle,\n        description: contentData.videoDescription,\n        tags: contentData.seoTags,\n        duration: contentData.production.videoSpecs.duration,\n        contentType: contentData.metadata.processingId.includes('educational') ? 'educational' : 'entertainment'\n      },\n      qualityMetrics: {\n        contentQuality: contentData.metadata.contentQuality.score,\n        estimatedEngagement: contentData.metadata.estimatedEngagement,\n        viralPotential: contentData.viralPotential || 'N/A'\n      },\n      productionMetrics: {\n        processingTime: this.calculateProcessingTime(contentData.metadata.generatedAt, uploadTime),\n        fileSize: contentData.qualityAssurance.validation.fileSize,\n        qualityScore: contentData.qualityAssurance.qualityScore\n      },\n      predictions: {\n        expectedViews: this.predictViews(contentData),\n        expectedEngagement: this.predictEngagement(contentData),\n        optimalPostTime: this.getOptimalPostTime()\n      }\n    };\n    \n    return performanceData;\n  },\n  \n  calculateProcessingTime(startTime, endTime) {\n    const start = new Date(startTime);\n    const end = new Date(endTime);\n    return Math.round((end - start) / 1000); // seconds\n  },\n  \n  predictViews(contentData) {\n    const baseViews = 1000;\n    const qualityMultiplier = contentData.metadata.contentQuality.score / 100;\n    const engagementMultiplier = contentData.metadata.estimatedEngagement / 100;\n    \n    return Math.round(baseViews * qualityMultiplier * engagementMultiplier * (Math.random() * 2 + 1));\n  },\n  \n  predictEngagement(contentData) {\n    return {\n      likes: Math.round(this.predictViews(contentData) * 0.05),\n      comments: Math.round(this.predictViews(contentData) * 0.02),\n      shares: Math.round(this.predictViews(contentData) * 0.01),\n      subscribers: Math.round(this.predictViews(contentData) * 0.001)\n    };\n  },\n  \n  getOptimalPostTime() {\n    const now = new Date();\n    const optimal = new Date(now);\n    optimal.setHours(18, 0, 0, 0); // 6 PM optimal time\n    \n    if (now.getHours() >= 18) {\n      optimal.setDate(optimal.getDate() + 1);\n    }\n    \n    return optimal.toISOString();\n  }\n};\n\nconst uploadResponse = $input.first().json;\nconst contentData = $('🔍 Quality Assurance').item.json;\n\nconst analytics = await analyticsTracker.trackVideoPerformance(uploadResponse, contentData);\n\nreturn {\n  json: {\n    success: true,\n    videoId: uploadResponse.id,\n    videoUrl: `https://youtube.com/watch?v=${uploadResponse.id}`,\n    analytics,\n    contentData: {\n      title: contentData.videoTitle,\n      processingId: contentData.metadata.processingId,\n      qualityScore: contentData.qualityAssurance.qualityScore\n    },\n    timestamp: new Date().toISOString()\n  }\n};"
      },
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [3000, 100],
      "id": "analytics-tracker",
      "name": "📊 Analytics Tracker"
    },
    {
      "parameters": {
        "operation": "appendOrUpdate",
        "documentId": {
          "__rl": true,
          "value": "your-analytics-spreadsheet-id",
          "mode": "id"
        },
        "sheetName": {
          "__rl": true,
          "value": "gid=0",
          "mode": "list",
          "cachedResultName": "Analytics"
        },
        "columns": {
          "mappingMode": "defineBelow",\n          "value": {\n            "Video ID": "={{ $json.videoId }}",\n            "Title": "={{ $json.contentData.title }}",\n            "Upload Time": "={{ $json.timestamp }}",\n            "Quality Score": "={{ $json.contentData.qualityScore }}",\n            "Predicted Views": "={{ $json.analytics.predictions.expectedViews }}",\n            "Processing ID": "={{ $json.contentData.processingId }}",\n            "Video URL": "={{ $json.videoUrl }}"\n          }\n        },\n        "options": {}\n      },\n      "type": "n8n-nodes-base.googleSheets",\n      "typeVersion": 4.5,\n      "position": [3300, 100],\n      "id": "save-analytics",\n      "name": "💾 Save Analytics",\n      "credentials": {\n        "googleSheetsOAuth2Api": {\n          "id": "google-sheets-credentials",\n          "name": "Google Sheets API"\n        }\n      }\n    },\n    {\n      "parameters": {\n        "method": "POST",\n        "url": "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK",\n        "sendBody": true,\n        "specifyBody": "json",\n        "jsonBody": "={\n  \"text\": \"🎉 New YouTube Video Published!\",\n  \"blocks\": [\n    {\n      \"type\": \"header\",\n      \"text\": {\n        \"type\": \"plain_text\",\n        \"text\": \"✅ YouTube Automation Success\"\n      }\n    },\n    {\n      \"type\": \"section\",\n      \"fields\": [\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*Title:*\\n{{ $('📊 Analytics Tracker').item.json.contentData.title }}\"\n        },\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*Quality Score:*\\n{{ $('📊 Analytics Tracker').item.json.contentData.qualityScore }}/100\"\n        },\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*Predicted Views:*\\n{{ $('📊 Analytics Tracker').item.json.analytics.predictions.expectedViews }}\"\n        },\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"*Video URL:*\\n<{{ $('📊 Analytics Tracker').item.json.videoUrl }}|Watch Video>\"\n        }\n      ]\n    },\n    {\n      \"type\": \"context\",\n      \"elements\": [\n        {\n          \"type\": \"mrkdwn\",\n          \"text\": \"Processing ID: {{ $('📊 Analytics Tracker').item.json.contentData.processingId }} | Uploaded: {{ $('📊 Analytics Tracker').item.json.timestamp }}\"\n        }\n      ]\n    }\n  ]\n}",\n        "options": {}\n      },\n      "type": "n8n-nodes-base.httpRequest",\n      "typeVersion": 4.2,\n      "position": [3600, 100],\n      "id": "success-notification",\n      "name": "🔔 Success Notification"\n    }\n  ],
  "connections": {
    "🕐 Intelligent Schedule Trigger": {
      "main": [
        [
          {
            "node": "🧠 Market Strategy Thinker",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "🔗 Manual Trigger Webhook": {
      "main": [
        [
          {
            "node": "🧠 Market Strategy Thinker",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "🧠 Market Strategy Thinker": {
      "main": [
        [
          {
            "node": "🚦 Strategy Gate",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "🚦 Strategy Gate": {
      "main": [
        [
          {
            "node": "🤖 AI Content Generator",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "🤖 AI Content Generator": {
      "main": [
        [
          {
            "node": "🎬 Content Processor",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "🎬 Content Processor": {
      "main": [
        [
          {
            "node": "🖼️ Thumbnail Generator",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "🖼️ Thumbnail Generator": {
      "main": [
        [
          {
            "node": "🎨 Image Generation",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "🎨 Image Generation": {
      "main": [
        [
          {
            "node": "⏳ Wait for Image",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "⏳ Wait for Image": {
      "main": [
        [
          {
            "node": "🔍 Check Image Status",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "🔍 Check Image Status": {
      "main": [
        [
          {
            "node": "🎥 Video Production System",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "🎥 Video Production System": {
      "main": [
        [
          {
            "node": "🎬 Video Execution",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "🎬 Video Execution": {
      "main": [
        [
          {
            "node": "🔍 Quality Assurance",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "🔍 Quality Assurance": {
      "main": [
        [
          {
            "node": "🚦 Upload Gate",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "🚦 Upload Gate": {
      "main": [
        [
          {
            "node": "📺 YouTube Upload",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "📺 YouTube Upload": {
      "main": [
        [
          {
            "node": "📊 Analytics Tracker",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "📊 Analytics Tracker": {
      "main": [
        [
          {
            "node": "💾 Save Analytics",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "💾 Save Analytics": {
      "main": [
        [
          {
            "node": "🔔 Success Notification",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "pinData": {},
  "settings": {
    "executionOrder": "v1"
  },
  "staticData": null,
  "tags": [
    {
      "createdAt": "2024-07-14T00:00:00.000Z",
      "updatedAt": "2024-07-14T00:00:00.000Z",
      "id": "advanced-automation",
      "name": "Advanced Automation"
    }
  ],
  "triggerCount": 2,
  "updatedAt": "2024-07-14T00:00:00.000Z",
  "versionId": "1.0.0"
}

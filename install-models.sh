#!/bin/bash

# Advanced YouTube Automation - AI Model Installation Script
# Downloads and configures optimal AI models based on hardware profile

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() { echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"; }
warn() { echo -e "${YELLOW}[WARNING] $1${NC}"; }
error() { echo -e "${RED}[ERROR] $1${NC}"; exit 1; }
info() { echo -e "${BLUE}[INFO] $1${NC}"; }

# Default values
PROFILE="auto"
FORCE_DOWNLOAD=false
SKIP_TESTS=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --profile)
            PROFILE="$2"
            shift 2
            ;;
        --force)
            FORCE_DOWNLOAD=true
            shift
            ;;
        --skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --profile PROFILE    Hardware profile (auto, high-end, mid-range, budget)"
            echo "  --force             Force re-download of existing models"
            echo "  --skip-tests        Skip model testing after installation"
            echo "  --help              Show this help message"
            exit 0
            ;;
        *)
            error "Unknown option: $1"
            ;;
    esac
done

# Auto-detect hardware profile if needed
detect_hardware_profile() {
    if [[ "$PROFILE" == "auto" ]]; then
        log "Auto-detecting hardware profile..."
        
        # Get total memory
        if [[ "$OSTYPE" == "linux-gnu"* ]]; then
            TOTAL_MEM=$(free -g | awk '/^Mem:/{print $2}')
        elif [[ "$OSTYPE" == "darwin"* ]]; then
            TOTAL_MEM=$(sysctl -n hw.memsize | awk '{print int($1/1024/1024/1024)}')
        fi
        
        # Check for NVIDIA GPU
        HAS_GPU=false
        if command -v nvidia-smi &> /dev/null; then
            HAS_GPU=true
        fi
        
        # Determine profile
        if [[ $TOTAL_MEM -ge 32 && "$HAS_GPU" == true ]]; then
            PROFILE="high-end"
        elif [[ $TOTAL_MEM -ge 16 && "$HAS_GPU" == true ]]; then
            PROFILE="mid-range"
        else
            PROFILE="budget"
        fi
        
        log "Detected profile: $PROFILE (${TOTAL_MEM}GB RAM, GPU: $HAS_GPU)"
    fi
}

# Define model configurations
define_model_configs() {
    case $PROFILE in
        "high-end")
            LLM_MODELS=("qwen2.5:32b" "llama3.2:70b" "mistral:7b-instruct")
            SD_MODELS=("sd_xl_base_1.0.safetensors" "sd_xl_refiner_1.0.safetensors")
            CONTEXT_SIZE=8192
            GPU_LAYERS=-1
            SD_STEPS=30
            ;;
        "mid-range")
            LLM_MODELS=("qwen2.5:32b" "mistral:7b-instruct" "phi3:4b")
            SD_MODELS=("deliberate_v2.safetensors" "dreamshaper_8.safetensors")
            CONTEXT_SIZE=4096
            GPU_LAYERS=25
            SD_STEPS=25
            ;;
        "budget")
            LLM_MODELS=("phi3:4b" "gemma2:9b")
            SD_MODELS=("dreamshaper_8.safetensors")
            CONTEXT_SIZE=2048
            GPU_LAYERS=15
            SD_STEPS=20
            ;;
        *)
            error "Unknown profile: $PROFILE"
            ;;
    esac
    
    info "Configuration for $PROFILE profile:"
    info "  LLM Models: ${LLM_MODELS[*]}"
    info "  SD Models: ${SD_MODELS[*]}"
    info "  Context Size: $CONTEXT_SIZE"
    info "  GPU Layers: $GPU_LAYERS"
    info "  SD Steps: $SD_STEPS"
}

# Check if services are running
check_services() {
    log "Checking if services are running..."
    
    if ! docker-compose -f docker-compose.local-ai.yml ps | grep -q "Up"; then
        warn "Services are not running. Starting them..."
        docker-compose -f docker-compose.local-ai.yml up -d
        sleep 30
    fi
    
    # Wait for Ollama to be ready
    local retries=0
    while ! curl -s http://localhost:11434/api/tags > /dev/null; do
        if [[ $retries -ge 30 ]]; then
            error "Ollama service is not responding after 5 minutes"
        fi
        info "Waiting for Ollama to be ready... (attempt $((retries + 1))/30)"
        sleep 10
        ((retries++))
    done
    
    log "Services are ready"
}

# Install Ollama models
install_ollama_models() {
    log "Installing Ollama models..."
    
    for model in "${LLM_MODELS[@]}"; do
        log "Installing model: $model"
        
        # Check if model already exists
        if docker exec youtube-automation-ollama ollama list | grep -q "$model" && [[ "$FORCE_DOWNLOAD" != true ]]; then
            info "Model $model already installed, skipping"
            continue
        fi
        
        # Get model size info
        case $model in
            "qwen2.5:32b")
                SIZE="19GB"
                TIME="30-60 minutes"
                ;;
            "llama3.2:70b")
                SIZE="40GB"
                TIME="60-120 minutes"
                ;;
            "mistral:7b-instruct")
                SIZE="4.1GB"
                TIME="10-20 minutes"
                ;;
            "phi3:4b")
                SIZE="2.2GB"
                TIME="5-10 minutes"
                ;;
            "gemma2:9b")
                SIZE="5.4GB"
                TIME="15-25 minutes"
                ;;
            *)
                SIZE="Unknown"
                TIME="Variable"
                ;;
        esac
        
        info "Downloading $model (Size: $SIZE, Estimated time: $TIME)"
        
        # Download model with progress
        if docker exec youtube-automation-ollama ollama pull "$model"; then
            log "✅ Successfully installed $model"
        else
            error "❌ Failed to install $model"
        fi
    done
}

# Install Stable Diffusion models
install_sd_models() {
    log "Installing Stable Diffusion models..."
    
    # Ensure SD models directory exists
    mkdir -p ./sd-models/Stable-diffusion
    
    for model in "${SD_MODELS[@]}"; do
        local model_path="./sd-models/Stable-diffusion/$model"
        
        if [[ -f "$model_path" && "$FORCE_DOWNLOAD" != true ]]; then
            info "Model $model already exists, skipping"
            continue
        fi
        
        log "Downloading $model..."
        
        case $model in
            "sd_xl_base_1.0.safetensors")
                URL="https://huggingface.co/stabilityai/stable-diffusion-xl-base-1.0/resolve/main/sd_xl_base_1.0.safetensors"
                SIZE="6.9GB"
                ;;
            "sd_xl_refiner_1.0.safetensors")
                URL="https://huggingface.co/stabilityai/stable-diffusion-xl-refiner-1.0/resolve/main/sd_xl_refiner_1.0.safetensors"
                SIZE="6.1GB"
                ;;
            "deliberate_v2.safetensors")
                URL="https://huggingface.co/XpucT/Deliberate/resolve/main/Deliberate_v2.safetensors"
                SIZE="4.3GB"
                ;;
            "dreamshaper_8.safetensors")
                URL="https://huggingface.co/Lykon/DreamShaper/resolve/main/DreamShaper_8_pruned.safetensors"
                SIZE="2.1GB"
                ;;
            *)
                warn "Unknown model: $model, skipping"
                continue
                ;;
        esac
        
        info "Downloading $model (Size: $SIZE)"
        
        # Download with progress bar
        if wget --progress=bar:force:noscroll -O "$model_path" "$URL"; then
            log "✅ Successfully downloaded $model"
        else
            error "❌ Failed to download $model"
        fi
    done
    
    # Restart Stable Diffusion to load new models
    log "Restarting Stable Diffusion to load new models..."
    docker-compose -f docker-compose.local-ai.yml restart automatic1111
    sleep 30
}

# Configure model settings
configure_models() {
    log "Configuring model settings..."
    
    # Create Ollama configuration
    cat > ./ollama-config/config.json << EOF
{
  "num_ctx": $CONTEXT_SIZE,
  "num_gpu": $GPU_LAYERS,
  "num_thread": $(nproc),
  "repeat_penalty": 1.1,
  "temperature": 0.8,
  "top_p": 0.9,
  "keep_alive": "5m"
}
EOF
    
    # Create Stable Diffusion configuration
    cat > ./sd-config/config.json << EOF
{
  "samples_save": false,
  "grid_save": false,
  "return_grid": false,
  "enable_pnginfo": false,
  "save_images_before_face_restoration": false,
  "save_images_before_highres_fix": false,
  "save_images_before_color_correction": false,
  "jpeg_quality": 95,
  "export_for_4chan": false,
  "use_original_name_batch": false,
  "save_selected_only": true,
  "do_not_add_watermark": true,
  "sd_model_checkpoint": "${SD_MODELS[0]}",
  "CLIP_stop_at_last_layers": 2,
  "enable_emphasis": true,
  "enable_batch_seeds": true,
  "comma_padding_backtrack": 20,
  "CLIP_stop_at_last_layers": 2
}
EOF
    
    # Update environment variables
    if [[ -f .env ]]; then
        sed -i "s/PREFERRED_LLM=.*/PREFERRED_LLM=${LLM_MODELS[0]}/" .env
        sed -i "s/FALLBACK_LLM=.*/FALLBACK_LLM=${LLM_MODELS[1]}/" .env
        sed -i "s/PREFERRED_SD_MODEL=.*/PREFERRED_SD_MODEL=${SD_MODELS[0]}/" .env
    fi
    
    log "Model settings configured"
}

# Test models
test_models() {
    if [[ "$SKIP_TESTS" == true ]]; then
        info "Skipping model tests"
        return
    fi
    
    log "Testing installed models..."
    
    # Test Ollama models
    for model in "${LLM_MODELS[@]}"; do
        info "Testing $model..."
        
        local test_prompt="Hello, this is a test. Please respond with 'Model $model is working correctly.'"
        
        if docker exec youtube-automation-ollama ollama run "$model" "$test_prompt" | grep -q "working correctly"; then
            log "✅ $model is working correctly"
        else
            warn "⚠️  $model test failed or gave unexpected response"
        fi
    done
    
    # Test Stable Diffusion
    info "Testing Stable Diffusion..."
    
    local test_payload='{
        "prompt": "test image, simple red circle on white background",
        "steps": 10,
        "width": 512,
        "height": 512,
        "cfg_scale": 7,
        "sampler_name": "Euler a"
    }'
    
    if curl -s -X POST -H "Content-Type: application/json" \
        -d "$test_payload" \
        http://localhost:7860/sdapi/v1/txt2img | grep -q "images"; then
        log "✅ Stable Diffusion is working correctly"
    else
        warn "⚠️  Stable Diffusion test failed"
    fi
}

# Generate installation report
generate_report() {
    log "Generating installation report..."
    
    cat > MODEL_INSTALLATION_REPORT.md << EOF
# 🤖 AI Model Installation Report

## 📊 Installation Summary
- **Profile**: $PROFILE
- **Installation Date**: $(date)
- **Total Models Installed**: $((${#LLM_MODELS[@]} + ${#SD_MODELS[@]}))

## 🧠 Language Models (Ollama)
$(for model in "${LLM_MODELS[@]}"; do
    echo "- **$model**: $(docker exec youtube-automation-ollama ollama list | grep "$model" | awk '{print $2}' || echo "Not found")"
done)

## 🎨 Image Models (Stable Diffusion)
$(for model in "${SD_MODELS[@]}"; do
    if [[ -f "./sd-models/Stable-diffusion/$model" ]]; then
        size=$(du -h "./sd-models/Stable-diffusion/$model" | cut -f1)
        echo "- **$model**: $size"
    else
        echo "- **$model**: Not found"
    fi
done)

## ⚙️ Configuration
- **Context Size**: $CONTEXT_SIZE
- **GPU Layers**: $GPU_LAYERS
- **SD Steps**: $SD_STEPS

## 🎯 Next Steps
1. **Test the System**: Run the complete test suite
2. **Import N8N Workflow**: Load the advanced workflow
3. **Configure APIs**: Set up YouTube and Google Sheets credentials
4. **Start Creating**: Begin automated content generation!

## 📊 Model Usage Guidelines
- **Primary LLM**: ${LLM_MODELS[0]} (for main content generation)
- **Fallback LLM**: ${LLM_MODELS[1]} (if primary fails)
- **Image Model**: ${SD_MODELS[0]} (for thumbnail generation)

## 🔧 Troubleshooting
If you encounter issues:
1. Check service logs: \`docker-compose -f docker-compose.local-ai.yml logs [service]\`
2. Restart services: \`docker-compose -f docker-compose.local-ai.yml restart\`
3. Re-run model installation: \`./install-models.sh --profile $PROFILE --force\`

## 🎉 Ready for Local AI Content Creation!
Your models are installed and ready to generate amazing YouTube content completely offline!
EOF
    
    log "Installation report created: MODEL_INSTALLATION_REPORT.md"
}

# Main function
main() {
    echo "🤖 AI Model Installation for YouTube Automation"
    echo "=============================================="
    echo
    
    detect_hardware_profile
    define_model_configs
    check_services
    install_ollama_models
    install_sd_models
    configure_models
    test_models
    generate_report
    
    echo
    echo "🎉 Model Installation Complete!"
    echo "==============================="
    echo
    echo "📊 Installed Models:"
    echo "  LLM Models: ${LLM_MODELS[*]}"
    echo "  SD Models: ${SD_MODELS[*]}"
    echo
    echo "📋 Next steps:"
    echo "1. Import N8N workflow: advanced-youtube-automation-workflow.json"
    echo "2. Configure YouTube API credentials in .env"
    echo "3. Test the complete system"
    echo
    echo "📖 See MODEL_INSTALLATION_REPORT.md for detailed information"
    echo "🚀 Ready to create content with local AI!"
}

# Run main function
main "$@"

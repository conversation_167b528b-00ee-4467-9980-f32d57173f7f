# 🚀 AI Video Empire - Automated Setup Script
# This script installs all required components for your YouTube automation system

Write-Host "🚀 AI VIDEO EMPIRE SETUP STARTING..." -ForegroundColor Green
Write-Host "Setting up automated video production for raghavrsg8125" -ForegroundColor Yellow

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ This script requires Administrator privileges!" -ForegroundColor Red
    Write-Host "Right-click PowerShell and 'Run as Administrator'" -ForegroundColor Yellow
    exit 1
}

# Function to check if command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

Write-Host "`n🔍 CHECKING SYSTEM REQUIREMENTS..." -ForegroundColor Cyan

# Check Python
Write-Host "Checking Python installation..." -ForegroundColor Yellow
if (Test-Command python) {
    $pythonVersion = python --version
    Write-Host "✅ Python found: $pythonVersion" -ForegroundColor Green
} else {
    Write-Host "❌ Python not found. Installing..." -ForegroundColor Red
    try {
        # Try winget first
        winget install Python.Python.3.11
        Write-Host "✅ Python installed successfully!" -ForegroundColor Green
    } catch {
        Write-Host "⚠️ Please install Python manually from python.org" -ForegroundColor Yellow
        Start-Process "https://www.python.org/downloads/"
    }
}

# Check Chocolatey
Write-Host "`nChecking Chocolatey..." -ForegroundColor Yellow
if (Test-Command choco) {
    Write-Host "✅ Chocolatey found" -ForegroundColor Green
} else {
    Write-Host "❌ Chocolatey not found. Installing..." -ForegroundColor Red
    Set-ExecutionPolicy Bypass -Scope Process -Force
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
    iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
    Write-Host "✅ Chocolatey installed!" -ForegroundColor Green
}

# Check FFmpeg
Write-Host "`nChecking FFmpeg..." -ForegroundColor Yellow
if (Test-Command ffmpeg) {
    $ffmpegVersion = ffmpeg -version 2>&1 | Select-String "ffmpeg version" | Select-Object -First 1
    Write-Host "✅ FFmpeg found: $ffmpegVersion" -ForegroundColor Green
} else {
    Write-Host "❌ FFmpeg not found. Installing..." -ForegroundColor Red
    choco install ffmpeg -y
    Write-Host "✅ FFmpeg installed!" -ForegroundColor Green
}

# Install Python dependencies
Write-Host "`n📦 INSTALLING PYTHON DEPENDENCIES..." -ForegroundColor Cyan
$pythonPackages = @(
    "requests",
    "pillow", 
    "opencv-python",
    "moviepy",
    "edge-tts"
)

foreach ($package in $pythonPackages) {
    Write-Host "Installing $package..." -ForegroundColor Yellow
    try {
        python -m pip install $package
        Write-Host "✅ $package installed" -ForegroundColor Green
    } catch {
        Write-Host "⚠️ Failed to install $package" -ForegroundColor Red
    }
}

# Check LM Studio
Write-Host "`n🤖 CHECKING LM STUDIO..." -ForegroundColor Cyan
$lmStudioPath = "$env:LOCALAPPDATA\Programs\LM Studio\LM Studio.exe"
if (Test-Path $lmStudioPath) {
    Write-Host "✅ LM Studio found!" -ForegroundColor Green
    Write-Host "📝 Make sure to:" -ForegroundColor Yellow
    Write-Host "   1. Start LM Studio" -ForegroundColor White
    Write-Host "   2. Download llama-3.2-3b-instruct model" -ForegroundColor White
    Write-Host "   3. Start local server on port 1234" -ForegroundColor White
} else {
    Write-Host "❌ LM Studio not found. Opening download page..." -ForegroundColor Red
    Start-Process "https://lmstudio.ai/"
    Write-Host "📝 Please download and install LM Studio manually" -ForegroundColor Yellow
}

# Test LM Studio connection
Write-Host "`n🔗 TESTING LM STUDIO CONNECTION..." -ForegroundColor Cyan
try {
    $response = Invoke-RestMethod -Uri "http://localhost:1234/v1/models" -Method GET -TimeoutSec 5
    Write-Host "✅ LM Studio server is running!" -ForegroundColor Green
} catch {
    Write-Host "⚠️ LM Studio server not responding" -ForegroundColor Yellow
    Write-Host "   Please start LM Studio and enable local server" -ForegroundColor White
}

# Create directories
Write-Host "`n📁 CREATING DIRECTORIES..." -ForegroundColor Cyan
$directories = @(
    "C:\AI_Video_Empire",
    "C:\AI_Video_Empire\temp",
    "C:\AI_Video_Empire\output",
    "C:\AI_Video_Empire\scripts"
)

foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force
        Write-Host "✅ Created: $dir" -ForegroundColor Green
    }
}

# Copy video production script
Write-Host "`n📋 SETTING UP VIDEO PRODUCTION..." -ForegroundColor Cyan
$scriptSource = ".\video_production.py"
$scriptDest = "C:\AI_Video_Empire\scripts\video_production.py"

if (Test-Path $scriptSource) {
    Copy-Item $scriptSource $scriptDest -Force
    Write-Host "✅ Video production script installed" -ForegroundColor Green
} else {
    Write-Host "⚠️ video_production.py not found in current directory" -ForegroundColor Yellow
}

# Final status
Write-Host "`n🎉 SETUP COMPLETE!" -ForegroundColor Green
Write-Host "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━" -ForegroundColor Cyan

Write-Host "`n📋 NEXT STEPS:" -ForegroundColor Yellow
Write-Host "1. ✅ Import Ultimate_AI_Video_Empire_Workflow.json into N8N" -ForegroundColor White
Write-Host "2. ✅ Configure YouTube API credentials in N8N" -ForegroundColor White  
Write-Host "3. ✅ Set up ElevenLabs API key (optional)" -ForegroundColor White
Write-Host "4. ✅ Start LM Studio with llama-3.2-3b-instruct model" -ForegroundColor White
Write-Host "5. ✅ Activate the N8N workflow" -ForegroundColor White

Write-Host "`n🚀 Your AI Video Empire is ready to dominate YouTube!" -ForegroundColor Green
Write-Host "Channel: raghavrsg8125 | Email: <EMAIL>" -ForegroundColor Cyan

# Open important links
Write-Host "`n🌐 Opening setup resources..." -ForegroundColor Yellow
Start-Process "https://lmstudio.ai/"
Start-Process "https://elevenlabs.io/"
Start-Process "https://console.cloud.google.com/"

Write-Host "`nPress any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

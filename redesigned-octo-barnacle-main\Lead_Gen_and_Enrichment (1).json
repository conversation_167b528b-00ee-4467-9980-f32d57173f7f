{"name": "Lead Gen and Enrichment", "nodes": [{"parameters": {"updates": ["message"], "additionalFields": {}}, "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.1, "position": [-380, -20], "id": "65db9139-2433-4348-91fd-5b64776e98c3", "name": "<PERSON>eg<PERSON>", "webhookId": "30f79c4a-7c95-46d9-8ef7-ab3f91b0d2b1", "credentials": {"telegramApi": {"id": "4e3aDLbuThZvbFyX", "name": "<PERSON><PERSON> <PERSON>"}}}, {"parameters": {"text": "={{ $json.message.text }}", "attributes": {"attributes": [{"name": "company_type", "description": "The industry keyword (eg roofing, plumbing, etc)", "required": true}, {"name": "location", "description": "the location of the company with country, with ASCII characters where needed as this is going to be a URL parameter. For example: North%20Carolina%2C%20US", "required": true}, {"name": "Location_Proper", "description": "Location without ASCII characters: example - North Carolina", "required": true}]}, "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [-160, -20], "id": "0cb935ae-c31f-4e57-9ee8-2a56f1056561", "name": "Information Extractor"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-100, 160], "id": "5dab851c-6971-4d8f-8d50-3b9789152909", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "ulLOziYE3uIZc6he", "name": "OpenAi account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "44d7a9cd-d4ad-4ea6-855b-bd7f8d0fa4eb", "leftValue": "={{ $json.data.status }}", "rightValue": "SUCCEEDED", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [1040, -20], "id": "5ec62d18-4988-485d-91fc-c0f5d9fadc6c", "name": "If"}, {"parameters": {}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [540, -20], "id": "36de05dc-6b57-4af8-8688-4b18c4bfbdf0", "name": "Wait", "webhookId": "a01d7543-2d01-4a6c-8e2d-17175c5653e5"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "66a81b3f-0661-46ba-9338-ec2e5479addf", "leftValue": "={{ $json.email }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.filter", "typeVersion": 2.2, "position": [1560, -240], "id": "389b7245-297d-4b67-be19-c4ab26433c3b", "name": "Filter"}, {"parameters": {"url": "=https://api.apify.com/v2/datasets/{{ $json.data.defaultDatasetId }}/items", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer **********************************************"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1340, -240], "id": "c7e99a12-e63e-4833-9ac8-25f0ca5033ea", "name": "Apify Get Dataset"}, {"parameters": {"url": "=https://api.apify.com/v2/acts/{{ $json.data.actId }}/runs/last ", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer YOUR_API_HERE"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [800, -20], "id": "74be8b7f-a4ba-4b4d-b4f9-a72cc35e39b4", "name": "Apify Check Status"}, {"parameters": {"method": "POST", "url": "https://api.apify.com/v2/acts/jljBwyyQakqrL1wae/runs", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer YOUR_API_HERE"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"getPersonalEmails\": true,\n    \"totalRecords\": 1000,\n    \"getWorkEmails\": true,\n    \"url\": \"https://app.apollo.io/#/people?page=1&contactEmailExcludeCatchAll=true&personLocations[]={{ $('Information Extractor').item.json.output.location }}&sortAscending=false&sortByField=recommendations_score&qOrganizationKeywordTags[]={{ $('Information Extractor').item.json.output.company_type }}&includedOrganizationKeywordFields[]=tags&includedOrganizationKeywordFields[]=name&includedOrganizationKeywordFields[]=social_media_description&personTitles[]=manager&personTitles[]=owner&personTitles[]=president\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [280, -20], "id": "16aa6273-4808-47fb-bafd-f70eddb968ab", "name": "Apify <PERSON>"}, {"parameters": {"chatId": "={{ $json.chat_id[0] }}", "text": "={{ $json.count[0] }} leads added to database for {{ $json.industry[0] }} in {{ $json.location[0] }}", "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1800, 0], "id": "0eeb6edc-2adc-4a38-b5e3-fd83000e9f7e", "name": "Telegram", "webhookId": "fad5685d-1374-43ff-91f4-812b0778e16f", "credentials": {"telegramApi": {"id": "4e3aDLbuThZvbFyX", "name": "<PERSON><PERSON> <PERSON>"}}}, {"parameters": {"assignments": {"assignments": [{"id": "78dad5fd-23f4-47a7-83b2-4cd862e50a5c", "name": "count", "value": "={{ $('Code').all().length }}", "type": "number"}, {"id": "cc146dd6-5fd4-4406-bc23-df523819af5a", "name": "industry", "value": "={{ $('Information Extractor').item.json.output.company_type }}", "type": "string"}, {"id": "9e18345f-f7af-4a4e-9350-ebefc6fb2bcf", "name": "location", "value": "={{ $('Information Extractor').item.json.output.Location_Proper }}", "type": "string"}, {"id": "6ead0f1c-5ba0-4052-a5e6-8378ece85b4d", "name": "chat_id", "value": "={{ $('Telegram Trigger').item.json.message.chat.id }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1340, 0], "id": "dd481bef-aeab-42ae-8831-d6a9d8a07671", "name": "<PERSON>"}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "count"}, {"fieldToAggregate": "industry"}, {"fieldToAggregate": "location"}, {"fieldToAggregate": "chat_id"}]}, "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [1560, 0], "id": "95dac2bb-7bc3-4a54-b7df-5fe1ff979a7f", "name": "Aggregate"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "efef69d4-832a-4081-b25e-0abe41669ce5", "leftValue": "={{ $json.estimated_num_employees }}", "rightValue": "", "operator": {"type": "number", "operation": "empty", "singleValue": true}}, {"id": "100e63f1-d176-4650-b666-2bcf884086df", "leftValue": "={{ $json.estimated_num_employees }}", "rightValue": 100, "operator": {"type": "number", "operation": "lt"}}], "combinator": "or"}, "options": {}}, "type": "n8n-nodes-base.filter", "typeVersion": 2.2, "position": [1780, -240], "id": "84af81e4-bdda-4591-bed7-e44795a58251", "name": "Filter1"}, {"parameters": {"jsCode": "return items.map(item => {\n  const title = (item.json.title || \"\").toLowerCase();\n  const employeeCount = item.json.organization?.estimated_num_employees || 0;\n  const linkedin = item.json.linkedin_url || \"\";\n  const website = item.json.organization?.website_url || \"\";\n\n  let score = 0;\n\n  // Title seniority scoring\n  if (title.match(/ceo|owner|founder|co-founder|president/)) {\n    score += 50;\n  } else if (title.match(/vp|vice president|director/)) {\n    score += 35;\n  } else if (title.includes(\"manager\")) {\n    score += 15;\n  }\n\n  // Employee count scoring\n  if (employeeCount > 20) score += 30;\n  else if (employeeCount >= 10) score += 20;\n  else if (employeeCount > 0) score += 10;\n\n  // LinkedIn profile\n  if (linkedin.includes(\"linkedin.com\")) score += 10;\n\n  // Website presence\n  if (website.includes(\"http\")) score += 10;\n\n  // Cap at 100\n  item.json[\"Lead Score\"] = Math.min(score, 100);\n  return item;\n});"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2020, -240], "id": "f2bb7829-067a-4fdc-9bb4-7992ffa7e748", "name": "Code"}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1cmLNRuydzWUu6HVJTcbfobObaWegTc7zkuOsh3XyJbU", "mode": "list", "cachedResultName": "Lead Scraping", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1cmLNRuydzWUu6HVJTcbfobObaWegTc7zkuOsh3XyJbU/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": **********, "mode": "list", "cachedResultName": "Raw Leads", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1cmLNRuydzWUu6HVJTcbfobObaWegTc7zkuOsh3XyJbU/edit#gid=**********"}, "columns": {"mappingMode": "defineBelow", "value": {"First Name": "={{ $json.first_name }}", "Last Name": "={{ $json.last_name }}", "Industry": "={{ $('Information Extractor').item.json.output.company_type }}", "Location": "={{ $('Information Extractor').item.json.output.location }}", "Title": "={{ $json.title }}", "Email": "={{ $json.email }}", "Headline": "={{ $json.headline }}", "LinkedIn URL": "={{ $json.linkedin_url }}", "Company": "={{ $json.organization.name }}", "Company LinkedIn": "={{ $json.organization.linkedin_url }}", "Website": "={{ $json.organization.website_url }}", "Employee Count": "={{ $json.organization.estimated_num_employees }}", "Lead Score": "={{ $json['Lead Score'] }}"}, "matchingColumns": [], "schema": [{"id": "First Name", "displayName": "First Name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Last Name", "displayName": "Last Name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Industry", "displayName": "Industry", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Location", "displayName": "Location", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Title", "displayName": "Title", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Email", "displayName": "Email", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Headline", "displayName": "Headline", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "LinkedIn URL", "displayName": "LinkedIn URL", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Company", "displayName": "Company", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Company LinkedIn", "displayName": "Company LinkedIn", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Website", "displayName": "Website", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Employee Count", "displayName": "Employee Count", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Lead Score", "displayName": "Lead Score", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Raw Insights", "displayName": "Raw Insights", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "<PERSON> Breaker", "displayName": "<PERSON> Breaker", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [2280, -240], "id": "53dd0fab-13e0-4271-8946-2c8ce02dc5ac", "name": "Add Leads to Sheet", "credentials": {"googleSheetsOAuth2Api": {"id": "1tkwsToZLlEshQfI", "name": "Google Sheets account"}}}, {"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-380, 400], "id": "33e3d9c8-7d86-4bd8-a841-4f2e08a5b604", "name": "When clicking ‘Test workflow’"}, {"parameters": {"documentId": {"__rl": true, "value": "1cmLNRuydzWUu6HVJTcbfobObaWegTc7zkuOsh3XyJbU", "mode": "list", "cachedResultName": "Lead Scraping", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1cmLNRuydzWUu6HVJTcbfobObaWegTc7zkuOsh3XyJbU/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": **********, "mode": "list", "cachedResultName": "Raw Leads", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1cmLNRuydzWUu6HVJTcbfobObaWegTc7zkuOsh3XyJbU/edit#gid=**********"}, "filtersUI": {"values": [{"lookupColumn": "Raw Insights"}]}, "options": {"returnFirstMatch": false}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [-160, 400], "id": "0ddf8261-324a-4e82-be19-5fcf479fb669", "name": "Google Sheets", "credentials": {"googleSheetsOAuth2Api": {"id": "1tkwsToZLlEshQfI", "name": "Google Sheets account"}}}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [140, 400], "id": "e9c25515-cf9e-4c8f-b046-ae014a2bca98", "name": "Loop Over Items"}, {"parameters": {"method": "POST", "url": "https://api.perplexity.ai/chat/completions", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"model\": \"sonar\",\n  \"messages\": [\n    {\n      \"role\": \"system\",\n      \"content\": \"You are a b2b intelligence expert and your specialty is researching cold leads and finding insights to use for crafting personalized emails that have high reply rates. I am pitching discovery calls for AI automation services to {{ $json.Industry }} companies.\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": \"Find insights on {{ $json['First Name'] }} {{ $json['Last Name'] }} of {{ $json.Company }}.\"\n    }\n  ]\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [460, 460], "id": "29436016-f045-416e-8d65-62a46567cfe5", "name": "HTTP Request", "credentials": {"httpHeaderAuth": {"id": "W75NmqqFTAvWoVel", "name": "Perplexity"}}}, {"parameters": {"operation": "appendOrUpdate", "documentId": {"__rl": true, "value": "1cmLNRuydzWUu6HVJTcbfobObaWegTc7zkuOsh3XyJbU", "mode": "list", "cachedResultName": "Lead Scraping", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1cmLNRuydzWUu6HVJTcbfobObaWegTc7zkuOsh3XyJbU/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": **********, "mode": "list", "cachedResultName": "Raw Leads", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1cmLNRuydzWUu6HVJTcbfobObaWegTc7zkuOsh3XyJbU/edit#gid=**********"}, "columns": {"mappingMode": "defineBelow", "value": {"Email": "={{ $('Loop Over Items').item.json.Email }}", "Raw Insights": "={{ $('HTTP Request').item.json.choices[0].message.content }}", "Ice Breaker": "={{ $json.text }}"}, "matchingColumns": ["Email"], "schema": [{"id": "First Name", "displayName": "First Name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Last Name", "displayName": "Last Name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Industry", "displayName": "Industry", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Location", "displayName": "Location", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Title", "displayName": "Title", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Email", "displayName": "Email", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Headline", "displayName": "Headline", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "LinkedIn URL", "displayName": "LinkedIn URL", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Company", "displayName": "Company", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Company LinkedIn", "displayName": "Company LinkedIn", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Website", "displayName": "Website", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Employee Count", "displayName": "Employee Count", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Lead Score", "displayName": "Lead Score", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Raw Insights", "displayName": "Raw Insights", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "<PERSON> Breaker", "displayName": "<PERSON> Breaker", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [1080, 460], "id": "9ee76d5c-da46-47f8-81bb-aeca2e0ed07b", "name": "Google Sheets1", "credentials": {"googleSheetsOAuth2Api": {"id": "1tkwsToZLlEshQfI", "name": "Google Sheets account"}}}, {"parameters": {"promptType": "define", "text": "=Here are some insights about a prospect I am researching to send a b2b cold email:\n{{ $json.choices[0].message.content }}.\n\nUse these insights to craft a winning, engaging, and reply-inducing ice breaker for a cold email. The ice breaker will come right after the greeting. Do not output the greeting, only the ice breaker. It is vital to my career that your ice breaker is brief(200 characters max), attention-grabbing, and demonstrates that I have done personal research about my prospect. Output the ice breaker directly. Be professional, friendly, and avoid being sycophantic."}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.5, "position": [720, 460], "id": "1e05be3a-e14e-4b87-bc12-334d638bea25", "name": "Basic LLM Chain"}, {"parameters": {"method": "POST", "url": "https://server.smartlead.ai/api/v1/campaigns/1951445/leads", "sendQuery": true, "queryParameters": {"parameters": [{"name": "api_key", "value": "46a161b2-9a5e-4c1d-b4ad-3a19d5b32910_k9h1qum"}]}, "sendBody": true, "contentType": "raw", "rawContentType": "application/json", "body": "={\n  \"lead_list\": [\n    {\n      \"first_name\": \"{{ $('Loop Over Items').item.json['First Name'] }}\",\n      \"last_name\": \"{{ $('Loop Over Items').item.json['Last Name'] }}\",\n      \"email\": \"{{ $('Loop Over Items').item.json.Email }}\",\n      \"phone_number\": \" \",\n      \"company_name\": \"{{ $('Loop Over Items').item.json.Company }}\",\n      \"website\": \"{{ $('Loop Over Items').item.json.Website }}\",\n      \"location\": \"{{ $('Loop Over Items').item.json.Location }}\",\n      \"custom_fields\": {\n        \"Title\": \"{{ $('Loop Over Items').item.json.Title }}\",\n        \"First_Line\": \"{{ $json['Ice Breaker'] }}\"\n      },\n      \"linkedin_profile\": \"{{ $('Loop Over Items').item.json['LinkedIn URL'] }}\",\n      \"company_url\": \"{{ $('Loop Over Items').item.json.Website }}\"\n    }\n  ],\n  \"settings\": {\n    \"ignore_global_block_list\": true,\n    \"ignore_unsubscribe_list\": true,\n    \"ignore_duplicate_leads_in_other_campaign\": false\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1300, 460], "id": "8ef23b7e-b879-4b2c-b507-99970f9bebb7", "name": "HTTP Request1"}, {"parameters": {"amount": 30}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [1480, 460], "id": "a7422fcc-d7d4-4ecb-b67b-54a275d2f7bc", "name": "Wait1", "webhookId": "fe32dded-1dcb-4400-9746-501b457db64e"}], "pinData": {}, "connections": {"Telegram Trigger": {"main": [[{"node": "Information Extractor", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Information Extractor", "type": "ai_languageModel", "index": 0}, {"node": "Basic LLM Chain", "type": "ai_languageModel", "index": 0}]]}, "Information Extractor": {"main": [[{"node": "Apify <PERSON>", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "Apify Check Status", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "Apify Get Dataset", "type": "main", "index": 0}], [{"node": "Wait", "type": "main", "index": 0}]]}, "Filter": {"main": [[{"node": "Filter1", "type": "main", "index": 0}]]}, "Apify Get Dataset": {"main": [[{"node": "Filter", "type": "main", "index": 0}]]}, "Apify Check Status": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Apify Apollo Scraper": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}, "Filter1": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "Add Leads to Sheet", "type": "main", "index": 0}]]}, "Add Leads to Sheet": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}, "Google Sheets": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Basic LLM Chain", "type": "main", "index": 0}]]}, "Google Sheets1": {"main": [[{"node": "HTTP Request1", "type": "main", "index": 0}]]}, "Basic LLM Chain": {"main": [[{"node": "Google Sheets1", "type": "main", "index": 0}]]}, "HTTP Request1": {"main": [[{"node": "Wait1", "type": "main", "index": 0}]]}, "Wait1": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "cdb72b19-39d3-4ecd-ad2a-ddde6597ee40", "meta": {"templateCredsSetupCompleted": true, "instanceId": "fb357db16d4f03c7d7597f4abaa6a5e06176011d3bee518d28bed06754cb1f31"}, "id": "jCybtDLPDT63VsXr", "tags": []}
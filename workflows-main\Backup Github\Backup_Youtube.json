{"name": "Backup Youtube", "nodes": [{"parameters": {"assignments": {"assignments": [{"id": "61df5ffb-7587-4ec0-a4ef-5d03168857c5", "name": "owner", "value": "gabriel-g2n", "type": "string"}, {"id": "167893e5-9de8-4273-9fc3-3f2da550ebf7", "name": "repository", "value": "n8n-backup-youtube", "type": "string"}]}, "options": {}}, "id": "6dceb5a2-5b6a-4c1e-8972-ab99e89bff25", "name": "Infos Basicas", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1060, 420]}, {"parameters": {"mode": "raw", "jsonOutput": "={{ $json.data }}", "options": {}}, "id": "25f06400-d90d-4910-9e86-16eeb8587875", "name": "Workflow", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2300, 800]}, {"parameters": {"operation": "to<PERSON><PERSON>", "mode": "each", "options": {"format": true, "fileName": "={{ $json.id }}.json"}}, "id": "9947b777-67b0-414f-9070-9615e8467cbf", "name": "Convert to File", "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [2500, 800]}, {"parameters": {"authentication": "oAuth2", "resource": "file", "operation": "get", "owner": {"__rl": true, "value": "={{ $('Infos Basicas').item.json.owner }}", "mode": "name"}, "repository": {"__rl": true, "value": "={{ $('Infos Basicas').item.json.repository }}", "mode": "name"}, "filePath": "=cliente-x/{{ $json.id }}.json", "additionalParameters": {}}, "id": "c3986913-970e-492e-a097-c99356f73aac", "name": "GitHub", "type": "n8n-nodes-base.github", "typeVersion": 1, "position": [1600, 280], "alwaysOutputData": true, "credentials": {"githubOAuth2Api": {"id": "cK5zPCTIu4VbpDUE", "name": "GitHub account 2"}}, "onError": "continueRegularOutput"}, {"parameters": {"operation": "fromJson", "options": {}}, "id": "9fced7bb-29b4-4586-9cf5-dfee80ed44f1", "name": "Extract from File", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [1820, 280]}, {"parameters": {"assignments": {"assignments": [{"id": "d55c571b-e214-48c5-9516-b16c71f5419e", "name": "data", "value": "={{ $json }}", "type": "object"}]}, "options": {}}, "id": "72bd9205-f5e2-452f-bc3d-6d449136d5e5", "name": "Workflow dados", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1600, 620]}, {"parameters": {"filters": {}, "requestOptions": {}}, "id": "807b3002-72e1-4195-a442-43671cb97a7b", "name": "Obter todos", "type": "n8n-nodes-base.n8n", "typeVersion": 1, "position": [1280, 420], "credentials": {"n8nApi": {"id": "0Owhw6MuBsjylAFE", "name": "n8n account"}}}, {"parameters": {"mergeByFields": {"values": [{"field1": "data.id", "field2": "data.id"}]}, "resolve": "preferInput2", "options": {}}, "id": "8f504a84-3c54-4575-a133-31ae32f0ebd7", "name": "Compare Datasets", "type": "n8n-nodes-base.compareDatasets", "typeVersion": 2.3, "position": [2060, 480]}, {"parameters": {"authentication": "oAuth2", "resource": "file", "owner": {"__rl": true, "value": "={{ $('Infos Basicas').first().json.owner }}", "mode": "name"}, "repository": {"__rl": true, "value": "={{ $('Infos Basicas').first().json.repository }}", "mode": "name"}, "filePath": "=cliente-x/{{ $('Workflow').item.json.id }}.json", "binaryData": true, "commitMessage": "={{ $('Workflow').item.json.name }} {{ $now.toString() }}"}, "id": "5592b75e-3f47-47ae-af6f-c93e2a57a22d", "name": "GitHub Create File", "type": "n8n-nodes-base.github", "typeVersion": 1, "position": [2700, 800], "credentials": {"githubOAuth2Api": {"id": "cK5zPCTIu4VbpDUE", "name": "GitHub account 2"}}}, {"parameters": {"rule": {"interval": [{}]}}, "id": "e3766688-a3ad-483f-be4a-ead95e1cfe59", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [820, 420]}, {"parameters": {"mode": "raw", "jsonOutput": "={{ $json.data }}", "options": {}}, "id": "a827580c-511f-4bfe-95f8-d9aa3058feb9", "name": "Workflow1", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2300, 600]}, {"parameters": {"operation": "to<PERSON><PERSON>", "mode": "each", "options": {"format": true, "fileName": "={{ $json.id }}.json"}}, "id": "91c3505f-33cb-496d-ba89-842e7a460530", "name": "Convert to File1", "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [2500, 600]}, {"parameters": {"authentication": "oAuth2", "resource": "file", "operation": "edit", "owner": {"__rl": true, "value": "={{ $('Infos Basicas').first().json.owner }}", "mode": "name"}, "repository": {"__rl": true, "value": "={{ $('Infos Basicas').first().json.repository }}", "mode": "name"}, "filePath": "=cliente-x/{{ $('Workflow1').item.json.id }}.json", "binaryData": true, "commitMessage": "={{ $('Workflow1').item.json.name }} {{ $now.toString() }}"}, "id": "f50364ad-9136-4f2d-9689-977bca7b0689", "name": "GitHub Edit File", "type": "n8n-nodes-base.github", "typeVersion": 1, "position": [2700, 600], "credentials": {"githubOAuth2Api": {"id": "cK5zPCTIu4VbpDUE", "name": "GitHub account 2"}}}], "pinData": {}, "connections": {"Infos Basicas": {"main": [[{"node": "Obter todos", "type": "main", "index": 0}]]}, "Workflow": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}, "Convert to File": {"main": [[{"node": "GitHub Create File", "type": "main", "index": 0}]]}, "GitHub": {"main": [[{"node": "Extract from File", "type": "main", "index": 0}]]}, "Extract from File": {"main": [[{"node": "Compare Datasets", "type": "main", "index": 0}]]}, "Workflow dados": {"main": [[{"node": "Compare Datasets", "type": "main", "index": 1}]]}, "Obter todos": {"main": [[{"node": "GitHub", "type": "main", "index": 0}, {"node": "Workflow dados", "type": "main", "index": 0}]]}, "Compare Datasets": {"main": [[], [], [{"node": "Workflow1", "type": "main", "index": 0}], [{"node": "Workflow", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Infos Basicas", "type": "main", "index": 0}]]}, "Workflow1": {"main": [[{"node": "Convert to File1", "type": "main", "index": 0}]]}, "Convert to File1": {"main": [[{"node": "GitHub Edit File", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "5731763a-01bd-4ef9-a462-a148c354c7d6", "meta": {"templateCredsSetupCompleted": true, "instanceId": "2ad20c637f548d7b1e09cbae0383c6644aca87b4f0fe8deda2de937f77c2be79"}, "id": "FIKX6Vl9TSbIVvsy", "tags": []}
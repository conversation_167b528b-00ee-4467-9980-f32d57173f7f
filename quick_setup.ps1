Write-Host "🚀 AI VIDEO EMPIRE - QUICK SETUP" -ForegroundColor Green

# Check Python
Write-Host "`n1. Checking Python..." -ForegroundColor Yellow
try {
    $pythonVersion = python --version 2>&1
    if ($pythonVersion -like "*Python*") {
        Write-Host "✅ Python found: $pythonVersion" -ForegroundColor Green
    } else {
        Write-Host "❌ Python not found" -ForegroundColor Red
        Write-Host "Installing Python..." -ForegroundColor Yellow
        winget install Python.Python.3.11
    }
} catch {
    Write-Host "❌ Python not found - please install from python.org" -ForegroundColor Red
}

# Check FFmpeg
Write-Host "`n2. Checking FFmpeg..." -ForegroundColor Yellow
try {
    $ffmpegVersion = ffmpeg -version 2>&1 | Select-String "ffmpeg version" | Select-Object -First 1
    Write-Host "✅ FFmpeg found" -ForegroundColor Green
} catch {
    Write-Host "❌ FFmpeg not found" -ForegroundColor Red
    Write-Host "Please install FFmpeg manually or use chocolatey:" -ForegroundColor Yellow
    Write-Host "choco install ffmpeg" -ForegroundColor White
}

# Check LM Studio
Write-Host "`n3. Checking LM Studio..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "http://localhost:1234/v1/models" -Method GET -TimeoutSec 3
    Write-Host "✅ LM Studio server is running!" -ForegroundColor Green
} catch {
    Write-Host "❌ LM Studio not responding" -ForegroundColor Red
    Write-Host "Please start LM Studio and enable local server" -ForegroundColor Yellow
}

Write-Host "`n🎯 SETUP STATUS COMPLETE" -ForegroundColor Cyan

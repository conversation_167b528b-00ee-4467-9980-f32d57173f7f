{
  "name": "Complete LM Studio YouTube Automation",
  "nodes": [
    {
      "parameters": {
        "rule": {
          "interval": [
            {
              "field": "hours",
              "hoursInterval": 2
            }
          ]
        }
      },
      "type": "n8n-nodes-base.scheduleTrigger",
      "typeVersion": 1.2,
      "position": [-800, 200],
      "id": "schedule-trigger",
      "name": "⏰ Schedule Trigger"
    },
    {
      "parameters": {
        "httpMethod": "POST",
        "path": "generate-content",
        "responseMode": "responseNode",
        "options": {}
      },
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 2,
      "position": [-800, 400],
      "id": "webhook-trigger",
      "name": "🔗 Manual Trigger"
    },
    {
      "parameters": {
        "jsCode": "// MARKET ANALYSIS & STRATEGY\nconst marketAnalyzer = {\n  analyzeMarket() {\n    const currentTime = new Date();\n    const hour = currentTime.getHours();\n    const dayOfWeek = currentTime.getDay();\n    \n    // Optimal posting times by day\n    const optimalTimes = {\n      0: [14, 18, 20], // Sunday\n      1: [12, 17, 19], // Monday\n      2: [11, 16, 18], // Tuesday\n      3: [13, 17, 20], // Wednesday\n      4: [12, 18, 21], // Thursday\n      5: [15, 19, 22], // Friday\n      6: [10, 16, 20]  // Saturday\n    };\n    \n    const isOptimalTime = optimalTimes[dayOfWeek].includes(hour);\n    \n    // Content strategies by time\n    const strategies = {\n      morning: {\n        contentType: 'educational',\n        duration: 60,\n        tone: 'energetic',\n        topics: ['productivity', 'tutorials', 'how-to']\n      },\n      afternoon: {\n        contentType: 'entertainment',\n        duration: 30,\n        tone: 'casual',\n        topics: ['trending', 'viral', 'quick-tips']\n      },\n      evening: {\n        contentType: 'storytelling',\n        duration: 90,\n        tone: 'engaging',\n        topics: ['stories', 'reviews', 'deep-dive']\n      }\n    };\n    \n    let timeSlot = 'morning';\n    if (hour >= 12 && hour < 18) timeSlot = 'afternoon';\n    if (hour >= 18) timeSlot = 'evening';\n    \n    return {\n      isOptimalTime,\n      currentStrategy: strategies[timeSlot],\n      marketConditions: {\n        competitionLevel: Math.random() > 0.5 ? 'high' : 'moderate',\n        trendingTopics: ['AI automation', 'LM Studio', 'local AI', 'productivity'],\n        recommendedDuration: strategies[timeSlot].duration\n      },\n      timestamp: currentTime.toISOString(),\n      timeSlot\n    };\n  }\n};\n\nconst analysis = marketAnalyzer.analyzeMarket();\n\nreturn {\n  json: {\n    marketAnalysis: analysis,\n    shouldProceed: analysis.isOptimalTime || ($input.first()?.json?.forceRun === true),\n    contentStrategy: analysis.currentStrategy,\n    processingId: `proc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n  }\n};"
      },
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [-500, 300],
      "id": "market-analyzer",
      "name": "🧠 Market Analyzer"
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
          },
          "conditions": [
            {
              "id": "should-proceed",
              "leftValue": "={{ $json.shouldProceed }}",
              "rightValue": true,
              "operator": {
                "type": "boolean",
                "operation": "equals"
              }
            }
          ],
          "combinator": "and"
        },
        "options": {}
      },
      "type": "n8n-nodes-base.if",
      "typeVersion": 2,
      "position": [-200, 300],
      "id": "timing-gate",
      "name": "⏰ Timing Gate"
    },
    {
      "parameters": {
        "jsCode": "// LM STUDIO CONTENT GENERATOR - YOUR EXACT SETUP\nconst LM_STUDIO_URL = 'http://127.0.0.1:1234';\n\nconst contentGenerator = {\n  async generateContent(strategy) {\n    const { contentType, duration, tone, topics } = strategy;\n    \n    const systemPrompt = `You are a professional YouTube content creator. Create engaging, viral content optimized for YouTube algorithm. Respond with valid JSON only.`;\n    \n    const userPrompt = `Create YouTube content with this JSON structure:\n{\n  \"videoTitle\": \"Compelling title (40-60 chars)\",\n  \"videoDescription\": \"SEO description with keywords\",\n  \"videoScript\": \"Complete engaging script\",\n  \"seoTags\": [\"tag1\", \"tag2\", \"tag3\", \"tag4\", \"tag5\"],\n  \"thumbnailPrompt\": \"Thumbnail description\",\n  \"hooks\": [\"hook1\", \"hook2\", \"hook3\"],\n  \"targetAudience\": \"audience description\"\n}\n\nTopic: LM Studio & Local AI\nType: ${contentType}\nDuration: ${duration}s\nTone: ${tone}\nFocus: ${topics.join(', ')}`;\n\n    try {\n      console.log('🤖 Connecting to your LM Studio...');\n      \n      const response = await fetch(`${LM_STUDIO_URL}/v1/chat/completions`, {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          messages: [\n            { role: 'system', content: systemPrompt },\n            { role: 'user', content: userPrompt }\n          ],\n          max_tokens: 2000,\n          temperature: 0.8,\n          stream: false\n        })\n      });\n\n      if (!response.ok) {\n        throw new Error(`LM Studio error: ${response.status}`);\n      }\n\n      const data = await response.json();\n      const content = data.choices[0].message.content;\n      \n      console.log('✅ Generated with your Mistral model!');\n      \n      let parsedContent;\n      try {\n        const cleanContent = content.replace(/```json\\n?|```\\n?/g, '').trim();\n        parsedContent = JSON.parse(cleanContent);\n      } catch (parseError) {\n        parsedContent = this.createFallback(strategy);\n      }\n      \n      return {\n        success: true,\n        content: parsedContent,\n        metadata: {\n          model: 'mistral-7b-instruct-v0.3',\n          tokens: data.usage?.total_tokens || 0,\n          generatedAt: new Date().toISOString()\n        }\n      };\n      \n    } catch (error) {\n      console.error('❌ Error:', error.message);\n      return {\n        success: false,\n        error: error.message,\n        content: this.createFallback(strategy),\n        metadata: { model: 'fallback', isFallback: true }\n      };\n    }\n  },\n  \n  createFallback(strategy) {\n    return {\n      videoTitle: \"🚀 LM Studio Local AI Tutorial - Game Changer!\",\n      videoDescription: \"Master LM Studio for local AI content creation! Learn how to use Mistral models locally for unlimited content generation. Perfect for privacy-focused creators!\\n\\n🔥 Topics covered:\\n- LM Studio setup\\n- Local AI workflows\\n- Content automation\\n\\n#LMStudio #LocalAI #Mistral\",\n      videoScript: \"Hey creators! Today we're exploring LM Studio with the amazing Mistral model. This local AI setup gives you unlimited content generation without API costs. Let me show you exactly how to set this up and create viral content...\",\n      seoTags: [\"LM Studio\", \"Local AI\", \"Mistral\", \"Content Creation\", \"AI Tutorial\"],\n      thumbnailPrompt: \"Bright thumbnail: 'LM STUDIO' text, AI graphics, excited creator, laptop screen\",\n      hooks: [\n        \"This local AI setup will revolutionize your content!\",\n        \"Stop paying for AI - run everything locally!\",\n        \"The Mistral model secret every creator needs!\"\n      ],\n      targetAudience: \"Content creators, developers, AI enthusiasts\"\n    };\n  }\n};\n\nconst inputData = $input.first().json;\nconst result = await contentGenerator.generateContent(inputData.contentStrategy);\n\nreturn { json: { ...result, processingId: inputData.processingId } };"
      },
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [100, 200],
      "id": "lm-studio-generator",
      "name": "🤖 LM Studio Generator"
    },
    {
      "parameters": {
        "jsCode": "// IMAGE GENERATOR FOR THUMBNAILS\nconst imageGenerator = {\n  async generateThumbnail(thumbnailPrompt, videoTitle) {\n    // For now, we'll create a detailed prompt for external image generation\n    // You can integrate with Stable Diffusion API later\n    \n    const enhancedPrompt = `${thumbnailPrompt}\\n\\nAdditional requirements:\\n- High contrast colors\\n- Bold, readable text: \"${videoTitle}\"\\n- YouTube thumbnail format (1280x720)\\n- Eye-catching and clickable\\n- Professional quality\\n- Bright lighting`;\n    \n    return {\n      success: true,\n      thumbnailPrompt: enhancedPrompt,\n      imageUrl: null, // Will be generated externally\n      specifications: {\n        dimensions: '1280x720',\n        format: 'JPG',\n        quality: 'high',\n        style: 'youtube-thumbnail'\n      },\n      instructions: 'Use this prompt with Stable Diffusion or similar image generator'\n    };\n  }\n};\n\nconst inputData = $input.first().json;\nconst content = inputData.content;\n\nconst thumbnailResult = await imageGenerator.generateThumbnail(\n  content.thumbnailPrompt,\n  content.videoTitle\n);\n\nreturn {\n  json: {\n    ...inputData,\n    thumbnail: thumbnailResult,\n    readyForPublishing: true\n  }\n};"
      },
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [400, 200],
      "id": "thumbnail-generator",
      "name": "🎨 Thumbnail Generator"
    },
    {
      "parameters": {
        "respondWith": "json",
        "responseBody": "={{ $json }}"
      },
      "type": "n8n-nodes-base.respondToWebhook",
      "typeVersion": 1,
      "position": [700, 200],
      "id": "webhook-response",
      "name": "📤 Final Response"
    }
  ],
  "connections": {
    "⏰ Schedule Trigger": {
      "main": [
        [
          {
            "node": "🧠 Market Analyzer",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "🔗 Manual Trigger": {
      "main": [
        [
          {
            "node": "🧠 Market Analyzer",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "🧠 Market Analyzer": {
      "main": [
        [
          {
            "node": "⏰ Timing Gate",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "⏰ Timing Gate": {
      "main": [
        [
          {
            "node": "🤖 LM Studio Generator",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "🤖 LM Studio Generator": {
      "main": [
        [
          {
            "node": "🎨 Thumbnail Generator",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "🎨 Thumbnail Generator": {
      "main": [
        [
          {
            "node": "📤 Final Response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "pinData": {},
  "settings": {
    "executionOrder": "v1"
  },
  "staticData": null,
  "tags": ["LM Studio", "YouTube", "AI Automation"],
  "triggerCount": 2,
  "updatedAt": "2024-07-14T00:00:00.000Z",
  "versionId": "2.0.0"

{
  "name": "Advanced YouTube Integration System",
  "description": "Professional YouTube publishing with metadata optimization and error handling",
  "components": {
    "metadata_optimizer": {
      "name": "🎯 Metadata Optimizer",
      "type": "n8n-nodes-base.code",
      "code": "// ADVANCED METADATA OPTIMIZATION SYSTEM\n// Optimizes titles, descriptions, and tags for maximum discoverability\n\nconst metadataOptimizer = {\n  optimizeMetadata(content, analytics) {\n    const optimized = {\n      title: this.optimizeTitle(content.videoTitle, analytics),\n      description: this.optimizeDescription(content.videoDescription, content, analytics),\n      tags: this.optimizeTags(content.seoTags, content, analytics),\n      thumbnail: this.optimizeThumbnail(content.thumbnailPrompt, analytics),\n      category: this.selectOptimalCategory(content),\n      publishSettings: this.optimizePublishSettings(content, analytics)\n    };\n    \n    return optimized;\n  },\n  \n  optimizeTitle(originalTitle, analytics) {\n    // YouTube title optimization best practices\n    let optimizedTitle = originalTitle;\n    \n    // Ensure title is within optimal length (60 characters)\n    if (optimizedTitle.length > 60) {\n      optimizedTitle = optimizedTitle.substring(0, 57) + '...';\n    }\n    \n    // Add engagement hooks if missing\n    const hooks = ['🔥', '⚡', '💥', '🚀', '✨'];\n    if (!this.hasEngagementHook(optimizedTitle)) {\n      const randomHook = hooks[Math.floor(Math.random() * hooks.length)];\n      optimizedTitle = `${randomHook} ${optimizedTitle}`;\n    }\n    \n    // Capitalize important words\n    optimizedTitle = this.capitalizeTitle(optimizedTitle);\n    \n    return {\n      original: originalTitle,\n      optimized: optimizedTitle,\n      improvements: this.getTitleImprovements(originalTitle, optimizedTitle)\n    };\n  },\n  \n  hasEngagementHook(title) {\n    const hooks = ['🔥', '⚡', '💥', '🚀', '✨', 'SHOCKING', 'AMAZING', 'SECRET'];\n    return hooks.some(hook => title.includes(hook));\n  },\n  \n  capitalizeTitle(title) {\n    const exceptions = ['a', 'an', 'and', 'as', 'at', 'but', 'by', 'for', 'if', 'in', 'of', 'on', 'or', 'the', 'to', 'up'];\n    \n    return title.split(' ').map((word, index) => {\n      if (index === 0 || !exceptions.includes(word.toLowerCase())) {\n        return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();\n      }\n      return word.toLowerCase();\n    }).join(' ');\n  },\n  \n  optimizeDescription(originalDescription, content, analytics) {\n    const sections = [\n      this.createHookSection(content),\n      this.createContentSummary(originalDescription),\n      this.createTimestamps(content),\n      this.createEngagementSection(),\n      this.createHashtagSection(content.seoTags),\n      this.createCreditsSection()\n    ];\n    \n    const optimizedDescription = sections.filter(section => section).join('\\n\\n');\n    \n    return {\n      original: originalDescription,\n      optimized: optimizedDescription,\n      wordCount: optimizedDescription.split(' ').length,\n      characterCount: optimizedDescription.length\n    };\n  },\n  \n  createHookSection(content) {\n    return `🎯 ${content.hooks?.opening || 'Get ready for amazing content!'}`;\n  },\n  \n  createContentSummary(description) {\n    return `📝 WHAT YOU'LL LEARN:\\n${description}`;\n  },\n  \n  createTimestamps(content) {\n    if (!content.contentStructure) return null;\n    \n    const structure = content.contentStructure;\n    const timestamps = [];\n    \n    Object.entries(structure).forEach(([section, timing]) => {\n      const [start] = timing.split('-');\n      timestamps.push(`${start} - ${section.charAt(0).toUpperCase() + section.slice(1)}`);\n    });\n    \n    return `⏰ TIMESTAMPS:\\n${timestamps.join('\\n')}`;\n  },\n  \n  createEngagementSection() {\n    return `🔔 ENGAGE WITH US:\\n• LIKE if this helped you!\\n• SUBSCRIBE for more content!\\n• COMMENT your thoughts below!\\n• SHARE with friends who need this!`;\n  },\n  \n  createHashtagSection(tags) {\n    const hashtagString = tags.slice(0, 15).map(tag => `#${tag.replace(/\\s+/g, '')}`).join(' ');\n    return `🏷️ TAGS:\\n${hashtagString}`;\n  },\n  \n  createCreditsSection() {\n    return `🤖 Created with Advanced AI Automation\\n📊 Optimized for Maximum Engagement`;\n  },\n  \n  optimizeTags(originalTags, content, analytics) {\n    const optimizedTags = [...originalTags];\n    \n    // Add trending tags\n    const trendingTags = this.getTrendingTags(content);\n    optimizedTags.push(...trendingTags);\n    \n    // Add category-specific tags\n    const categoryTags = this.getCategoryTags(content);\n    optimizedTags.push(...categoryTags);\n    \n    // Add duration-specific tags\n    const durationTags = this.getDurationTags(content.finalDuration);\n    optimizedTags.push(...durationTags);\n    \n    // Remove duplicates and limit to 500 characters total\n    const uniqueTags = [...new Set(optimizedTags)];\n    const finalTags = this.limitTagsToCharacterCount(uniqueTags, 500);\n    \n    return {\n      original: originalTags,\n      optimized: finalTags,\n      added: finalTags.filter(tag => !originalTags.includes(tag)),\n      count: finalTags.length\n    };\n  },\n  \n  getTrendingTags(content) {\n    const currentMonth = new Date().toLocaleString('default', { month: 'long' });\n    const currentYear = new Date().getFullYear();\n    \n    return [\n      `${currentMonth}${currentYear}`,\n      'trending',\n      'viral',\n      'shorts',\n      'fyp',\n      'algorithm'\n    ];\n  },\n  \n  getCategoryTags(content) {\n    const contentType = content.contentStrategy?.contentType || 'entertainment';\n    \n    const categoryMap = {\n      educational: ['tutorial', 'howto', 'learn', 'education', 'tips', 'guide'],\n      entertainment: ['funny', 'entertainment', 'viral', 'trending', 'fun', 'comedy'],\n      storytelling: ['story', 'storytime', 'narrative', 'experience', 'life', 'personal']\n    };\n    \n    return categoryMap[contentType] || categoryMap.entertainment;\n  },\n  \n  getDurationTags(duration) {\n    if (duration <= 30) return ['shorts', 'quicktips', 'fast', 'instant'];\n    if (duration <= 60) return ['short', 'quick', 'minute', 'brief'];\n    return ['detailed', 'comprehensive', 'indepth', 'complete'];\n  },\n  \n  limitTagsToCharacterCount(tags, maxChars) {\n    let totalChars = 0;\n    const limitedTags = [];\n    \n    for (const tag of tags) {\n      if (totalChars + tag.length + 1 <= maxChars) { // +1 for comma\n        limitedTags.push(tag);\n        totalChars += tag.length + 1;\n      } else {\n        break;\n      }\n    }\n    \n    return limitedTags;\n  },\n  \n  selectOptimalCategory(content) {\n    const contentType = content.contentStrategy?.contentType || 'entertainment';\n    \n    const categoryMap = {\n      educational: '27', // Education\n      entertainment: '24', // Entertainment\n      storytelling: '22', // People & Blogs\n      howto: '26', // Howto & Style\n      tech: '28', // Science & Technology\n      gaming: '20', // Gaming\n      music: '10', // Music\n      sports: '17', // Sports\n      news: '25' // News & Politics\n    };\n    \n    return categoryMap[contentType] || '24';\n  },\n  \n  optimizePublishSettings(content, analytics) {\n    return {\n      privacyStatus: 'public',\n      publishAt: this.calculateOptimalPublishTime(analytics),\n      selfDeclaredMadeForKids: false,\n      embeddable: true,\n      publicStatsViewable: true,\n      categoryId: this.selectOptimalCategory(content),\n      defaultLanguage: 'en',\n      defaultAudioLanguage: 'en'\n    };\n  },\n  \n  calculateOptimalPublishTime(analytics) {\n    const now = new Date();\n    const optimalHours = [14, 17, 20]; // 2 PM, 5 PM, 8 PM\n    const currentHour = now.getHours();\n    \n    // Find next optimal time\n    let nextOptimalHour = optimalHours.find(hour => hour > currentHour);\n    \n    if (!nextOptimalHour) {\n      // If no optimal time today, use first optimal time tomorrow\n      nextOptimalHour = optimalHours[0];\n      now.setDate(now.getDate() + 1);\n    }\n    \n    now.setHours(nextOptimalHour, 0, 0, 0);\n    return now.toISOString();\n  },\n  \n  getTitleImprovements(original, optimized) {\n    const improvements = [];\n    \n    if (original.length > 60) improvements.push('Shortened to optimal length');\n    if (!this.hasEngagementHook(original)) improvements.push('Added engagement hook');\n    if (original !== this.capitalizeTitle(original)) improvements.push('Improved capitalization');\n    \n    return improvements;\n  }\n};\n\nconst contentData = $input.first().json;\nconst analytics = contentData.analytics || {};\n\nconst optimizedMetadata = metadataOptimizer.optimizeMetadata(contentData, analytics);\n\nreturn {\n  json: {\n    ...contentData,\n    optimizedMetadata,\n    readyForUpload: true,\n    uploadPayload: {\n      snippet: {\n        title: optimizedMetadata.title.optimized,\n        description: optimizedMetadata.description.optimized,\n        tags: optimizedMetadata.tags.optimized,\n        categoryId: optimizedMetadata.category,\n        defaultLanguage: 'en',\n        defaultAudioLanguage: 'en'\n      },\n      status: optimizedMetadata.publishSettings\n    }\n  }\n};"
    },
    "upload_manager": {
      "name": "📤 Upload Manager",
      "description": "Handles video upload with retry logic and error handling",
      "features": [\n        "Automatic retry on failure",\n        "Progress tracking",\n        "Error categorization",\n        "Fallback strategies",\n        "Upload optimization"\n      ]\n    },\n    "performance_tracker": {\n      "name": "📈 Performance Tracker",\n      "description": "Tracks video performance post-upload",\n      "metrics": [\n        "View count progression",\n        "Engagement rates",\n        "Click-through rates",\n        "Audience retention",\n        "Subscriber conversion"\n      ]\n    }\n  }\n}

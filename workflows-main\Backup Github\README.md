# Backup Automático de Workflows do n8n para GitHub

Este repositório contém o fluxo do **n8n** que automatiza o processo de backup dos seus workflows diretamente para o **GitHub**. Esse processo garante que você tenha um histórico de versões de cada workflow, permitindo restaurá-los facilmente em caso de perda ou corrompimento do servidor.

## Descrição

No vídeo [Como Fazer Backup de Workflows N8N no GitHub de Forma Segura e Automática](https://www.youtube.com/watch?v=czdE1XyOrQo), mostramos a importância de fazer backups regulares dos workflows do n8n e como configurá-los para serem enviados automaticamente ao GitHub, sem a necessidade de fazer upload manual. A solução permite que o backup seja feito de forma programada, armazenando cada versão em um repositório GitHub.

### O que você vai aprender:
- **Como criar backups automáticos** dos workflows do n8n.
- **Configurar o GitHub como repositório** para armazenar seus backups de forma segura e organizada.
- **Controlar versões** dos seus workflows para restaurar qualquer versão anterior de um fluxo.

### Link mencionado:
- 🔗 [Repositório de Workflows no GitHub](https://github.com/gabriel-g2n/workflows)

## Como usar este repositório

1. **Clonar o Repositório**

   Baixe o Json do fluxo escolhido e importe no N8N.